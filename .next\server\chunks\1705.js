"use strict";exports.id=1705,exports.ids=[1705],exports.modules={51705:(e,a,t)=>{t.d(a,{ActiveDaysService:()=>c,S3:()=>u,i7:()=>l,isUserPlanExpired:()=>p,mH:()=>d,nd:()=>y,updateUserActiveDays:()=>n});var r=t(33784),s=t(75535);let i={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},o={users:"users"};class c{static async calculateActiveDays(e){try{let a,t=await (0,s.x7)((0,s.H9)(r.db,o.users,e));if(!t.exists())return console.error(`User ${e} not found`),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let i=t.data(),l=i.joinedDate?.toDate()||new Date,n=i.lastActiveDayUpdate?.toDate(),d=i.activeDays||0,y=i.plan||"Trial",u=new Date,p=u.toDateString(),D=n?n.toDateString():null;if(console.log(`📅 Calculating active days for user ${e}:`),console.log(`   - Joined: ${l.toDateString()}`),console.log(`   - Current active days: ${d}`),console.log(`   - Last update: ${D||"Never"}`),console.log(`   - Today: ${p}`),console.log(`   - Plan: ${y}`),console.log(`   - Is new day: ${D!==p}`),D===p)return console.log(`✅ Already updated today for user ${e}`),{activeDays:d,shouldUpdate:!1,isNewDay:!1};if("Admin"===y)return console.log(`⏭️ Skipping active days increment for admin user ${e}`),await c.updateLastActiveDayUpdate(e),{activeDays:d,shouldUpdate:!1,isNewDay:!0};if(await c.isUserOnLeaveToday(e))return console.log(`🏖️ User ${e} is on leave today, not incrementing active days`),await c.updateLastActiveDayUpdate(e),{activeDays:d,shouldUpdate:!1,isNewDay:!0};return a="Trial"===y?Math.floor((u.getTime()-l.getTime())/864e5)+1:d+1,console.log(`📈 New active days calculated: ${d} → ${a}`),{activeDays:a,shouldUpdate:a!==d,isNewDay:!0}}catch(a){return console.error(`Error calculating active days for user ${e}:`,a),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(e){try{let a=await c.calculateActiveDays(e);if(a.shouldUpdate){let t=(0,s.H9)(r.db,o.users,e);await (0,s.mZ)(t,{[i.activeDays]:a.activeDays,[i.lastActiveDayUpdate]:s.Dc.now()}),console.log(`✅ Updated active days for user ${e}: ${a.activeDays}`)}else a.isNewDay&&await c.updateLastActiveDayUpdate(e);return a.activeDays}catch(a){throw console.error(`Error updating active days for user ${e}:`,a),a}}static async updateLastActiveDayUpdate(e){try{let a=(0,s.H9)(r.db,o.users,e);await (0,s.mZ)(a,{[i.lastActiveDayUpdate]:s.Dc.now()})}catch(a){console.error(`Error updating last active day timestamp for user ${e}:`,a)}}static async isUserOnLeaveToday(e){try{let{isUserOnLeave:a}=await t.e(7087).then(t.bind(t,87087));return await a(e,new Date)}catch(a){return console.error(`Error checking leave status for user ${e}:`,a),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let e=await (0,s.getDocs)((0,s.collection)(r.db,o.users)),a=0,t=0,i=0;for(let r of e.docs)try{a++;let e=await c.calculateActiveDays(r.id);(e.shouldUpdate||e.isNewDay)&&(await c.updateUserActiveDays(r.id),e.shouldUpdate&&t++)}catch(e){i++,console.error(`Error processing active days for user ${r.id}:`,e)}return console.log(`✅ Daily active days processing complete:`),console.log(`   - Processed: ${a} users`),console.log(`   - Updated: ${t} users`),console.log(`   - Errors: ${i} users`),{processed:a,updated:t,errors:i}}catch(e){throw console.error("Error in daily active days processing:",e),e}}static async getUserActiveDays(e){try{let a=await (0,s.x7)((0,s.H9)(r.db,o.users,e));if(!a.exists())return 0;return a.data().activeDays||0}catch(a){return console.error(`Error getting active days for user ${e}:`,a),0}}static async initializeActiveDaysForNewUser(e){try{let a=(0,s.H9)(r.db,o.users,e);await (0,s.mZ)(a,{[i.activeDays]:1,[i.lastActiveDayUpdate]:s.Dc.now()}),console.log(`✅ Initialized active days for new user ${e}: Day 1`)}catch(a){throw console.error(`Error initializing active days for user ${e}:`,a),a}}static async getActiveDaysDisplay(e){try{let a,t=await (0,s.x7)((0,s.H9)(r.db,o.users,e));if(!t.exists())return{current:0,total:2,displayText:"0/2"};let i=t.data(),c=i.plan||"Trial",l=i.activeDays||0;return a="Trial"===c?2:30,{current:l,total:a,displayText:`${l}/${a}`}}catch(a){return console.error(`Error getting active days display for user ${e}:`,a),{current:0,total:2,displayText:"0/2"}}}static async isUserPlanExpired(e){try{let a=await (0,s.x7)((0,s.H9)(r.db,o.users,e));if(!a.exists())return{expired:!0,reason:"User not found"};let t=a.data(),i=t.plan||"Trial",c=t.activeDays||0,l=t.planExpiry;if(console.log(`📅 Checking plan expiry for user ${e}:`,{plan:i,activeDays:c,hasPlanExpiry:!!l,planExpiryDate:l?l.toDate():null}),"Admin"===i){let a={expired:!1,activeDays:c};return console.log(`📅 Plan expiry result for admin user ${e}:`,a),a}if("Trial"===i){let a=Math.max(0,2-c),t={expired:a<=0,reason:a<=0?"Trial period expired":void 0,daysLeft:a,activeDays:c};return console.log(`📅 Plan expiry result for trial user ${e}:`,t),t}if(l){let a=new Date,t=l.toDate(),r=a>t,s=r?0:Math.ceil((t.getTime()-a.getTime())/864e5),i={expired:r,reason:r?"Plan subscription expired":void 0,daysLeft:s,activeDays:c};return console.log(`📅 Plan expiry result for user ${e} (using planExpiry field):`,i),i}let n=Math.max(0,30-c),d=c>30,y={expired:d,reason:d?`Plan expired - You have used ${c} days out of 30 allowed days`:void 0,daysLeft:n,activeDays:c};return console.log(`📅 Plan expiry result for user ${e}:`,y),y}catch(a){return console.error(`Error checking plan expiry for user ${e}:`,a),{expired:!0,reason:"Error checking plan status"}}}static async setPlanExpiryForUser(e,a=30){try{let t=(0,s.H9)(r.db,o.users,e),i=await (0,s.x7)(t);if(!i.exists())return console.error(`User ${e} not found`),!1;let c=i.data(),l=c.plan||"Trial";if("Trial"===l||"Admin"===l)return console.log(`Skipping plan expiry setup for ${l} user: ${e}`),!1;if(c.planExpiry)return console.log(`User ${e} already has plan expiry set: ${c.planExpiry.toDate()}`),!1;let n=c.joinedDate?.toDate()||new Date,d=new Date(n);return d.setDate(d.getDate()+a),await (0,s.mZ)(t,{planExpiry:s.Dc.fromDate(d),planExpirySetDate:s.Dc.now()}),console.log(`✅ Set plan expiry for user ${e}: ${d}`),!0}catch(a){return console.error(`Error setting plan expiry for user ${e}:`,a),!1}}static async forceUpdateActiveDays(e,a,t){try{let c=(0,s.H9)(r.db,o.users,e);await (0,s.mZ)(c,{[i.activeDays]:a,[i.lastActiveDayUpdate]:s.Dc.now()}),console.log(`🔧 Admin ${t} force updated active days for user ${e}: ${a}`)}catch(a){throw console.error(`Error force updating active days for user ${e}:`,a),a}}static async getActiveDaysStatistics(){try{let e=await (0,s.getDocs)((0,s.collection)(r.db,o.users)),a=0,t=0,i=0,c=0,l=0,n=0,d=new Date().toDateString();for(let r of e.docs){let e=r.data(),s=e.plan||"Trial",o=e.activeDays||0,y=e.lastActiveDayUpdate?.toDate();a++,l+=o,"Trial"===s?t++:"Admin"===s?c++:i++,y&&y.toDateString()===d&&n++}return{totalUsers:a,trialUsers:t,paidUsers:i,adminUsers:c,averageActiveDays:a>0?Math.round(l/a*100)/100:0,usersUpdatedToday:n}}catch(e){throw console.error("Error getting active days statistics:",e),e}}}let l=c.calculateActiveDays,n=c.updateUserActiveDays,d=c.processAllUsersActiveDays,y=c.getUserActiveDays,u=c.initializeActiveDaysForNewUser;c.getActiveDaysDisplay;let p=c.isUserPlanExpired;c.forceUpdateActiveDays,c.getActiveDaysStatistics}};