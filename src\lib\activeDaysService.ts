import { db } from './firebase'
import { 
  doc, 
  updateDoc, 
  getDoc, 
  collection, 
  getDocs, 
  query, 
  where,
  Timestamp 
} from 'firebase/firestore'

// Field names and collections
const FIELD_NAMES = {
  activeDays: 'activeDays',
  lastActiveDayUpdate: 'lastActiveDayUpdate',
  joinedDate: 'joinedDate',
  plan: 'plan',
  name: 'name',
  email: 'email'
}

const COLLECTIONS = {
  users: 'users'
}

/**
 * UNIFIED ACTIVE DAYS SERVICE
 *
 * This is the SINGLE source of truth for all active days calculations.
 * All pages, admin functions, and services should use this service.
 *
 * Key Features:
 * - Ensures active days increment by 1 only once per day
 * - Handles all user types: Trial, Paid Plans, Admin
 * - Respects user leave days
 * - Provides comprehensive logging and error handling
 * - Thread-safe with timestamp-based deduplication
 */
class ActiveDaysService {
  
  /**
   * Calculate active days for a user based on their registration and leave history
   */
  static async calculateActiveDays(userId: string): Promise<{
    activeDays: number
    shouldUpdate: boolean
    isNewDay: boolean
  }> {
    try {
      const userDoc = await getDoc(doc(db, COLLECTIONS.users, userId))
      if (!userDoc.exists()) {
        console.error(`User ${userId} not found`)
        return { activeDays: 0, shouldUpdate: false, isNewDay: false }
      }

      const userData = userDoc.data()
      const joinedDate = userData.joinedDate?.toDate() || new Date()
      const lastUpdate = userData.lastActiveDayUpdate?.toDate()
      const currentActiveDays = userData.activeDays || 0
      const plan = userData.plan || 'Trial'
      
      const today = new Date()
      const todayString = today.toDateString()
      const lastUpdateString = lastUpdate ? lastUpdate.toDateString() : null

      console.log(`📅 Calculating active days for user ${userId}:`)
      console.log(`   - Joined: ${joinedDate.toDateString()}`)
      console.log(`   - Current active days: ${currentActiveDays}`)
      console.log(`   - Last update: ${lastUpdateString || 'Never'}`)
      console.log(`   - Today: ${todayString}`)
      console.log(`   - Plan: ${plan}`)
      console.log(`   - Is new day: ${lastUpdateString !== todayString}`)

      // Check if it's a new day
      const isNewDay = lastUpdateString !== todayString

      if (!isNewDay) {
        console.log(`✅ Already updated today for user ${userId}`)
        return { activeDays: currentActiveDays, shouldUpdate: false, isNewDay: false }
      }

      // Skip active days increment for admins
      if (plan === 'Admin') {
        console.log(`⏭️ Skipping active days increment for admin user ${userId}`)
        await ActiveDaysService.updateLastActiveDayUpdate(userId)
        return { activeDays: currentActiveDays, shouldUpdate: false, isNewDay: true }
      }

      // Check if user is on leave today
      const isOnLeave = await ActiveDaysService.isUserOnLeaveToday(userId)
      if (isOnLeave) {
        console.log(`🏖️ User ${userId} is on leave today, not incrementing active days`)
        await ActiveDaysService.updateLastActiveDayUpdate(userId)
        return { activeDays: currentActiveDays, shouldUpdate: false, isNewDay: true }
      }

      // Calculate new active days
      let newActiveDays: number

      if (plan === 'Trial') {
        // For trial users: calculate based on days since joining
        const daysSinceJoining = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24))
        newActiveDays = daysSinceJoining + 1 // Day 1 starts on registration day
      } else {
        // For paid plans: increment from current active days
        newActiveDays = currentActiveDays + 1
      }

      console.log(`📈 New active days calculated: ${currentActiveDays} → ${newActiveDays}`)

      return { 
        activeDays: newActiveDays, 
        shouldUpdate: newActiveDays !== currentActiveDays, 
        isNewDay: true 
      }

    } catch (error) {
      console.error(`Error calculating active days for user ${userId}:`, error)
      return { activeDays: 0, shouldUpdate: false, isNewDay: false }
    }
  }

  /**
   * Update user's active days
   */
  static async updateUserActiveDays(userId: string): Promise<number> {
    try {
      const calculation = await ActiveDaysService.calculateActiveDays(userId)

      if (calculation.shouldUpdate) {
        const userRef = doc(db, COLLECTIONS.users, userId)
        await updateDoc(userRef, {
          [FIELD_NAMES.activeDays]: calculation.activeDays,
          [FIELD_NAMES.lastActiveDayUpdate]: Timestamp.now()
        })

        console.log(`✅ Updated active days for user ${userId}: ${calculation.activeDays}`)
      } else if (calculation.isNewDay) {
        // Update timestamp even if active days didn't change
        await ActiveDaysService.updateLastActiveDayUpdate(userId)
      }

      return calculation.activeDays
    } catch (error) {
      console.error(`Error updating active days for user ${userId}:`, error)
      throw error
    }
  }

  /**
   * Update only the last active day update timestamp
   */
  private static async updateLastActiveDayUpdate(userId: string): Promise<void> {
    try {
      const userRef = doc(db, COLLECTIONS.users, userId)
      await updateDoc(userRef, {
        [FIELD_NAMES.lastActiveDayUpdate]: Timestamp.now()
      })
    } catch (error) {
      console.error(`Error updating last active day timestamp for user ${userId}:`, error)
    }
  }

  /**
   * Check if user is on leave today
   */
  private static async isUserOnLeaveToday(userId: string): Promise<boolean> {
    try {
      const { isUserOnLeave } = await import('./leaveService')
      return await isUserOnLeave(userId, new Date())
    } catch (error) {
      console.error(`Error checking leave status for user ${userId}:`, error)
      return false // Default to not on leave to avoid blocking work
    }
  }

  /**
   * Process all users' active days (daily scheduler)
   */
  static async processAllUsersActiveDays(): Promise<{
    processed: number
    updated: number
    errors: number
  }> {
    try {
      console.log('🔄 Starting daily active days processing for all users...')
      
      const usersSnapshot = await getDocs(collection(db, COLLECTIONS.users))
      let processed = 0
      let updated = 0
      let errors = 0

      for (const userDoc of usersSnapshot.docs) {
        try {
          processed++
          const calculation = await ActiveDaysService.calculateActiveDays(userDoc.id)

          if (calculation.shouldUpdate || calculation.isNewDay) {
            await ActiveDaysService.updateUserActiveDays(userDoc.id)
            if (calculation.shouldUpdate) updated++
          }
        } catch (error) {
          errors++
          console.error(`Error processing active days for user ${userDoc.id}:`, error)
        }
      }

      console.log(`✅ Daily active days processing complete:`)
      console.log(`   - Processed: ${processed} users`)
      console.log(`   - Updated: ${updated} users`)
      console.log(`   - Errors: ${errors} users`)

      return { processed, updated, errors }
    } catch (error) {
      console.error('Error in daily active days processing:', error)
      throw error
    }
  }

  /**
   * Get active days for a user (read-only)
   */
  static async getUserActiveDays(userId: string): Promise<number> {
    try {
      const userDoc = await getDoc(doc(db, COLLECTIONS.users, userId))
      if (!userDoc.exists()) {
        return 0
      }
      
      const userData = userDoc.data()
      return userData.activeDays || 0
    } catch (error) {
      console.error(`Error getting active days for user ${userId}:`, error)
      return 0
    }
  }

  /**
   * Initialize active days for new user (called during registration)
   */
  static async initializeActiveDaysForNewUser(userId: string): Promise<void> {
    try {
      const userRef = doc(db, COLLECTIONS.users, userId)
      await updateDoc(userRef, {
        [FIELD_NAMES.activeDays]: 1, // Start with day 1
        [FIELD_NAMES.lastActiveDayUpdate]: Timestamp.now()
      })

      console.log(`✅ Initialized active days for new user ${userId}: Day 1`)
    } catch (error) {
      console.error(`Error initializing active days for user ${userId}:`, error)
      throw error
    }
  }

  /**
   * Get active days display format for UI (current/total)
   */
  static async getActiveDaysDisplay(userId: string): Promise<{
    current: number
    total: number
    displayText: string
  }> {
    try {
      const userDoc = await getDoc(doc(db, COLLECTIONS.users, userId))
      if (!userDoc.exists()) {
        return { current: 0, total: 2, displayText: '0/2' }
      }

      const userData = userDoc.data()
      const plan = userData.plan || 'Trial'
      const activeDays = userData.activeDays || 0

      let total: number
      if (plan === 'Trial') {
        total = 2
      } else {
        total = 30 // Standard plan duration (expires on 31st day)
      }

      return {
        current: activeDays,
        total,
        displayText: `${activeDays}/${total}`
      }
    } catch (error) {
      console.error(`Error getting active days display for user ${userId}:`, error)
      return { current: 0, total: 2, displayText: '0/2' }
    }
  }

  /**
   * Check if user's plan is expired based on active days
   */
  static async isUserPlanExpired(userId: string): Promise<{
    expired: boolean
    reason?: string
    daysLeft?: number
    activeDays?: number
  }> {
    try {
      const userDoc = await getDoc(doc(db, COLLECTIONS.users, userId))
      if (!userDoc.exists()) {
        return { expired: true, reason: 'User not found' }
      }

      const userData = userDoc.data()
      const plan = userData.plan || 'Trial'
      const activeDays = userData.activeDays || 0
      const planExpiry = userData.planExpiry

      console.log(`📅 Checking plan expiry for user ${userId}:`, {
        plan,
        activeDays,
        hasPlanExpiry: !!planExpiry,
        planExpiryDate: planExpiry ? planExpiry.toDate() : null
      })

      if (plan === 'Admin') {
        const result = { expired: false, activeDays }
        console.log(`📅 Plan expiry result for admin user ${userId}:`, result)
        return result
      }

      if (plan === 'Trial') {
        const daysLeft = Math.max(0, 2 - activeDays)
        const result = {
          expired: daysLeft <= 0,
          reason: daysLeft <= 0 ? 'Trial period expired' : undefined,
          daysLeft,
          activeDays
        }
        console.log(`📅 Plan expiry result for trial user ${userId}:`, result)
        return result
      }

      // For paid plans, check if planExpiry is set
      if (planExpiry) {
        const today = new Date()
        const expiryDate = planExpiry.toDate()
        const expired = today > expiryDate
        const daysLeft = expired ? 0 : Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

        const result = {
          expired,
          reason: expired ? 'Plan subscription expired' : undefined,
          daysLeft,
          activeDays
        }
        console.log(`📅 Plan expiry result for user ${userId} (using planExpiry field):`, result)
        return result
      }

      // If planExpiry field is empty, use active days to determine expiry
      // Paid plans (Junior, Senior, Expert) expire after 30 active days
      const daysLeft = Math.max(0, 30 - activeDays)
      const expired = activeDays > 30 // Expire if user has more than 30 active days

      const result = {
        expired,
        reason: expired ? `Plan expired - You have used ${activeDays} days out of 30 allowed days` : undefined,
        daysLeft,
        activeDays
      }

      console.log(`📅 Plan expiry result for user ${userId}:`, result)
      return result
    } catch (error) {
      console.error(`Error checking plan expiry for user ${userId}:`, error)
      return { expired: true, reason: 'Error checking plan status' }
    }
  }

  /**
   * Set plan expiry date for users who don't have it set
   * This is useful for migrating existing users
   */
  static async setPlanExpiryForUser(userId: string, planDurationDays: number = 30): Promise<boolean> {
    try {
      const userDocRef = doc(db, COLLECTIONS.users, userId)
      const userDoc = await getDoc(userDocRef)

      if (!userDoc.exists()) {
        console.error(`User ${userId} not found`)
        return false
      }

      const userData = userDoc.data()
      const plan = userData.plan || 'Trial'

      // Don't set expiry for Trial or Admin users
      if (plan === 'Trial' || plan === 'Admin') {
        console.log(`Skipping plan expiry setup for ${plan} user: ${userId}`)
        return false
      }

      // Don't overwrite existing planExpiry
      if (userData.planExpiry) {
        console.log(`User ${userId} already has plan expiry set: ${userData.planExpiry.toDate()}`)
        return false
      }

      // Calculate expiry date based on join date + plan duration
      const joinedDate = userData.joinedDate?.toDate() || new Date()
      const expiryDate = new Date(joinedDate)
      expiryDate.setDate(expiryDate.getDate() + planDurationDays)

      // Update user document with plan expiry
      await updateDoc(userDocRef, {
        planExpiry: Timestamp.fromDate(expiryDate),
        planExpirySetDate: Timestamp.now()
      })

      console.log(`✅ Set plan expiry for user ${userId}: ${expiryDate}`)
      return true
    } catch (error) {
      console.error(`Error setting plan expiry for user ${userId}:`, error)
      return false
    }
  }

  /**
   * Force update active days for a specific user (admin use only)
   */
  static async forceUpdateActiveDays(userId: string, newActiveDays: number, adminId: string): Promise<void> {
    try {
      const userRef = doc(db, COLLECTIONS.users, userId)
      await updateDoc(userRef, {
        [FIELD_NAMES.activeDays]: newActiveDays,
        [FIELD_NAMES.lastActiveDayUpdate]: Timestamp.now()
      })

      console.log(`🔧 Admin ${adminId} force updated active days for user ${userId}: ${newActiveDays}`)
    } catch (error) {
      console.error(`Error force updating active days for user ${userId}:`, error)
      throw error
    }
  }

  /**
   * Get comprehensive active days statistics for admin dashboard
   */
  static async getActiveDaysStatistics(): Promise<{
    totalUsers: number
    trialUsers: number
    paidUsers: number
    adminUsers: number
    averageActiveDays: number
    usersUpdatedToday: number
  }> {
    try {
      const usersSnapshot = await getDocs(collection(db, COLLECTIONS.users))
      let totalUsers = 0
      let trialUsers = 0
      let paidUsers = 0
      let adminUsers = 0
      let totalActiveDays = 0
      let usersUpdatedToday = 0

      const today = new Date().toDateString()

      for (const userDoc of usersSnapshot.docs) {
        const userData = userDoc.data()
        const plan = userData.plan || 'Trial'
        const activeDays = userData.activeDays || 0
        const lastUpdate = userData.lastActiveDayUpdate?.toDate()

        totalUsers++
        totalActiveDays += activeDays

        if (plan === 'Trial') trialUsers++
        else if (plan === 'Admin') adminUsers++
        else paidUsers++

        if (lastUpdate && lastUpdate.toDateString() === today) {
          usersUpdatedToday++
        }
      }

      return {
        totalUsers,
        trialUsers,
        paidUsers,
        adminUsers,
        averageActiveDays: totalUsers > 0 ? Math.round(totalActiveDays / totalUsers * 100) / 100 : 0,
        usersUpdatedToday
      }
    } catch (error) {
      console.error('Error getting active days statistics:', error)
      throw error
    }
  }
}

// Export convenience functions for backward compatibility
export const calculateActiveDays = ActiveDaysService.calculateActiveDays
export const updateUserActiveDays = ActiveDaysService.updateUserActiveDays
export const processAllUsersActiveDays = ActiveDaysService.processAllUsersActiveDays
export const getUserActiveDays = ActiveDaysService.getUserActiveDays
export const initializeActiveDaysForNewUser = ActiveDaysService.initializeActiveDaysForNewUser

// Export new unified functions
export const getActiveDaysDisplay = ActiveDaysService.getActiveDaysDisplay
export const isUserPlanExpired = ActiveDaysService.isUserPlanExpired
export const forceUpdateActiveDays = ActiveDaysService.forceUpdateActiveDays
export const getActiveDaysStatistics = ActiveDaysService.getActiveDaysStatistics

// Export the main class for direct usage (recommended)
export { ActiveDaysService }
