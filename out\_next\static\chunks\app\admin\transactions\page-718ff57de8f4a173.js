(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4426],{216:(e,t,a)=>{Promise.resolve().then(a.bind(a,6740))},3737:(e,t,a)=>{"use strict";function s(e,t,a){if(!e||0===e.length)return void alert("No data to export");let s=a||Object.keys(e[0]),r=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],n=new Blob(["\uFEFF"+[s.join(","),...e.map(e=>s.map(t=>{let a=e[t];if(null==a)return"";let s=r.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return'"'.concat(e,'"')}return a instanceof Date?'"'.concat(a.toLocaleDateString(),'"'):"object"==typeof a&&null!==a&&a.toDate?'"'.concat(a.toDate().toLocaleDateString(),'"'):s&&("number"==typeof a||!isNaN(Number(a)))?'"'.concat(a,'"'):"number"==typeof a?a.toString():'"'.concat(String(a),'"')}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(n);i.setAttribute("href",e),i.setAttribute("download","".concat(t,"_").concat(new Date().toISOString().split("T")[0],".csv")),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function r(e){return e.map(t=>{let a=0,s=null,r="No",n=t.quickTranslationAdvantageExpiry||t.quickVideoAdvantageExpiry;if(n)try{n instanceof Date?s=n:n.toDate&&"function"==typeof n.toDate?s=n.toDate():s=new Date(n);let i=new Date,l=s.getTime()-i.getTime();r=(a=Math.max(0,Math.ceil(l/864e5)))>0?"Yes":"No",5>e.indexOf(t)&&console.log("\uD83D\uDCCA Export debug for user ".concat(t.email,":"),{expiryField:n,expiryFieldType:typeof n,copyPasteExpiryDate:s,copyPasteRemainingDays:a,copyPastePermission:r,hasQuickTranslationAdvantageExpiry:!!t.quickTranslationAdvantageExpiry,hasQuickVideoAdvantageExpiry:!!t.quickVideoAdvantageExpiry})}catch(e){console.error("❌ Error calculating copy-paste days for user ".concat(t.email,":"),e)}else 5>e.indexOf(t)&&console.log("\uD83D\uDCCA Export debug for user ".concat(t.email,": No copy-paste expiry field found"));return{"User ID":t.id||"",Name:t.name||"",Email:t.email||"",Mobile:String(t.mobile||""),"Referral Code":t.referralCode||"","Referred By":t.referredBy||"Direct","Referrals Count":t.referralCount||0,Plan:t.plan||"","Plan Expiry":t.planExpiry instanceof Date?t.planExpiry.toLocaleDateString():t.planExpiry?new Date(t.planExpiry).toLocaleDateString():"","Active Days":t.activeDays||0,"Total Translations":t.totalTranslations||t.totalVideos||0,"Today Translations":t.todayTranslations||t.todayVideos||0,"Last Translation Date":t.lastTranslationDate instanceof Date?t.lastTranslationDate.toLocaleDateString():t.lastTranslationDate?new Date(t.lastTranslationDate).toLocaleDateString():t.lastVideoDate instanceof Date?t.lastVideoDate.toLocaleDateString():t.lastVideoDate?new Date(t.lastVideoDate).toLocaleDateString():"","Copy-Paste Permission":r,"Copy-Paste Remaining Days":a,"Copy-Paste Expiry":s?s.toLocaleDateString():"","Copy-Paste Granted By":t.quickTranslationAdvantageGrantedBy||t.quickVideoAdvantageGrantedBy||"","Copy-Paste Granted At":t.quickTranslationAdvantageGrantedAt?t.quickTranslationAdvantageGrantedAt instanceof Date?t.quickTranslationAdvantageGrantedAt.toLocaleDateString():new Date(t.quickTranslationAdvantageGrantedAt).toLocaleDateString():"","Wallet Balance":t.wallet||0,"Referral Bonus Credited":t.referralBonusCredited?"Yes":"No",Status:t.status||"","Joined Date":t.joinedDate instanceof Date?t.joinedDate.toLocaleDateString():t.joinedDate?new Date(t.joinedDate).toLocaleDateString():"","Joined Time":t.joinedDate instanceof Date?t.joinedDate.toLocaleTimeString():t.joinedDate?new Date(t.joinedDate).toLocaleTimeString():""}})}function n(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function i(e){return e.map(e=>{var t,a,s,r;return{"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":(null==(t=e.bankDetails)?void 0:t.accountHolderName)||"","Bank Name":(null==(a=e.bankDetails)?void 0:a.bankName)||"","Account Number":String((null==(s=e.bankDetails)?void 0:s.accountNumber)||""),"IFSC Code":(null==(r=e.bankDetails)?void 0:r.ifscCode)||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}})}function l(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>s,Fz:()=>r,Pe:()=>l,dB:()=>i,sL:()=>n})},5664:(e,t,a)=>{"use strict";a.d(t,{A:()=>n,W:()=>i});var s=a(5155),r=a(2115);function n(e){let{currentPage:t,totalPages:a,totalItems:n,itemsPerPage:i,onPageChange:l,onItemsPerPageChange:o,showItemsPerPage:c=!0,className:d=""}=e,u=Math.min(t*i,n),m=a>1?(()=>{let e=[],s=[];for(let s=Math.max(2,t-2);s<=Math.min(a-1,t+2);s++)e.push(s);return t-2>2?s.push(1,"..."):s.push(1),s.push(...e),t+2<a-1?s.push("...",a):s.push(a),s})():[];return(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 ".concat(d),children:[c&&(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Show:"}),(0,s.jsxs)("select",{value:i,onChange:e=>o(Number(e.target.value)),className:"px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:10,children:"10"}),(0,s.jsx)("option",{value:25,children:"25"}),(0,s.jsx)("option",{value:50,children:"50"}),(0,s.jsx)("option",{value:100,children:"100"})]}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"per page"})]}),(0,s.jsx)("div",{className:"text-sm text-gray-700",children:n>0?(0,s.jsxs)(s.Fragment,{children:["Showing ",(0,s.jsx)("span",{className:"font-medium",children:(t-1)*i+1})," to"," ",(0,s.jsx)("span",{className:"font-medium",children:u})," of"," ",(0,s.jsx)("span",{className:"font-medium",children:n})," results"]}):"No results found"}),a>1&&(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)("button",{onClick:()=>l(t-1),disabled:1===t,className:"px-3 py-2 text-sm font-medium rounded-md ".concat(1===t?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:text-gray-900 hover:bg-gray-100"),children:(0,s.jsx)("i",{className:"fas fa-chevron-left"})}),m.map((e,a)=>(0,s.jsx)(r.Fragment,{children:"..."===e?(0,s.jsx)("span",{className:"px-3 py-2 text-sm text-gray-500",children:"..."}):(0,s.jsx)("button",{onClick:()=>l(e),className:"px-3 py-2 text-sm font-medium rounded-md ".concat(t===e?"bg-blue-600 text-white":"text-gray-700 hover:text-gray-900 hover:bg-gray-100"),children:e})},a)),(0,s.jsx)("button",{onClick:()=>l(t+1),disabled:t===a,className:"px-3 py-2 text-sm font-medium rounded-md ".concat(t===a?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:text-gray-900 hover:bg-gray-100"),children:(0,s.jsx)("i",{className:"fas fa-chevron-right"})})]})]})}function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:25,[a,s]=r.useState(1),[n,i]=r.useState(t),l=Math.ceil(e.length/n),o=(a-1)*n,c=e.slice(o,o+n);return r.useEffect(()=>{s(1)},[e.length]),r.useEffect(()=>{a>l&&l>0&&s(l)},[l,a]),{currentPage:a,setCurrentPage:s,itemsPerPage:n,setItemsPerPage:e=>{i(e),s(1)},totalPages:l,currentItems:c,totalItems:e.length}}},6740:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var s=a(5155),r=a(2115),n=a(6874),i=a.n(n),l=a(6681),o=a(5317),c=a(6104),d=a(3592),u=a(3737),m=a(5664),x=a(4752),p=a.n(x);function g(){let{user:e,loading:t,isAdmin:a}=(0,l.wC)(),[n,x]=(0,r.useState)([]),[g,h]=(0,r.useState)(!0),[f,y]=(0,r.useState)(""),[b,D]=(0,r.useState)(""),[N,j]=(0,r.useState)("");(0,r.useEffect)(()=>{a&&v()},[a]);let v=async()=>{try{h(!0);let t=(0,o.P)((0,o.collection)(c.db,d.COLLECTIONS.transactions),(0,o.My)("date","desc"),(0,o.AB)(500)),a=await (0,o.getDocs)(t),s=[];for(let t of a.docs){var e;let a=t.data(),r="Unknown User",n="<EMAIL>",i="Unknown Mobile";try{let e=await (0,o.getDocs)((0,o.P)((0,o.collection)(c.db,d.COLLECTIONS.users),(0,o._M)("__name__","==",a.userId)));if(!e.empty){let t=e.docs[0].data();r=t.name||"Unknown User",n=t.email||"<EMAIL>",i=t.mobile||"Unknown Mobile"}}catch(e){console.error("Error fetching user data:",e)}s.push({id:t.id,userId:a.userId,userName:r,userEmail:n,userMobile:i,type:a.type,amount:a.amount,description:a.description,date:(null==(e=a.date)?void 0:e.toDate())||new Date,status:a.status||"completed"})}console.log("\uD83D\uDCCA Admin transactions loaded: ".concat(s.length," total"));let r=s.reduce((e,t)=>(e[t.type]=(e[t.type]||0)+1,e),{});console.log("\uD83D\uDCCA Transaction type breakdown:",r);let n=s.filter(e=>"translation_earning"===e.type||"video_earning"===e.type);console.log("\uD83D\uDCCA Translation earnings found: ".concat(n.length)),n.length>0&&console.log("\uD83D\uDCCA Sample translation earnings:",n.slice(0,3)),x(s)}catch(e){console.error("Error loading transactions:",e),p().fire({icon:"error",title:"Error",text:"Failed to load transactions. Please try again."})}finally{h(!1)}},w=n.filter(e=>{let t=!f||e.type===f,a=!b||e.status===b,s=!N||String(e.userName||"").toLowerCase().includes(N.toLowerCase())||String(e.userEmail||"").toLowerCase().includes(N.toLowerCase())||String(e.userMobile||"").toLowerCase().includes(N.toLowerCase())||String(e.description||"").toLowerCase().includes(N.toLowerCase());return t&&a&&s}),{currentPage:C,setCurrentPage:S,itemsPerPage:k,setItemsPerPage:A,totalPages:L,currentItems:T,totalItems:E}=(0,m.W)(w,25),P=e=>null==e||isNaN(e)?"₹0.00":new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}).format(e),_=e=>{switch(e){case"translation_earning":case"video_earning":return"Translation Earning";case"withdrawal":return"Withdrawal";case"withdrawal_request":return"Withdrawal Request";case"bonus":return"Bonus";case"referral":return"Referral";case"referral_bonus":return"Referral Bonus";case"quick_advantage_removed":return"Copy-Paste Removed";default:return e.charAt(0).toUpperCase()+e.slice(1).replace(/_/g," ")}},q=e=>{switch(e){case"translation_earning":case"video_earning":return"fas fa-language text-green-500";case"withdrawal":return"fas fa-download text-red-500";case"withdrawal_request":return"fas fa-money-bill-wave text-orange-500";case"bonus":return"fas fa-gift text-yellow-500";case"referral":return"fas fa-users text-blue-500";case"referral_bonus":return"fas fa-user-plus text-purple-500";case"quick_advantage_removed":return"fas fa-times-circle text-red-500";default:return"fas fa-exchange-alt text-gray-500"}};return t||g?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading transactions..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(i(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Transactions"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-gray-700",children:["Total: ",E," | Page ",C," of ",L]}),(0,s.jsxs)("button",{onClick:()=>{if(0===w.length)return void p().fire({icon:"warning",title:"No Data",text:"No transactions to export."});let e=(0,u.sL)(w);(0,u.Bf)(e,"transactions"),p().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(w.length," transactions to CSV file."),timer:2e3,showConfirmButton:!1})},className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,s.jsxs)("button",{onClick:v,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),!1,(0,s.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search"}),(0,s.jsx)("input",{type:"text",value:N,onChange:e=>j(e.target.value),placeholder:"Search user, email, mobile, or description...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,s.jsxs)("select",{value:f,onChange:e=>y(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"All Types"}),(0,s.jsx)("option",{value:"translation_earning",children:"Translation Earning"}),(0,s.jsx)("option",{value:"withdrawal",children:"Withdrawal"}),(0,s.jsx)("option",{value:"withdrawal_request",children:"Withdrawal Request"}),(0,s.jsx)("option",{value:"bonus",children:"Bonus"}),(0,s.jsx)("option",{value:"referral",children:"Referral"}),(0,s.jsx)("option",{value:"referral_bonus",children:"Referral Bonus"}),(0,s.jsx)("option",{value:"quick_advantage_removed",children:"Copy-Paste Removed"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,s.jsxs)("select",{value:b,onChange:e=>D(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"All Status"}),(0,s.jsx)("option",{value:"completed",children:"Completed"}),(0,s.jsx)("option",{value:"pending",children:"Pending"}),(0,s.jsx)("option",{value:"failed",children:"Failed"}),(0,s.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsx)("button",{onClick:()=>{y(""),D(""),j("")},className:"w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:"Clear Filters"})})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Mobile"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"})]})}),(0,s.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[T.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.userName}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.userEmail})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.userMobile})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"".concat(q(e.type)," mr-2")}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:_(e.type)})]})}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.description})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("span",{className:"text-sm font-medium ".concat(e.amount>0?"text-green-600":"text-red-600"),children:[e.amount>0?"+":"",P(e.amount)]})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[e.date.toLocaleDateString()," ",e.date.toLocaleTimeString()]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("completed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"failed"===e.status?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})})]},e.id)),0===T.length&&(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:7,className:"px-6 py-12 text-center",children:(0,s.jsxs)("div",{className:"text-gray-500",children:[(0,s.jsx)("i",{className:"fas fa-receipt text-4xl mb-4"}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"No transactions found"}),(0,s.jsx)("p",{className:"text-sm",children:0===w.length?"No transactions match your current filters.":"No transactions on this page."})]})})})]})]})}),E>0&&(0,s.jsx)("div",{className:"px-6 py-4 border-t border-gray-200",children:(0,s.jsx)(m.A,{currentPage:C,totalPages:L,totalItems:E,itemsPerPage:k,onPageChange:S,onItemsPerPageChange:A,className:"justify-between"})})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6874,6681,3592,8441,1684,7358],()=>t(216)),_N_E=e.O()}]);