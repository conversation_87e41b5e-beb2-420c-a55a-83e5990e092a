"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6104,6681],{12:(e,t,s)=>{s.d(t,{M4:()=>l,_f:()=>a});var o=s(6104),r=s(4752),n=s.n(r);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await n().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await o.auth.signOut(),n().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),n().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&i(e),await o.auth.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},5738:(e,t,s)=>{s.d(t,{i:()=>i});var o=s(6104),r=s(3004);class n{static getInstance(){return n.instance||(n.instance=new n),n.instance}initializeSession(){let e="session_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9));this.sessionData={user:null,lastActivity:new Date,isOnline:"undefined"==typeof navigator||navigator.onLine,sessionId:e},this.setupAuthStateListener(),this.setupNetworkListeners(),this.setupPeriodicSave(),this.setupHeartbeat(),this.restoreSession(),console.log("Session Manager initialized with ID:",e)}setupAuthStateListener(){this.authStateListener=(0,r.hg)(o.auth,e=>{console.log("Auth state changed:",e?"User ".concat(e.uid):"No user"),this.sessionData&&(this.sessionData.user=e,this.sessionData.lastActivity=new Date,e?this.restoreWorkProgress(e.uid):this.saveCurrentProgress(),this.saveSession())},e=>{console.error("Auth state listener error:",e)})}setupNetworkListeners(){this.onlineListener=()=>{console.log("Network: Online"),this.sessionData&&(this.sessionData.isOnline=!0,this.saveSession(),this.syncPendingData())},this.offlineListener=()=>{console.log("Network: Offline"),this.sessionData&&(this.sessionData.isOnline=!1,this.saveSession())},window.addEventListener("online",this.onlineListener),window.addEventListener("offline",this.offlineListener)}setupPeriodicSave(){this.saveInterval=setInterval(()=>{this.saveSession(),this.saveCurrentProgress()},3e4)}setupHeartbeat(){this.heartbeatInterval=setInterval(()=>{this.sessionData&&(this.sessionData.lastActivity=new Date,this.saveSession())},6e4)}saveWorkProgress(e){var t;if(!(null==(t=this.sessionData)?void 0:t.user)||"undefined"==typeof localStorage)return;let s={currentStep:e.currentStep||null,userTypedText:e.userTypedText||"",selectedLanguage:e.selectedLanguage||"",isTypingComplete:e.isTypingComplete||!1,completedTranslations:e.completedTranslations||0,batchProgress:e.batchProgress||0,lastSaved:new Date,userId:this.sessionData.user.uid};this.sessionData.workProgress=s,this.saveSession();try{let e="work_progress_".concat(this.sessionData.user.uid);localStorage.setItem(e,JSON.stringify(s)),console.log("Work progress saved for user:",this.sessionData.user.uid)}catch(e){console.error("Error saving work progress to localStorage:",e)}}getWorkProgress(){var e;if(!(null==(e=this.sessionData)?void 0:e.user)||"undefined"==typeof localStorage)return null;if(this.sessionData.workProgress)return this.sessionData.workProgress;try{let e="work_progress_".concat(this.sessionData.user.uid),t=localStorage.getItem(e);if(t)return JSON.parse(t)}catch(e){console.error("Error parsing saved work progress:",e)}return null}clearWorkProgress(){var e;if((null==(e=this.sessionData)?void 0:e.user)&&"undefined"!=typeof localStorage){this.sessionData.workProgress=void 0;try{let e="work_progress_".concat(this.sessionData.user.uid);localStorage.removeItem(e),this.saveSession(),console.log("Work progress cleared for user:",this.sessionData.user.uid)}catch(e){console.error("Error clearing work progress:",e)}}}restoreWorkProgress(e){if("undefined"!=typeof localStorage)try{let t=localStorage.getItem("work_progress_".concat(e));if(t){let s=JSON.parse(t);this.sessionData&&(this.sessionData.workProgress=s),console.log("Work progress restored for user:",e)}}catch(e){console.error("Error restoring work progress:",e)}}saveCurrentProgress(){var e;if((null==(e=this.sessionData)?void 0:e.workProgress)&&"undefined"!=typeof localStorage)try{let e="work_progress_".concat(this.sessionData.workProgress.userId);localStorage.setItem(e,JSON.stringify(this.sessionData.workProgress))}catch(e){console.error("Error saving current progress:",e)}}saveSession(){if(this.sessionData&&"undefined"!=typeof localStorage)try{let e={...this.sessionData,user:this.sessionData.user?{uid:this.sessionData.user.uid,email:this.sessionData.user.email,displayName:this.sessionData.user.displayName}:null};localStorage.setItem("instra_session",JSON.stringify(e))}catch(e){console.error("Error saving session:",e)}}restoreSession(){if("undefined"!=typeof localStorage)try{let e="instra_session",t=localStorage.getItem(e);if(t){let s=JSON.parse(t),o=new Date(s.lastActivity);(new Date().getTime()-o.getTime())/36e5<24?(console.log("Restored session from:",o.toLocaleString()),this.sessionData&&(this.sessionData.lastActivity=o,this.sessionData.isOnline="undefined"==typeof navigator||navigator.onLine)):(console.log("Session expired, clearing old session"),localStorage.removeItem(e))}}catch(e){console.error("Error restoring session:",e)}}syncPendingData(){console.log("Syncing pending data...")}updateActivity(){this.sessionData&&(this.sessionData.lastActivity=new Date)}isOnline(){var e;return"undefined"!=typeof navigator?navigator.onLine:(null==(e=this.sessionData)?void 0:e.isOnline)||!0}getSessionId(){var e;return(null==(e=this.sessionData)?void 0:e.sessionId)||""}getCurrentUser(){var e;return(null==(e=this.sessionData)?void 0:e.user)||null}cleanup(){this.heartbeatInterval&&clearInterval(this.heartbeatInterval),this.saveInterval&&clearInterval(this.saveInterval),this.authStateListener&&this.authStateListener(),this.onlineListener&&window.removeEventListener("online",this.onlineListener),this.offlineListener&&window.removeEventListener("offline",this.offlineListener),console.log("Session Manager cleaned up")}constructor(){this.sessionData=null,this.heartbeatInterval=null,this.saveInterval=null,this.authStateListener=null,this.onlineListener=null,this.offlineListener=null,this.initializeSession()}}let i=n.getInstance()},6104:(e,t,s)=>{s.d(t,{Cn:()=>g,auth:()=>c,db:()=>u});var o=s(3915),r=s(3004),n=s(5317),i=s(858),a=s(2144);let l=(0,o.Dk)().length?(0,o.Sx)():(0,o.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),c=(0,r.xI)(l),u=(0,n.aU)(l);(0,i.c7)(l);let g=(0,a.Uz)(l)},6681:(e,t,s)=>{s.d(t,{Nu:()=>c,hD:()=>l,wC:()=>u});var o=s(2115),r=s(3004),n=s(6104),i=s(12),a=s(5738);function l(){let[e,t]=(0,o.useState)(null),[s,l]=(0,o.useState)(!0),[c,u]=(0,o.useState)(!0);(0,o.useEffect)(()=>{let e=0,s=()=>{try{return(0,r.hg)(n.auth,s=>{console.log("Auth state changed:",s?"User logged in":"No user"),t(s),l(!1),a.i.updateActivity(),e=0},t=>{console.error("Auth state listener error:",t),e<3?(e++,console.log("Retrying auth listener (".concat(e,"/").concat(3,")...")),setTimeout(s,2e3*e)):(console.error("Max auth retries reached, setting loading to false"),l(!1))})}catch(e){return console.error("Error setting up auth state listener:",e),l(!1),()=>{}}},o=s(),i=()=>{u(!0),console.log("Network: Back online")},c=()=>{u(!1),console.log("Network: Offline")};return window.addEventListener("online",i),window.addEventListener("offline",c),u(navigator.onLine),()=>{o(),window.removeEventListener("online",i),window.removeEventListener("offline",c)}},[]);let g=async()=>{try{await (0,i.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:g,isOnline:c}}function c(){let{user:e,loading:t,isOnline:s}=l(),[r,n]=(0,o.useState)(null);return(0,o.useEffect)(()=>{if(r&&(clearTimeout(r),n(null)),!t&&!e){if(!s)return void console.log("User not authenticated but offline - waiting for network");n(setTimeout(()=>{console.log("Redirecting to login - no authenticated user"),window.location.href="/login"},2e3))}return()=>{r&&clearTimeout(r)}},[e,t,s]),{user:e,loading:t,isOnline:s}}function u(){let{user:e,loading:t}=l(),[s,r]=(0,o.useState)(!1),[n,i]=(0,o.useState)(!0);return(0,o.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");r(t),i(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||n,isAdmin:s}}}}]);