(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7718,8297],{239:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var a=t(5155),r=t(2115),i=t(6874),n=t.n(i),o=t(6681),c=t(2649),l=t(7718),d=t(4752),u=t.n(d);function m(){let{user:e,loading:s,isAdmin:t}=(0,o.wC)(),[i,d]=(0,r.useState)(!1),[m,h]=(0,r.useState)(null),g=async()=>{if(e&&(await u().fire({icon:"warning",title:"Manual Active Days Increment",text:"This will increment active days for all eligible users. Are you sure?",showCancelButton:!0,confirmButtonText:"Yes, Proceed",cancelButtonText:"Cancel"})).isConfirmed)try{d(!0),console.log("\uD83D\uDD27 Manual active days increment triggered by admin: ".concat(e.uid)),console.log("\uD83D\uDCC5 Processing active days increment via Firebase Function...");let s=await (0,c.e5)();console.log("\uD83D\uDCCB Processing copy-paste reduction...");let t=await (0,l.Mk)(),a={success:!0,totalUsers:s.processed,updatedUsers:s.updated,skippedUsers:0,errors:s.errors,activeDaysResult:s,copyPasteResult:t};h(a),u().fire({icon:"success",title:"Process Completed",html:'\n          <div class="text-left">\n            <p><strong>Active Days Results:</strong></p>\n            <ul>\n              <li>Processed: '.concat(s.processed," users</li>\n              <li>Updated: ").concat(s.updated," users</li>\n              <li>Errors: ").concat(s.errors," users</li>\n            </ul>\n            <br>\n            <p><strong>Copy-Paste Results:</strong></p>\n            <ul>\n              <li>Processed: ").concat(t.processed," users</li>\n              <li>Reduced: ").concat(t.reduced," users</li>\n              <li>Expired: ").concat(t.expired," users</li>\n              <li>Errors: ").concat(t.errors," users</li>\n            </ul>\n          </div>\n        "),confirmButtonText:"OK"})}catch(e){console.error("Error triggering manual increment:",e),u().fire({icon:"error",title:"Process Failed",text:"An error occurred while processing active days increment."})}finally{d(!1)}};if(s)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Loading active days manager..."})]})});let x=(()=>{let e=new Date(new Date);return e.setDate(e.getDate()+1),e.setHours(0,0,0,0),e})();return(0,a.jsxs)("div",{className:"min-h-screen p-4",children:[(0,a.jsx)("div",{className:"glass-card p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-2xl font-bold text-white mb-2",children:[(0,a.jsx)("i",{className:"fas fa-calendar-plus mr-3"}),"Active Days Manager"]}),(0,a.jsx)("p",{className:"text-white/80",children:"Manage daily active days increment system"})]}),(0,a.jsxs)(n(),{href:"/admin",className:"btn-secondary",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]})]})}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"System Status"]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-clock mr-2"}),"Next Scheduled Run"]}),(0,a.jsx)("p",{className:"text-white/80 text-lg",children:x.toLocaleString()}),(0,a.jsx)("p",{className:"text-white/60 text-sm mt-1",children:"Runs automatically at midnight (12:00 AM) daily"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-cogs mr-2"}),"System Rules"]}),(0,a.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Increments active days for all active users"}),(0,a.jsx)("li",{children:"• Skips admin users automatically"}),(0,a.jsx)("li",{children:"• Skips users on leave"}),(0,a.jsx)("li",{children:"• Runs once per day at midnight"})]})]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-hand-pointer mr-2"}),"Manual Controls"]}),(0,a.jsx)("div",{className:"bg-yellow-500/20 rounded-lg p-4 border border-yellow-500/30 mb-4",children:(0,a.jsxs)("div",{className:"flex items-start text-yellow-300",children:[(0,a.jsx)("i",{className:"fas fa-exclamation-triangle mr-2 mt-1"}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("p",{className:"font-semibold mb-1",children:"Warning:"}),(0,a.jsx)("p",{children:"Manual trigger will increment active days for all eligible users immediately. This should only be used for testing or emergency situations."})]})]})}),(0,a.jsx)("button",{onClick:g,disabled:i,className:"btn-primary",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Processing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-play mr-2"}),"Manual Trigger Active Days Increment"]})})]}),m&&(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-chart-bar mr-2"}),"Last Execution Result"]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"bg-blue-500/20 rounded-lg p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-400",children:m.totalUsers}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:"Total Users"})]}),(0,a.jsxs)("div",{className:"bg-green-500/20 rounded-lg p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-400",children:m.updatedUsers}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:"Successfully Updated"})]}),(0,a.jsxs)("div",{className:"bg-yellow-500/20 rounded-lg p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:m.skippedUsers}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:"Skipped Users"})]}),(0,a.jsxs)("div",{className:"bg-red-500/20 rounded-lg p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-400",children:m.errors}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:"Errors"})]})]}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat(m.success?"bg-green-500/20 text-green-400":"bg-red-500/20 text-red-400"),children:m.success?"Success":"Failed"})})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mt-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-question-circle mr-2"}),"How It Works"]}),(0,a.jsxs)("div",{className:"space-y-4 text-white/80",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1",children:"1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-white",children:"Automatic Scheduling"}),(0,a.jsx)("p",{className:"text-sm",children:"System runs automatically every day at midnight (12:00 AM)"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1",children:"2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-white",children:"User Filtering"}),(0,a.jsx)("p",{className:"text-sm",children:"Identifies eligible users (active, non-admin, not on leave)"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1",children:"3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-white",children:"Increment Process"}),(0,a.jsx)("p",{className:"text-sm",children:"Increases active days by 1 for each eligible user"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1",children:"4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-white",children:"Logging"}),(0,a.jsx)("p",{className:"text-sm",children:"Records all actions and results for audit purposes"})]})]})]})]})]})}},2649:(e,s,t)=>{"use strict";t.d(s,{Ou:()=>h,Ov:()=>b,PY:()=>j,ck:()=>f,e5:()=>v,iM:()=>x,lA:()=>y,rB:()=>g,tv:()=>w,wh:()=>p});var a=t(2144),r=t(6104);async function i(){let{auth:e}=await Promise.resolve().then(t.bind(t,6104)),s=e.currentUser;if(!s)throw Error("User not authenticated");try{await s.getIdToken(!0),console.log("✅ User token refreshed and validated")}catch(e){throw console.error("❌ Error refreshing user token:",e),Error("Authentication token expired. Please refresh the page.")}}let n=(0,a.Qg)(r.Cn,"getUserWorkData"),o=(0,a.Qg)(r.Cn,"submitTranslationBatch"),c=(0,a.Qg)(r.Cn,"getUserDashboardData"),l=((0,a.Qg)(r.Cn,"getAdminDashboardData"),(0,a.Qg)(r.Cn,"getUserTransactions")),d=(0,a.Qg)(r.Cn,"processWithdrawalRequest"),u=(0,a.Qg)(r.Cn,"processDailyActiveDays"),m=((0,a.Qg)(r.Cn,"processDailyCopyPasteReduction"),(0,a.Qg)(r.Cn,"grantCopyPastePermission"),(0,a.Qg)(r.Cn,"updateUserPlan"),(0,a.Qg)(r.Cn,"getPlatformStats")),h={functionsUsed:0,firestoreReadsAvoided:0,firestoreWritesOptimized:0,incrementFunctionUsage(){this.functionsUsed++,console.log("\uD83D\uDE80 Firebase Functions used: ".concat(this.functionsUsed))},addReadsAvoided(e){this.firestoreReadsAvoided+=e,console.log("\uD83D\uDCB0 Firestore reads avoided: ".concat(e," (Total: ").concat(this.firestoreReadsAvoided,")"))},addWritesOptimized(e){this.firestoreWritesOptimized+=e,console.log("⚡ Firestore writes optimized: ".concat(e," (Total: ").concat(this.firestoreWritesOptimized,")"))},getStats(){return{functionsUsed:this.functionsUsed,firestoreReadsAvoided:this.firestoreReadsAvoided,firestoreWritesOptimized:this.firestoreWritesOptimized,estimatedCostSavings:(36e-5*this.firestoreReadsAvoided+.00108*this.firestoreWritesOptimized).toFixed(4)}}};async function g(){try{console.log("\uD83D\uDE80 Fetching user work data via Firebase Function..."),await i();let e=(await n()).data;return h.incrementFunctionUsage(),h.addReadsAvoided(4),console.log("✅ User work data fetched successfully:",e),e}catch(s){var e;if(console.error("❌ Error fetching user work data:",s),"unauthenticated"===s.code||(null==(e=s.message)?void 0:e.includes("User must be authenticated")))throw Error("Authentication required. Please refresh the page and try again.");throw Error("Failed to fetch user work data")}}async function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;try{console.log("\uD83D\uDE80 Submitting translation batch via Firebase Function: ".concat(e," translations")),await i();let s=(await o({batchSize:e})).data;return h.incrementFunctionUsage(),h.addWritesOptimized(3),console.log("✅ Translation batch submitted successfully:",s),s}catch(e){var s;if(console.error("❌ Error submitting translation batch:",e),"unauthenticated"===e.code||(null==(s=e.message)?void 0:s.includes("User must be authenticated")))throw Error("Authentication required. Please refresh the page and try again.");if("permission-denied"===e.code)throw Error("Permission denied. Please check your account status.");throw Error("Failed to submit translation batch")}}async function p(){try{console.log("\uD83D\uDE80 Fetching user dashboard data via Firebase Function..."),await i();let e=(await c()).data;return h.incrementFunctionUsage(),h.addReadsAvoided(3),console.log("✅ User dashboard data fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching user dashboard data:",e),Error("Failed to fetch user dashboard data")}}async function y(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20,s=arguments.length>1?arguments[1]:void 0;try{console.log("\uD83D\uDE80 Fetching user transactions via Firebase Function: limit=".concat(e));let t=(await l({limit:e,startAfter:s})).data;return h.incrementFunctionUsage(),h.addReadsAvoided(Math.max(0,100-e)),console.log("✅ User transactions fetched successfully:",t),t}catch(e){throw console.error("❌ Error fetching user transactions:",e),Error("Failed to fetch user transactions")}}async function f(e,s){try{console.log("\uD83D\uDE80 Processing withdrawal request via Firebase Function: ₹".concat(e));let t=(await d({amount:e,upiId:s})).data;return h.incrementFunctionUsage(),h.addWritesOptimized(2),console.log("✅ Withdrawal request processed successfully:",t),t}catch(e){throw console.error("❌ Error processing withdrawal request:",e),Error("Failed to process withdrawal request")}}async function v(){try{console.log("\uD83D\uDE80 Processing daily active days via Firebase Function...");let e=(await u()).data;return h.incrementFunctionUsage(),h.addWritesOptimized(e.updated||0),console.log("✅ Daily active days processed successfully:",e),e}catch(e){throw console.error("❌ Error processing daily active days:",e),Error("Failed to process daily active days")}}async function w(){try{console.log("\uD83D\uDE80 Fetching platform stats via Firebase Function...");let e=(await m()).data;return h.incrementFunctionUsage(),h.addReadsAvoided(10),console.log("✅ Platform stats fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching platform stats:",e),Error("Failed to fetch platform stats")}}async function b(){try{console.log("\uD83D\uDE80 Getting optimized user work data..."),await i();let e=await n();return console.log("✅ Optimized user work data retrieved"),e.data}catch(e){throw console.error("❌ Error getting optimized user work data:",e),e}}async function j(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;try{console.log("\uD83D\uDE80 Submitting optimized translation batch: ".concat(e," translations"));let s=await o({batchSize:e});return console.log("✅ Optimized translation batch submitted"),s.data}catch(e){throw console.error("❌ Error submitting optimized translation batch:",e),e}}},7718:(e,s,t)=>{"use strict";t.d(s,{Mk:()=>m,checkCopyPastePermission:()=>c,grantCopyPastePermission:()=>l,i7:()=>u,removeCopyPastePermission:()=>d});var a=t(6104),r=t(5317);let i={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},n={users:"users"};class o{static async checkCopyPastePermission(e){try{let s=await (0,r.x7)((0,r.H9)(a.db,n.users,e));if(!s.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let t=s.data()[i.quickTranslationAdvantageExpiry];if(!t)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let o=t.toDate(),c=new Date,l=o>c,d=l?Math.ceil((o.getTime()-c.getTime())/864e5):0;return{hasPermission:l,daysRemaining:d,expiryDate:o}}catch(s){return console.error("Error checking copy-paste permission for user ".concat(e,":"),s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,s){try{let t=new Date;t.setDate(t.getDate()+s);let o=(0,r.H9)(a.db,n.users,e);await (0,r.mZ)(o,{[i.quickTranslationAdvantageExpiry]:r.Dc.fromDate(t),[i.lastCopyPasteReduction]:r.Dc.now()}),console.log("✅ Granted copy-paste permission to user ".concat(e," for ").concat(s," days (expires: ").concat(t.toDateString(),")"))}catch(s){throw console.error("Error granting copy-paste permission to user ".concat(e,":"),s),s}}static async removeCopyPastePermission(e){try{let s=(0,r.H9)(a.db,n.users,e);await (0,r.mZ)(s,{[i.quickTranslationAdvantageExpiry]:null}),console.log("✅ Removed copy-paste permission from user ".concat(e))}catch(s){throw console.error("Error removing copy-paste permission from user ".concat(e,":"),s),s}}static async reduceCopyPasteDays(e){try{let s=await (0,r.x7)((0,r.H9)(a.db,n.users,e));if(!s.exists())return{reduced:!1,daysRemaining:0,expired:!1};let t=s.data(),o=t[i.quickTranslationAdvantageExpiry],c=t[i.lastCopyPasteReduction];if(!o)return{reduced:!1,daysRemaining:0,expired:!1};let l=new Date().toDateString();if((c?c.toDate().toDateString():null)===l){let e=o.toDate(),s=new Date,t=Math.max(0,Math.ceil((e.getTime()-s.getTime())/864e5));return{reduced:!1,daysRemaining:t,expired:0===t}}let d=o.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let m=(0,r.H9)(a.db,n.users,e);if(u<=new Date)return await (0,r.mZ)(m,{[i.quickTranslationAdvantageExpiry]:null,[i.lastCopyPasteReduction]:r.Dc.now()}),console.log("\uD83D\uDCC5 Copy-paste permission expired for user ".concat(e)),{reduced:!0,daysRemaining:0,expired:!0};{await (0,r.mZ)(m,{[i.quickTranslationAdvantageExpiry]:r.Dc.fromDate(u),[i.lastCopyPasteReduction]:r.Dc.now()});let s=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log("\uD83D\uDCC5 Reduced copy-paste days for user ".concat(e,": ").concat(s," days remaining")),{reduced:!0,daysRemaining:s,expired:!1}}}catch(s){return console.error("Error reducing copy-paste days for user ".concat(e,":"),s),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,r.getDocs)((0,r.collection)(a.db,n.users)),s=0,t=0,i=0,c=0;for(let a of e.docs)try{s++;let e=await o.reduceCopyPasteDays(a.id);e.reduced&&(t++,e.expired&&i++)}catch(e){c++,console.error("Error processing copy-paste reduction for user ".concat(a.id,":"),e)}return console.log("✅ Daily copy-paste reduction complete:"),console.log("   - Processed: ".concat(s," users")),console.log("   - Reduced: ".concat(t," users")),console.log("   - Expired: ".concat(i," users")),console.log("   - Errors: ".concat(c," users")),{processed:s,reduced:t,expired:i,errors:c}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let s=await o.checkCopyPastePermission(e);return{hasPermission:s.hasPermission,daysRemaining:s.daysRemaining,expiryDate:s.expiryDate?s.expiryDate.toDateString():null}}catch(s){return console.error("Error getting copy-paste status for user ".concat(e,":"),s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let c=o.checkCopyPastePermission,l=o.grantCopyPastePermission,d=o.removeCopyPastePermission,u=o.reduceCopyPasteDays,m=o.processAllUsersCopyPasteReduction;o.getCopyPasteStatus},9409:(e,s,t)=>{Promise.resolve().then(t.bind(t,239))}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,8818,6874,6681,8441,1684,7358],()=>s(9409)),_N_E=e.O()}]);