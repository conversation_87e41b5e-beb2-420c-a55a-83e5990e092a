(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{1469:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return o},getImageProps:function(){return n}});let a=s(8229),l=s(8883),r=s(3063),i=a._(s(1193));function n(e){let{props:t}=(0,l.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let o=r.Image},2649:(e,t,s)=>{"use strict";s.d(t,{Ou:()=>u,Ov:()=>w,PY:()=>y,ck:()=>b,e5:()=>v,iM:()=>f,lA:()=>g,rB:()=>h,tv:()=>j,wh:()=>p});var a=s(2144),l=s(6104);async function r(){let{auth:e}=await Promise.resolve().then(s.bind(s,6104)),t=e.currentUser;if(!t)throw Error("User not authenticated");try{await t.getIdToken(!0),console.log("✅ User token refreshed and validated")}catch(e){throw console.error("❌ Error refreshing user token:",e),Error("Authentication token expired. Please refresh the page.")}}let i=(0,a.Qg)(l.Cn,"getUserWorkData"),n=(0,a.Qg)(l.Cn,"submitTranslationBatch"),o=(0,a.Qg)(l.Cn,"getUserDashboardData"),c=((0,a.Qg)(l.Cn,"getAdminDashboardData"),(0,a.Qg)(l.Cn,"getUserTransactions")),d=(0,a.Qg)(l.Cn,"processWithdrawalRequest"),m=(0,a.Qg)(l.Cn,"processDailyActiveDays"),x=((0,a.Qg)(l.Cn,"processDailyCopyPasteReduction"),(0,a.Qg)(l.Cn,"grantCopyPastePermission"),(0,a.Qg)(l.Cn,"updateUserPlan"),(0,a.Qg)(l.Cn,"getPlatformStats")),u={functionsUsed:0,firestoreReadsAvoided:0,firestoreWritesOptimized:0,incrementFunctionUsage(){this.functionsUsed++,console.log("\uD83D\uDE80 Firebase Functions used: ".concat(this.functionsUsed))},addReadsAvoided(e){this.firestoreReadsAvoided+=e,console.log("\uD83D\uDCB0 Firestore reads avoided: ".concat(e," (Total: ").concat(this.firestoreReadsAvoided,")"))},addWritesOptimized(e){this.firestoreWritesOptimized+=e,console.log("⚡ Firestore writes optimized: ".concat(e," (Total: ").concat(this.firestoreWritesOptimized,")"))},getStats(){return{functionsUsed:this.functionsUsed,firestoreReadsAvoided:this.firestoreReadsAvoided,firestoreWritesOptimized:this.firestoreWritesOptimized,estimatedCostSavings:(36e-5*this.firestoreReadsAvoided+.00108*this.firestoreWritesOptimized).toFixed(4)}}};async function h(){try{console.log("\uD83D\uDE80 Fetching user work data via Firebase Function..."),await r();let e=(await i()).data;return u.incrementFunctionUsage(),u.addReadsAvoided(4),console.log("✅ User work data fetched successfully:",e),e}catch(t){var e;if(console.error("❌ Error fetching user work data:",t),"unauthenticated"===t.code||(null==(e=t.message)?void 0:e.includes("User must be authenticated")))throw Error("Authentication required. Please refresh the page and try again.");throw Error("Failed to fetch user work data")}}async function f(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;try{console.log("\uD83D\uDE80 Submitting translation batch via Firebase Function: ".concat(e," translations")),await r();let t=(await n({batchSize:e})).data;return u.incrementFunctionUsage(),u.addWritesOptimized(3),console.log("✅ Translation batch submitted successfully:",t),t}catch(e){var t;if(console.error("❌ Error submitting translation batch:",e),"unauthenticated"===e.code||(null==(t=e.message)?void 0:t.includes("User must be authenticated")))throw Error("Authentication required. Please refresh the page and try again.");if("permission-denied"===e.code)throw Error("Permission denied. Please check your account status.");throw Error("Failed to submit translation batch")}}async function p(){try{console.log("\uD83D\uDE80 Fetching user dashboard data via Firebase Function..."),await r();let e=(await o()).data;return u.incrementFunctionUsage(),u.addReadsAvoided(3),console.log("✅ User dashboard data fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching user dashboard data:",e),Error("Failed to fetch user dashboard data")}}async function g(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20,t=arguments.length>1?arguments[1]:void 0;try{console.log("\uD83D\uDE80 Fetching user transactions via Firebase Function: limit=".concat(e));let s=(await c({limit:e,startAfter:t})).data;return u.incrementFunctionUsage(),u.addReadsAvoided(Math.max(0,100-e)),console.log("✅ User transactions fetched successfully:",s),s}catch(e){throw console.error("❌ Error fetching user transactions:",e),Error("Failed to fetch user transactions")}}async function b(e,t){try{console.log("\uD83D\uDE80 Processing withdrawal request via Firebase Function: ₹".concat(e));let s=(await d({amount:e,upiId:t})).data;return u.incrementFunctionUsage(),u.addWritesOptimized(2),console.log("✅ Withdrawal request processed successfully:",s),s}catch(e){throw console.error("❌ Error processing withdrawal request:",e),Error("Failed to process withdrawal request")}}async function v(){try{console.log("\uD83D\uDE80 Processing daily active days via Firebase Function...");let e=(await m()).data;return u.incrementFunctionUsage(),u.addWritesOptimized(e.updated||0),console.log("✅ Daily active days processed successfully:",e),e}catch(e){throw console.error("❌ Error processing daily active days:",e),Error("Failed to process daily active days")}}async function j(){try{console.log("\uD83D\uDE80 Fetching platform stats via Firebase Function...");let e=(await x()).data;return u.incrementFunctionUsage(),u.addReadsAvoided(10),console.log("✅ Platform stats fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching platform stats:",e),Error("Failed to fetch platform stats")}}async function w(){try{console.log("\uD83D\uDE80 Getting optimized user work data..."),await r();let e=await i();return console.log("✅ Optimized user work data retrieved"),e.data}catch(e){throw console.error("❌ Error getting optimized user work data:",e),e}}async function y(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;try{console.log("\uD83D\uDE80 Submitting optimized translation batch: ".concat(e," translations"));let t=await n({batchSize:e});return console.log("✅ Optimized translation batch submitted"),t.data}catch(e){throw console.error("❌ Error submitting optimized translation batch:",e),e}}},3282:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var a=s(5155),l=s(2115),r=s(6874),i=s.n(r),n=s(6766),o=s(6681),c=s(7460),d=s(3592),m=s(2649),x=s(12);function u(e){let{userId:t,isOpen:s,onClose:r}=e,[i,n]=(0,l.useState)([]),[o,c]=(0,l.useState)(!0);(0,l.useEffect)(()=>{s&&t&&m()},[s,t]);let m=async()=>{try{c(!0);let e=await (0,d.Ss)(t,20);n(e)}catch(e){console.error("Error loading notifications:",e)}finally{c(!1)}},x=e=>{e.id&&!(0,d.mv)(e.id,t)&&((0,d.bA)(e.id,t),n([...i]))},u=e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}},h=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"Just now";if(t<3600){let e=Math.floor(t/60);return"".concat(e," minute").concat(e>1?"s":""," ago")}if(t<86400){let e=Math.floor(t/3600);return"".concat(e," hour").concat(e>1?"s":""," ago")}{let e=Math.floor(t/86400);return"".concat(e," day").concat(e>1?"s":""," ago")}};return s?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-16 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[80vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-900",children:[(0,a.jsx)("i",{className:"fas fa-bell mr-2"}),"Notifications"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:m,disabled:o,className:"text-gray-500 hover:text-gray-700 transition-colors p-1",title:"Refresh notifications",children:(0,a.jsx)("i",{className:"fas fa-sync-alt ".concat(o?"animate-spin":"")})}),(0,a.jsx)("button",{onClick:r,className:"text-gray-500 hover:text-gray-700 transition-colors",children:(0,a.jsx)("i",{className:"fas fa-times text-xl"})})]})]}),(0,a.jsx)("div",{className:"overflow-y-auto max-h-[60vh]",children:o?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"spinner w-8 h-8"})}):0===i.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("i",{className:"fas fa-bell-slash text-gray-300 text-4xl mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No notifications yet"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"You'll see important updates here"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:i.map(e=>{let s=!!e.id&&(0,d.mv)(e.id,t);return(0,a.jsx)("div",{onClick:()=>x(e),className:"p-4 cursor-pointer hover:bg-gray-50 transition-colors ".concat(s?"":"bg-blue-50 border-l-4 border-l-blue-500"),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,a.jsx)("i",{className:u(e.type)})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"text-sm font-medium ".concat(s?"text-gray-700":"text-gray-900"),children:e.title}),!s&&(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"})]}),(0,a.jsx)("p",{className:"text-sm mt-1 ".concat(s?"text-gray-600":"text-gray-800"),children:e.message}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:h(e.createdAt)})]})]})},e.id)})})}),i.length>0&&(0,a.jsx)("div",{className:"p-4 border-t border-gray-200 bg-gray-50",children:(0,a.jsx)("button",{onClick:()=>{i.forEach(e=>{e.id&&!(0,d.mv)(e.id,t)&&(0,d.bA)(e.id,t)}),n([...i])},className:"w-full text-sm text-blue-600 hover:text-blue-800 font-medium",children:"Mark all as read"})})]})}):null}function h(e){let{userId:t,onClick:s}=e,[r,i]=(0,l.useState)([]),[n,o]=(0,l.useState)(0),[c,m]=(0,l.useState)(!1);(0,l.useEffect)(()=>{if(t){x();let e=setInterval(x,15e3);return()=>clearInterval(e)}},[t]);let x=async()=>{try{m(!0);let e=await (0,d.Ss)(t,20);i(e);let s=(0,d.ul)(e,t);if(s>n&&n>0){let e=document.querySelector(".notification-bell");e&&(e.classList.add("animate-bounce"),setTimeout(()=>{e.classList.remove("animate-bounce")},1e3))}o(s),console.log("Loaded ".concat(e.length," notifications, ").concat(s," unread"))}catch(e){console.error("Error loading notifications for bell:",e)}finally{m(!1)}};return(0,a.jsxs)("button",{onClick:s,className:"relative p-2 text-white hover:text-yellow-300 transition-colors",title:"".concat(n," unread notifications"),children:[(0,a.jsx)("i",{className:"fas fa-bell text-xl notification-bell ".concat(c?"animate-pulse":"")}),n>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold animate-pulse",children:n>9?"9+":n}),c&&(0,a.jsx)("span",{className:"absolute -bottom-1 -right-1 bg-blue-500 text-white text-xs rounded-full h-3 w-3 flex items-center justify-center",children:(0,a.jsx)("i",{className:"fas fa-sync-alt text-xs animate-spin"})})]})}var f=s(8647),p=s(4752),g=s.n(p);function b(e){let{userId:t,currentMonth:r,usedLeaves:i,maxLeaves:n,onLeaveCountChange:o}=e,[c,d]=(0,l.useState)([]),[m,x]=(0,l.useState)(!1),[u,h]=(0,l.useState)(!1),[f,p]=(0,l.useState)(""),[b,v]=(0,l.useState)({date:"",reason:""});(0,l.useEffect)(()=>{w(),j()},[t]);let j=async()=>{try{let{getUserData:e,getPlanValidityDays:a}=await Promise.resolve().then(s.bind(s,3592)),l=await e(t);if(l){let e,t=new Date;if("Trial"===l.plan){let t=l.joinedDate||new Date;e=new Date(t.getTime()+1728e5)}else if(l.planExpiry)e=l.planExpiry;else{let s=a(l.plan),r=l.activeDays||1,i=Math.max(0,s-r);e=new Date(t.getTime()+24*i*36e5)}let s=new Date(t.getTime()+2592e6),r=e<s?e:s;p(r.toISOString().split("T")[0])}}catch(t){console.error("Error calculating max date:",t);let e=new Date;e.setDate(e.getDate()+30),p(e.toISOString().split("T")[0])}},w=async()=>{try{let{getUserLeaves:e}=await s.e(9567).then(s.bind(s,9567)),a=await e(t);d(a)}catch(e){console.error("Error loading user leaves:",e),d([])}},y=async()=>{try{if(!b.date||!b.reason.trim())return void g().fire({icon:"error",title:"Validation Error",text:"Please fill in all required fields."});let e=new Date(b.date),a=new Date;a.setHours(0,0,0,0);let l=new Date(a);if(l.setDate(l.getDate()+1),e<=a)return void g().fire({icon:"error",title:"Invalid Date",text:"Cannot apply leave for today or past dates. Please select a future date."});try{let{ActiveDaysService:l}=await s.e(2439).then(s.bind(s,2439));if((await l.isUserPlanExpired(t)).expired)return void g().fire({icon:"error",title:"Plan Expired",text:"Your plan has expired. Cannot apply for leave."});let{getUserData:r,getPlanValidityDays:i}=await Promise.resolve().then(s.bind(s,3592)),n=await r(t);if(n){let t;if("Trial"===n.plan){let e=n.joinedDate||new Date;t=new Date(e.getTime()+1728e5)}else if(n.planExpiry)t=n.planExpiry;else{let e=i(n.plan),s=n.activeDays||1,l=Math.max(0,e-s);t=new Date(a.getTime()+24*l*36e5)}if(e>t)return void g().fire({icon:"error",title:"Date Outside Plan Period",text:"Cannot apply leave beyond your plan expiry date (".concat(t.toLocaleDateString(),").")})}}catch(e){console.error("Error checking plan expiry:",e)}if(i>=n)return void g().fire({icon:"error",title:"Leave Limit Exceeded",text:"You have already used all ".concat(n," leaves for this month.")});if(c.find(t=>t.date.toDateString()===e.toDateString()))return void g().fire({icon:"error",title:"Duplicate Application",text:"You have already applied for leave on this date."});h(!0);let{applyUserLeave:r}=await s.e(9567).then(s.bind(s,9567)),d=await r({userId:t,date:e,reason:b.reason.trim()});await w(),o&&o(),d.autoApproved?g().fire({icon:"success",title:"✅ Leave Auto-Approved!",html:'\n            <div class="text-left">\n              <p><strong>Your leave has been automatically approved!</strong></p>\n              <br>\n              <p><strong>Date:</strong> '.concat(e.toLocaleDateString(),"</p>\n              <p><strong>Reason:</strong> ").concat(b.reason.trim(),'</p>\n              <br>\n              <p class="text-green-600"><strong>Leave Quota:</strong> ').concat(d.usedLeaves,"/").concat(d.maxLeaves,' used this month</p>\n              <p class="text-blue-600"><strong>Status:</strong> Approved automatically</p>\n            </div>\n          '),timer:6e3,showConfirmButton:!0,confirmButtonText:"Great!"}):g().fire({icon:"warning",title:"⏳ Leave Pending Approval",html:'\n            <div class="text-left">\n              <p><strong>Your leave application has been submitted.</strong></p>\n              <br>\n              <p><strong>Date:</strong> '.concat(e.toLocaleDateString(),"</p>\n              <p><strong>Reason:</strong> ").concat(b.reason.trim(),'</p>\n              <br>\n              <p class="text-orange-600"><strong>Status:</strong> Pending admin approval (quota exceeded)</p>\n              <p class="text-gray-600"><strong>Leave Quota:</strong> ').concat(d.maxLeaves,"/").concat(d.maxLeaves," used this month</p>\n            </div>\n          "),timer:6e3,showConfirmButton:!0,confirmButtonText:"Understood"}),v({date:"",reason:""}),x(!1)}catch(e){console.error("Error applying leave:",e),g().fire({icon:"error",title:"Application Failed",text:"Failed to apply for leave. Please try again."})}finally{h(!1)}},N=async e=>{try{let t=c.find(t=>t.id===e);if(!t||"pending"!==t.status)return void g().fire({icon:"error",title:"Cannot Cancel",text:"Only pending leave applications can be cancelled."});if((await g().fire({title:"Cancel Leave Application",text:"Are you sure you want to cancel this leave application?",icon:"warning",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Cancel",cancelButtonText:"Keep Application"})).isConfirmed){let{cancelUserLeave:t}=await s.e(9567).then(s.bind(s,9567));await t(e),await w(),o&&o(),g().fire({icon:"success",title:"Application Cancelled",text:"Your leave application has been cancelled.",timer:2e3,showConfirmButton:!1})}}catch(e){console.error("Error cancelling leave:",e),g().fire({icon:"error",title:"Cancellation Failed",text:"Failed to cancel leave application. Please try again."})}},k=e=>{switch(e){case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"pending":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},D=(e,t)=>{switch(e){case"approved":return"system"===t?"fas fa-magic text-green-500":"fas fa-check-circle text-green-500";case"rejected":return"fas fa-times-circle text-red-500";case"pending":return"fas fa-clock text-yellow-500";default:return"fas fa-question-circle text-gray-500"}},C=n-i;return(0,a.jsxs)("div",{className:"glass-card p-4 md:p-6 relative",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-2",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,a.jsx)("i",{className:"fas fa-calendar-times mr-2"}),"Leave Management"]}),(0,a.jsxs)("div",{className:"text-left sm:text-right",children:[(0,a.jsxs)("div",{className:"text-sm text-white/80",children:[r," Leaves"]}),(0,a.jsxs)("div",{className:"text-lg font-bold text-white",children:[C,"/",n," Available"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-2 sm:gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-green-500/20 p-2 sm:p-3 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-lg sm:text-xl font-bold text-green-400",children:n}),(0,a.jsx)("div",{className:"text-xs text-white/80",children:"Monthly Quota"})]}),(0,a.jsxs)("div",{className:"bg-yellow-500/20 p-2 sm:p-3 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-lg sm:text-xl font-bold text-yellow-400",children:i}),(0,a.jsx)("div",{className:"text-xs text-white/80",children:"Used"})]}),(0,a.jsxs)("div",{className:"bg-blue-500/20 p-2 sm:p-3 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-lg sm:text-xl font-bold text-blue-400",children:C}),(0,a.jsx)("div",{className:"text-xs text-white/80",children:"Remaining"})]})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("button",{onClick:()=>x(!0),disabled:C<=0,className:"w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base",children:[(0,a.jsx)("i",{className:"fas fa-plus mr-2"}),C>0?"Apply for Leave":"No Leaves Available"]})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white/80",children:"Recent Applications"}),0===c.length?(0,a.jsxs)("div",{className:"text-center py-4 text-white/60",children:[(0,a.jsx)("i",{className:"fas fa-calendar-check text-2xl mb-2"}),(0,a.jsx)("p",{className:"text-sm",children:"No leave applications yet"})]}):(0,a.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:c.slice(-5).reverse().map(e=>(0,a.jsx)("div",{className:"bg-white/10 p-2 sm:p-3 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start justify-between gap-2",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2",children:[(0,a.jsx)("span",{className:"text-white font-medium text-sm",children:e.date.toLocaleDateString()}),(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ".concat(k(e.status)," w-fit"),children:[(0,a.jsx)("i",{className:"".concat(D(e.status,e.reviewedBy)," mr-1")}),e.status.charAt(0).toUpperCase()+e.status.slice(1),"system"===e.reviewedBy&&"approved"===e.status&&(0,a.jsx)("span",{className:"ml-1",title:"Auto-approved",children:"⚡"})]})]}),(0,a.jsxs)("div",{className:"text-sm text-white/70 mt-1 break-words",children:[e.reason,"system"===e.reviewedBy&&"approved"===e.status&&(0,a.jsxs)("div",{className:"text-xs text-green-400 mt-1",children:[(0,a.jsx)("i",{className:"fas fa-magic mr-1"}),"Auto-approved (within quota)"]}),e.reviewNotes&&"system"!==e.reviewedBy&&(0,a.jsxs)("div",{className:"text-xs text-blue-400 mt-1 break-words",children:[(0,a.jsx)("i",{className:"fas fa-comment mr-1"}),e.reviewNotes]})]})]}),"pending"===e.status&&(0,a.jsx)("button",{onClick:()=>N(e.id),className:"text-red-400 hover:text-red-300 text-sm flex-shrink-0 p-1",title:"Cancel application",children:(0,a.jsx)("i",{className:"fas fa-times"})})]})},e.id))})]}),m&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 sm:p-6 max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4 sm:mb-6",children:[(0,a.jsx)("h3",{className:"text-lg sm:text-xl font-bold text-gray-900",children:"Apply for Leave"}),(0,a.jsx)("button",{onClick:()=>x(!1),className:"text-gray-500 hover:text-gray-700 p-1",children:(0,a.jsx)("i",{className:"fas fa-times text-lg sm:text-xl"})})]}),f&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center text-blue-800",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),(0,a.jsxs)("span",{className:"text-sm",children:["Leave applications are allowed until ",new Date(f).toLocaleDateString()]})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),(0,a.jsx)("input",{type:"date",value:b.date,onChange:e=>v(t=>({...t,date:e.target.value})),min:(()=>{let e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]})(),max:f,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Leave can only be applied for future dates within your plan period"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reason"}),(0,a.jsx)("textarea",{value:b.reason,onChange:e=>v(t=>({...t,reason:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter reason for leave..."})]}),(0,a.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"You have ",C," leave(s) remaining for ",r,"."]}),C>0&&(0,a.jsxs)("div",{className:"text-sm text-green-700 mt-2",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-2"}),(0,a.jsx)("strong",{children:"Auto-Approval:"})," Your leave will be automatically approved since you have available quota."]}),C<=0&&(0,a.jsxs)("div",{className:"text-sm text-orange-700 mt-2",children:[(0,a.jsx)("i",{className:"fas fa-clock mr-2"}),(0,a.jsx)("strong",{children:"Manual Review:"})," Leave will require admin approval as quota is exceeded."]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-end gap-3 sm:gap-4 mt-4 sm:mt-6",children:[(0,a.jsx)("button",{onClick:()=>x(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 order-2 sm:order-1",children:"Cancel"}),(0,a.jsx)("button",{onClick:y,disabled:u,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 order-1 sm:order-2",children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Applying..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Apply Leave"]})})]})]})})]})}var v=s(8926);function j(){let{user:e,loading:t}=(0,o.Nu)(),{hasBlockingNotifications:r,isChecking:p,markAllAsRead:g}=(0,c.J)((null==e?void 0:e.uid)||null),[j,w]=(0,l.useState)(null),[y,N]=(0,l.useState)(null),[k,D]=(0,l.useState)(null),[C,A]=(0,l.useState)(!0),[E,S]=(0,l.useState)(!1),[F,T]=(0,l.useState)(0);(0,l.useEffect)(()=>{e&&L()},[e]);let L=async()=>{try{A(!0),console.log("\uD83D\uDE80 Loading dashboard data via Firebase Function...");let e=await (0,m.wh)();m.Ou.incrementFunctionUsage(),console.log("\uD83D\uDCB0 Dashboard cost optimization stats:",m.Ou.getStats()),w({name:e.profile.name,email:e.profile.email,mobile:e.profile.mobile,referralCode:"",plan:e.profile.plan,planExpiry:null,activeDays:e.profile.activeDays}),N({wallet:e.wallet.balance}),D({totalTranslations:e.translations.total,todayTranslations:e.translations.today,remainingTranslations:Math.max(0,50-e.translations.today)}),await P(),console.log("✅ Dashboard data loaded successfully via Firebase Function")}catch(t){console.error("❌ Error loading dashboard data via Firebase Function:",t),console.log("\uD83D\uDD04 Falling back to original data loading method...");try{let[t,s,a]=await Promise.all([(0,d.getUserData)(e.uid),(0,d.getWalletData)(e.uid),(0,d.getVideoCountData)(e.uid),P()]);w(t),N(s),D(a),console.log("✅ Fallback data loading completed")}catch(e){console.error("❌ Fallback data loading also failed:",e)}}finally{A(!1)}},P=async()=>{try{let{getUserMonthlyLeaveCount:t}=await s.e(9567).then(s.bind(s,9567)),a=new Date,l=a.getFullYear(),r=a.getMonth()+1,i=await t(e.uid,l,r);return T(i),console.log("User ".concat(e.uid," has used ").concat(i," leaves this month")),i}catch(e){return console.error("Error loading user leave count:",e),T(0),0}};return t||C||p?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:t?"Loading...":p?"Checking notifications...":"Loading dashboard..."})]})}):r&&e?(0,a.jsx)(f.A,{userId:e.uid,onAllRead:g}):(0,a.jsxs)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:[(0,a.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:40,height:40,className:"mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"Instra Global Dashboard"}),(0,a.jsxs)("p",{className:"text-white/80",children:["Welcome back, ",(null==j?void 0:j.name)||"User"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[e&&(0,a.jsx)(h,{userId:e.uid,onClick:()=>S(!0)}),(0,a.jsxs)("button",{onClick:()=>{(0,x._f)(null==e?void 0:e.uid,"/login")},className:"glass-button px-4 py-2 text-white hover:bg-red-500/20 transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-sign-out-alt mr-2"}),"Logout"]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6",children:[(0,a.jsxs)(i(),{href:"/work",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-language text-3xl text-blue-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Translate"})]}),(0,a.jsxs)(i(),{href:"/wallet",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-wallet text-3xl text-green-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Wallet"})]}),(0,a.jsxs)(i(),{href:"/transactions",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-history text-3xl text-orange-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Transactions"})]}),(0,a.jsxs)(i(),{href:"/refer",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-users text-3xl text-blue-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Refer & Earn"})]}),(0,a.jsxs)(i(),{href:"/profile",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-user text-3xl text-purple-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Profile"})]}),(0,a.jsxs)(i(),{href:"/plans",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-crown text-3xl text-yellow-400 mb-2"}),(0,a.jsx)("h3",{className:"text-white font-semibold",children:"Plans"})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-wallet mr-2"}),"Wallet Overview"]}),(0,a.jsxs)("div",{className:"bg-green-500/20 p-6 rounded-lg text-center",children:[(0,a.jsx)("h3",{className:"text-green-400 font-semibold mb-2",children:"My Wallet"}),(0,a.jsxs)("p",{className:"text-4xl font-bold text-white mb-2",children:["₹",((null==y?void 0:y.wallet)||0).toFixed(2)]}),(0,a.jsx)("p",{className:"text-white/60",children:"Total available balance"}),(null==j?void 0:j.plan)==="Trial"&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,a.jsx)("i",{className:"fas fa-lock text-red-400 mr-2"}),(0,a.jsx)("span",{className:"text-red-400 font-medium text-sm",children:"Withdrawal Restricted"})]}),(0,a.jsx)("p",{className:"text-white/80 text-xs mb-3",children:"Trial users cannot withdraw funds. Upgrade to enable withdrawals."}),(0,a.jsxs)(i(),{href:"/plans",className:"btn-secondary text-xs px-3 py-1",children:[(0,a.jsx)("i",{className:"fas fa-arrow-up mr-1"}),"Upgrade Plan"]})]}),(0,a.jsxs)(i(),{href:"/wallet",className:"btn-primary mt-4 inline-block",children:[(0,a.jsx)("i",{className:"fas fa-eye mr-2"}),"View Details"]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-language mr-2"}),"Today's Progress"]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-purple-400",children:(null==k?void 0:k.todayTranslations)||0}),(0,a.jsx)("p",{className:"text-white/80",children:"Translations Done"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-yellow-400",children:(null==k?void 0:k.remainingTranslations)||0}),(0,a.jsx)("p",{className:"text-white/80",children:"Remaining"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-green-400",children:(null==k?void 0:k.totalTranslations)||0}),(0,a.jsx)("p",{className:"text-white/80",children:"Total Translations"})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("div",{className:"bg-white/20 rounded-full h-3",children:(0,a.jsx)("div",{className:"bg-purple-500 h-3 rounded-full transition-all duration-300",style:{width:"".concat(k?k.todayTranslations/50*100:0,"%")}})}),(0,a.jsxs)("p",{className:"text-white/80 text-sm mt-2 text-center",children:[(null==k?void 0:k.todayTranslations)||0," / 50 translations completed today"]})]})]}),e&&(0,a.jsx)("div",{className:"mb-8 sm:mb-10 relative z-10",children:(0,a.jsx)(b,{userId:e.uid,currentMonth:new Date().toLocaleDateString("en-US",{month:"long",year:"numeric"}),usedLeaves:F,maxLeaves:4,onLeaveCountChange:P})}),(0,a.jsxs)("div",{className:"glass-card p-4 md:p-6 mb-6 mt-8 sm:mt-10 relative z-10 clear-both",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-headset mr-2"}),"Need Help?"]}),(0,a.jsx)("p",{className:"text-white/60 mb-6",children:"Our support team is here to help you with any questions about earning, withdrawals, or your account."}),(0,a.jsxs)("div",{className:"grid sm:grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("a",{href:"https://wa.me/************",target:"_blank",rel:"noopener noreferrer",className:"flex items-center bg-green-500/20 border border-green-500/30 rounded-lg p-4 hover:bg-green-500/30 transition-colors",children:[(0,a.jsx)("i",{className:"fab fa-whatsapp text-green-400 text-2xl mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-white font-semibold",children:"WhatsApp Support"}),(0,a.jsx)("div",{className:"text-green-400 text-sm",children:"+91 **********"}),(0,a.jsx)("div",{className:"text-white/60 text-xs",children:"9 AM - 6 PM (Working days)"})]})]}),(0,a.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center bg-blue-500/20 border border-blue-500/30 rounded-lg p-4 hover:bg-blue-500/30 transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-envelope text-blue-400 text-2xl mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-white font-semibold",children:"Email Support"}),(0,a.jsx)("div",{className:"text-blue-400 text-sm",children:"<EMAIL>"}),(0,a.jsx)("div",{className:"text-white/60 text-xs",children:"9 AM - 6 PM (Working days)"})]})]})]})]}),(0,a.jsx)(v.A,{variant:"dashboard",className:"mb-6"}),e&&(0,a.jsx)(u,{userId:e.uid,isOpen:E,onClose:()=>S(!1)})]})}},5604:(e,t,s)=>{Promise.resolve().then(s.bind(s,3282))},6766:(e,t,s)=>{"use strict";s.d(t,{default:()=>l.a});var a=s(1469),l=s.n(a)},7460:(e,t,s)=>{"use strict";s.d(t,{J:()=>r});var a=s(2115),l=s(3592);function r(e){let[t,s]=(0,a.useState)(!1),[r,i]=(0,a.useState)(!0);(0,a.useEffect)(()=>{e?n():i(!1)},[e]);let n=async()=>{try{i(!0);let t=await (0,l.iA)(e);s(t)}catch(e){console.error("Error checking for blocking notifications:",e),s(!1)}finally{i(!1)}};return{hasBlockingNotifications:t,isChecking:r,checkForBlockingNotifications:n,markAllAsRead:()=>{s(!1)}}}},8647:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(5155),l=s(2115),r=s(3592);function i(e){let{userId:t,onAllRead:s}=e,[i,n]=(0,l.useState)([]),[o,c]=(0,l.useState)(0),[d,m]=(0,l.useState)(!0);(0,l.useEffect)(()=>{t&&x()},[t]);let x=async()=>{try{m(!0);let e=await (0,r.AX)(t);n(e),0===e.length&&s()}catch(e){console.error("Error loading notifications:",e),s()}finally{m(!1)}},u=async()=>{let e=i[o];(null==e?void 0:e.id)&&(await (0,r.bA)(e.id,t),o<i.length-1?c(o+1):s())};if(d)return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,a.jsx)("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})})});if(0===i.length)return null;let h=i[o];return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("i",{className:(e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}})(h.type)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-bold",children:"Important Notice"}),(0,a.jsxs)("p",{className:"text-blue-100 text-sm",children:[o+1," of ",i.length," notifications"]})]})]}),(0,a.jsx)("div",{className:"bg-white bg-opacity-20 rounded-full px-3 py-1",children:(0,a.jsx)("span",{className:"text-sm font-medium",children:"Required"})})]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:h.title}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,a.jsx)("p",{className:"text-gray-800 leading-relaxed",children:h.message})}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-6",children:[(0,a.jsxs)("span",{children:["From: ",h.createdBy]}),(0,a.jsx)("span",{children:(e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);return t<60?"Just now":t<3600?"".concat(Math.floor(t/60)," minutes ago"):t<86400?"".concat(Math.floor(t/3600)," hours ago"):"".concat(Math.floor(t/86400)," days ago")})(h.createdAt)})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,a.jsx)("span",{children:"Progress"}),(0,a.jsxs)("span",{children:[o+1,"/",i.length]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat((o+1)/i.length*100,"%")}})})]}),(0,a.jsxs)("button",{onClick:u,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,a.jsx)("i",{className:"fas fa-check"}),(0,a.jsx)("span",{children:o<i.length-1?"Acknowledge & Continue":"Acknowledge & Proceed"})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-t",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)("i",{className:"fas fa-info-circle"}),(0,a.jsx)("span",{children:"You must acknowledge all notifications to continue"})]})})]})})}},8926:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(5155),l=s(2115),r=s(4752),i=s.n(r);function n(e){let{variant:t="homepage",className:s=""}=e,{isInstallable:r,isInstalled:n,installApp:o,getInstallInstructions:c}=function(){let[e,t]=(0,l.useState)(null),[s,a]=(0,l.useState)(!1),[r,i]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{let e=e=>{e.preventDefault(),t(e),a(!0)},s=()=>{i(!0),a(!1),t(null)};return window.matchMedia("(display-mode: standalone)").matches&&i(!0),window.addEventListener("beforeinstallprompt",e),window.addEventListener("appinstalled",s),()=>{window.removeEventListener("beforeinstallprompt",e),window.removeEventListener("appinstalled",s)}},[]),{isInstallable:s,isInstalled:r,installApp:async()=>{if(!e)return!1;try{await e.prompt();let{outcome:s}=await e.userChoice;if("accepted"===s)return i(!0),a(!1),t(null),!0;return!1}catch(e){return console.error("Error installing app:",e),!1}},getInstallInstructions:()=>{let e=navigator.userAgent.toLowerCase();return e.includes("chrome")&&!e.includes("edg")?{browser:"Chrome",steps:["Click the install button above","Or click the install icon in the address bar",'Click "Install" in the popup']}:e.includes("firefox")?{browser:"Firefox",steps:["Click the menu button (☰)",'Select "Install this site as an app"','Click "Install" in the dialog']}:e.includes("safari")?{browser:"Safari",steps:["Tap the Share button",'Scroll down and tap "Add to Home Screen"','Tap "Add" to install']}:e.includes("edg")?{browser:"Edge",steps:["Click the install button above","Or click the app icon in the address bar",'Click "Install" in the popup']}:{browser:"Your Browser",steps:["Look for an install option in your browser menu","Or check the address bar for an install icon","Follow your browser's installation prompts"]}}}}(),[d,m]=(0,l.useState)(!1),x=async()=>{await o()?i().fire({icon:"success",title:"App Installed!",text:"Instra Global has been installed on your device. You can now access it from your home screen.",timer:3e3,showConfirmButton:!1}):r||m(!0)},u=()=>{let e=c();i().fire({title:"Install Instra Global on ".concat(e.browser),html:'\n        <div class="text-left">\n          <p class="mb-4 text-gray-600">Follow these steps to install Instra Global as an app:</p>\n          <ol class="list-decimal list-inside space-y-2">\n            '.concat(e.steps.map(e=>'<li class="text-gray-700">'.concat(e,"</li>")).join(""),'\n          </ol>\n          <div class="mt-6 p-4 bg-blue-50 rounded-lg">\n            <p class="text-sm text-blue-800">\n              <i class="fas fa-info-circle mr-2"></i>\n              Installing the app gives you faster access, offline capabilities, and a native app experience!\n            </p>\n          </div>\n        </div>\n      '),confirmButtonText:"Got it!",confirmButtonColor:"#6A11CB"})};return n?(0,a.jsx)("div",{className:"".concat(s),children:"homepage"===t?(0,a.jsxs)("div",{className:"glass-card p-6 text-center",children:[(0,a.jsx)("i",{className:"fas fa-check-circle text-4xl text-green-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"App Installed!"}),(0,a.jsx)("p",{className:"text-white/80",children:"MyTube is installed on your device"})]}):(0,a.jsxs)("div",{className:"flex items-center text-green-400",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-2"}),(0,a.jsx)("span",{className:"text-sm",children:"App Installed"})]})}):"homepage"===t?(0,a.jsxs)("div",{className:"glass-card p-8 hover:scale-105 transition-transform ".concat(s),children:[(0,a.jsx)("i",{className:"fas fa-mobile-alt text-5xl text-purple-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"Install MyTube App"}),(0,a.jsx)("p",{className:"text-white/80 mb-6",children:"Get the best experience with our mobile app"}),(0,a.jsx)("div",{className:"space-y-3",children:r?(0,a.jsxs)("button",{onClick:x,className:"w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Install Now"]}):(0,a.jsxs)("button",{onClick:u,className:"w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How to Install"]})}),(0,a.jsx)("div",{className:"mt-4 text-white/60 text-sm",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-bolt mr-1"}),"Faster"]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-wifi mr-1"}),"Offline"]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-home mr-1"}),"Home Screen"]})]})})]}):(0,a.jsx)("div",{className:"glass-card p-4 ".concat(s),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-mobile-alt text-purple-400 text-xl mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Install Instra Global App"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Get faster access & offline features"})]})]}),r?(0,a.jsxs)("button",{onClick:x,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-download mr-1"}),"Install"]}):(0,a.jsxs)("button",{onClick:u,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-1"}),"How to"]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6874,3063,6681,3592,8441,1684,7358],()=>t(5604)),_N_E=e.O()}]);