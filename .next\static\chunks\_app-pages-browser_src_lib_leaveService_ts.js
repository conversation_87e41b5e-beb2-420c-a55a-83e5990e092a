"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_leaveService_ts"],{

/***/ "(app-pages-browser)/./src/lib/leaveService.ts":
/*!*********************************!*\
  !*** ./src/lib/leaveService.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyUserLeave: () => (/* binding */ applyUserLeave),\n/* harmony export */   cancelUserLeave: () => (/* binding */ cancelUserLeave),\n/* harmony export */   createAdminLeave: () => (/* binding */ createAdminLeave),\n/* harmony export */   debugAdminLeaveStatus: () => (/* binding */ debugAdminLeaveStatus),\n/* harmony export */   deleteAdminLeave: () => (/* binding */ deleteAdminLeave),\n/* harmony export */   getAdminLeaves: () => (/* binding */ getAdminLeaves),\n/* harmony export */   getAllUserLeaves: () => (/* binding */ getAllUserLeaves),\n/* harmony export */   getUserLeaves: () => (/* binding */ getUserLeaves),\n/* harmony export */   getUserMonthlyLeaveCount: () => (/* binding */ getUserMonthlyLeaveCount),\n/* harmony export */   isAdminLeaveDay: () => (/* binding */ isAdminLeaveDay),\n/* harmony export */   isUserOnLeave: () => (/* binding */ isUserOnLeave),\n/* harmony export */   isWorkBlocked: () => (/* binding */ isWorkBlocked),\n/* harmony export */   updateUserLeaveStatus: () => (/* binding */ updateUserLeaveStatus)\n/* harmony export */ });\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n\n\nconst COLLECTIONS = {\n    adminLeaves: 'adminLeaves',\n    userLeaves: 'userLeaves'\n};\n// Admin Leave Functions\nasync function createAdminLeave(leaveData) {\n    try {\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves), {\n            ...leaveData,\n            date: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(leaveData.date),\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n        });\n        return docRef.id;\n    } catch (error) {\n        console.error('Error creating admin leave:', error);\n        throw error;\n    }\n}\nasync function getAdminLeaves() {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.orderBy)('date', 'asc'));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const leaves = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date.toDate(),\n                createdAt: doc.data().createdAt.toDate()\n            }));\n        console.log('📅 All admin leaves:', leaves);\n        return leaves;\n    } catch (error) {\n        console.error('Error getting admin leaves:', error);\n        throw error;\n    }\n}\n// Debug function to check current admin leave status\nasync function debugAdminLeaveStatus() {\n    try {\n        const today = new Date();\n        console.log('🔍 Debug: Checking admin leave status for today:', today.toDateString());\n        const isLeave = await isAdminLeaveDay(today);\n        console.log('📊 Debug: Admin leave result:', isLeave);\n        const allLeaves = await getAdminLeaves();\n        console.log('📅 Debug: All admin leaves in database:', allLeaves);\n        const todayLeaves = allLeaves.filter((leave)=>leave.date.toDateString() === today.toDateString());\n        console.log('📅 Debug: Today\\'s admin leaves:', todayLeaves);\n    } catch (error) {\n        console.error('❌ Debug: Error checking admin leave status:', error);\n    }\n}\nasync function deleteAdminLeave(leaveId) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves, leaveId));\n    } catch (error) {\n        console.error('Error deleting admin leave:', error);\n        throw error;\n    }\n}\nasync function isAdminLeaveDay(date) {\n    try {\n        const startOfDay = new Date(date);\n        startOfDay.setHours(0, 0, 0, 0);\n        const endOfDay = new Date(date);\n        endOfDay.setHours(23, 59, 59, 999);\n        console.log('🔍 Checking admin leave for date range:', startOfDay.toISOString(), 'to', endOfDay.toISOString());\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.adminLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '>=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(startOfDay)), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '<=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(endOfDay)));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const hasAdminLeave = !querySnapshot.empty;\n        if (hasAdminLeave) {\n            console.log('📅 Found admin leave(s) for today:', querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data(),\n                    date: doc.data().date.toDate()\n                })));\n        } else {\n            console.log('📅 No admin leaves found for today');\n        }\n        return hasAdminLeave;\n    } catch (error) {\n        console.error('❌ Error checking admin leave day:', error);\n        // Return false (no leave) on error to avoid blocking work unnecessarily\n        return false;\n    }\n}\n// User Leave Functions\nasync function applyUserLeave(leaveData) {\n    try {\n        // Check if user has available leave quota for automatic approval\n        const currentDate = new Date();\n        const currentYear = currentDate.getFullYear();\n        const currentMonth = currentDate.getMonth() + 1;\n        const usedLeaves = await getUserMonthlyLeaveCount(leaveData.userId, currentYear, currentMonth);\n        const maxLeaves = 4 // Monthly leave quota\n        ;\n        // Determine status and approval details\n        let status = 'pending';\n        let reviewedBy;\n        let reviewedAt = undefined;\n        let reviewNotes;\n        // Auto-approve if user has available quota\n        if (usedLeaves < maxLeaves) {\n            status = 'approved';\n            reviewedBy = 'system';\n            reviewedAt = firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now();\n            reviewNotes = \"Auto-approved: \".concat(usedLeaves + 1, \"/\").concat(maxLeaves, \" monthly leaves used\");\n        }\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), {\n            ...leaveData,\n            date: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(leaveData.date),\n            status,\n            appliedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now(),\n            ...reviewedBy && {\n                reviewedBy\n            },\n            ...reviewedAt && {\n                reviewedAt\n            },\n            ...reviewNotes && {\n                reviewNotes\n            }\n        });\n        return {\n            id: docRef.id,\n            autoApproved: status === 'approved',\n            usedLeaves: usedLeaves + (status === 'approved' ? 1 : 0),\n            maxLeaves\n        };\n    } catch (error) {\n        console.error('Error applying user leave:', error);\n        throw error;\n    }\n}\nasync function getUserLeaves(userId) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.orderBy)('date', 'desc'));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        return querySnapshot.docs.map((doc)=>{\n            var _doc_data_reviewedAt;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date.toDate(),\n                appliedAt: doc.data().appliedAt.toDate(),\n                reviewedAt: (_doc_data_reviewedAt = doc.data().reviewedAt) === null || _doc_data_reviewedAt === void 0 ? void 0 : _doc_data_reviewedAt.toDate()\n            };\n        });\n    } catch (error) {\n        console.error('Error getting user leaves:', error);\n        throw error;\n    }\n}\n// Get all user leaves for admin review\nasync function getAllUserLeaves() {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.orderBy)('appliedAt', 'desc'));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const leaves = querySnapshot.docs.map((doc)=>{\n            var _doc_data_reviewedAt;\n            return {\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date.toDate(),\n                appliedAt: doc.data().appliedAt.toDate(),\n                reviewedAt: (_doc_data_reviewedAt = doc.data().reviewedAt) === null || _doc_data_reviewedAt === void 0 ? void 0 : _doc_data_reviewedAt.toDate()\n            };\n        });\n        // Get user details for each leave\n        const { getUserData } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\"));\n        for (const leave of leaves){\n            try {\n                const userData = await getUserData(leave.userId);\n                if (userData) {\n                    leave.userName = userData.name;\n                    leave.userEmail = userData.email;\n                }\n            } catch (error) {\n                console.error(\"Error getting user data for \".concat(leave.userId, \":\"), error);\n                leave.userName = 'Unknown User';\n                leave.userEmail = '<EMAIL>';\n            }\n        }\n        return leaves;\n    } catch (error) {\n        console.error('Error getting all user leaves:', error);\n        throw error;\n    }\n}\nasync function updateUserLeaveStatus(leaveId, status, reviewedBy, reviewNotes) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves, leaveId), {\n            status,\n            reviewedBy,\n            reviewedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now(),\n            reviewNotes: reviewNotes || ''\n        });\n    } catch (error) {\n        console.error('Error updating user leave status:', error);\n        throw error;\n    }\n}\nasync function cancelUserLeave(leaveId) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves, leaveId));\n    } catch (error) {\n        console.error('Error cancelling user leave:', error);\n        throw error;\n    }\n}\nasync function getUserMonthlyLeaveCount(userId, year, month) {\n    try {\n        const startOfMonth = new Date(year, month - 1, 1);\n        const endOfMonth = new Date(year, month, 0, 23, 59, 59, 999);\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('status', '==', 'approved'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '>=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(startOfMonth)), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '<=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(endOfMonth)));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        return querySnapshot.size;\n    } catch (error) {\n        console.error('Error getting user monthly leave count:', error);\n        return 0;\n    }\n}\nasync function isUserOnLeave(userId, date) {\n    try {\n        const startOfDay = new Date(date);\n        startOfDay.setHours(0, 0, 0, 0);\n        const endOfDay = new Date(date);\n        endOfDay.setHours(23, 59, 59, 999);\n        console.log('🔍 Checking user leave for user:', userId, 'on date range:', startOfDay.toISOString(), 'to', endOfDay.toISOString());\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.userLeaves), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('status', '==', 'approved'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '>=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(startOfDay)), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('date', '<=', firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(endOfDay)));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const hasUserLeave = !querySnapshot.empty;\n        if (hasUserLeave) {\n            console.log('👤 Found user leave(s) for today:', querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data(),\n                    date: doc.data().date.toDate()\n                })));\n        } else {\n            console.log('👤 No user leaves found for today');\n        }\n        return hasUserLeave;\n    } catch (error) {\n        console.error('❌ Error checking user leave day:', error);\n        // Return false (no leave) on error to avoid blocking work unnecessarily\n        return false;\n    }\n}\n// REMOVED: calculateActiveDays function has been removed\n// Use ActiveDaysService.calculateActiveDays() from activeDaysService.ts instead\n// Check if work/withdrawals should be blocked\nasync function isWorkBlocked(userId) {\n    try {\n        const today = new Date();\n        console.log('🔍 Checking work block status for user:', userId, 'on date:', today.toDateString());\n        // Check admin leave with detailed logging\n        try {\n            const isAdminLeave = await isAdminLeaveDay(today);\n            console.log('📅 Admin leave check result:', isAdminLeave);\n            if (isAdminLeave) {\n                console.log('🚫 Work blocked due to admin leave');\n                return {\n                    blocked: true,\n                    reason: 'System maintenance/holiday'\n                };\n            }\n        } catch (adminLeaveError) {\n            console.error('❌ Error checking admin leave (allowing work to continue):', adminLeaveError);\n        // Don't block work if admin leave check fails\n        }\n        // Check user leave with detailed logging\n        try {\n            const isUserLeave = await isUserOnLeave(userId, today);\n            console.log('👤 User leave check result:', isUserLeave);\n            if (isUserLeave) {\n                console.log('🚫 Work blocked due to user leave');\n                return {\n                    blocked: true,\n                    reason: 'You are on approved leave today'\n                };\n            }\n        } catch (userLeaveError) {\n            console.error('❌ Error checking user leave (allowing work to continue):', userLeaveError);\n        // Don't block work if user leave check fails\n        }\n        console.log('✅ Work is not blocked');\n        return {\n            blocked: false\n        };\n    } catch (error) {\n        console.error('❌ Error checking work block status (allowing work to continue):', error);\n        return {\n            blocked: false\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/leaveService.ts\n"));

/***/ })

}]);