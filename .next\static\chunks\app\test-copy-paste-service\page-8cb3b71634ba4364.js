(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1607,7718],{7050:(e,s,t)=>{Promise.resolve().then(t.bind(t,7053))},7053:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(5155),a=t(2115),i=t(6681),o=t(7718);function n(){let{user:e,loading:s}=(0,i.Nu)(),[t,n]=(0,a.useState)(""),[c,l]=(0,a.useState)(null),[d,u]=(0,a.useState)(!1),m=async()=>{if(!t.trim())return void alert("Please enter a user ID");u(!0),l(null);try{console.log("\uD83E\uDDEA Testing copy-paste service for user: ".concat(t)),console.log("1. Testing checkCopyPastePermission...");let e=await (0,o.checkCopyPastePermission)(t.trim());console.log("✅ checkCopyPastePermission result:",e),console.log("2. Testing reduceCopyPasteDays...");let s=await (0,o.i7)(t.trim());console.log("✅ reduceCopyPasteDays result:",s),l({success:!0,permission:e,reduction:s,message:"Single user test completed successfully"})}catch(e){console.error("❌ Error in single user test:",e),l({success:!1,error:e.message,message:"Single user test failed"})}finally{u(!1)}},p=async()=>{u(!0),l(null);try{console.log("\uD83E\uDDEA Testing processAllUsersCopyPasteReduction...");let e=await (0,o.Mk)();console.log("✅ processAllUsersCopyPasteReduction result:",e),l({success:!0,allUsersResult:e,message:"All users test completed successfully"})}catch(e){console.error("❌ Error in all users test:",e),l({success:!1,error:e.message,stack:e.stack,message:"All users test failed"})}finally{u(!1)}};return s?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,r.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,r.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,r.jsxs)("div",{className:"glass-card p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Copy-Paste Service"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white mb-2",children:"User ID (for single user test):"}),(0,r.jsx)("input",{type:"text",value:t,onChange:e=>n(e.target.value),className:"w-full p-3 rounded-lg bg-white/10 text-white border border-white/20",placeholder:"Enter user ID"})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{onClick:m,disabled:d,className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:d?"Testing...":"Test Single User"}),(0,r.jsx)("button",{onClick:p,disabled:d,className:"flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:d?"Testing...":"Test All Users"})]}),c&&(0,r.jsxs)("div",{className:"p-4 rounded-lg ".concat(c.success?"bg-green-500/20 border border-green-500/30":"bg-red-500/20 border border-red-500/30"),children:[(0,r.jsx)("h3",{className:"font-bold ".concat(c.success?"text-green-400":"text-red-400"),children:c.success?"Success!":"Failed"}),(0,r.jsx)("p",{className:"text-white mt-2",children:c.message}),c.permission&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Permission Check:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(c.permission,null,2)})]}),c.reduction&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Reduction Result:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(c.reduction,null,2)})]}),c.allUsersResult&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"All Users Result:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(c.allUsersResult,null,2)})]}),c.error&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-red-500/20 rounded",children:[(0,r.jsx)("h4",{className:"text-red-400 font-semibold",children:"Error Details:"}),(0,r.jsx)("p",{className:"text-white/80 text-sm mt-1",children:c.error}),c.stack&&(0,r.jsx)("pre",{className:"text-white/60 text-xs mt-2 overflow-auto",children:c.stack})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 p-4 bg-white/5 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,r.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• Enter a user ID to test single user functions"}),(0,r.jsx)("li",{children:'• Click "Test Single User" to test permission check and reduction'}),(0,r.jsx)("li",{children:'• Click "Test All Users" to test the batch processing function'}),(0,r.jsx)("li",{children:"• Check browser console for detailed logs"})]})]})]})})})}},7718:(e,s,t)=>{"use strict";t.d(s,{Mk:()=>m,checkCopyPastePermission:()=>c,grantCopyPastePermission:()=>l,i7:()=>u,removeCopyPastePermission:()=>d});var r=t(6104),a=t(5317);let i={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},o={users:"users"};class n{static async checkCopyPastePermission(e){try{let s=await (0,a.x7)((0,a.H9)(r.db,o.users,e));if(!s.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let t=s.data()[i.quickTranslationAdvantageExpiry];if(!t)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let n=t.toDate(),c=new Date,l=n>c,d=l?Math.ceil((n.getTime()-c.getTime())/864e5):0;return{hasPermission:l,daysRemaining:d,expiryDate:n}}catch(s){return console.error("Error checking copy-paste permission for user ".concat(e,":"),s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,s){try{let t=new Date;t.setDate(t.getDate()+s);let n=(0,a.H9)(r.db,o.users,e);await (0,a.mZ)(n,{[i.quickTranslationAdvantageExpiry]:a.Dc.fromDate(t),[i.lastCopyPasteReduction]:a.Dc.now()}),console.log("✅ Granted copy-paste permission to user ".concat(e," for ").concat(s," days (expires: ").concat(t.toDateString(),")"))}catch(s){throw console.error("Error granting copy-paste permission to user ".concat(e,":"),s),s}}static async removeCopyPastePermission(e){try{let s=(0,a.H9)(r.db,o.users,e);await (0,a.mZ)(s,{[i.quickTranslationAdvantageExpiry]:null}),console.log("✅ Removed copy-paste permission from user ".concat(e))}catch(s){throw console.error("Error removing copy-paste permission from user ".concat(e,":"),s),s}}static async reduceCopyPasteDays(e){try{let s=await (0,a.x7)((0,a.H9)(r.db,o.users,e));if(!s.exists())return{reduced:!1,daysRemaining:0,expired:!1};let t=s.data(),n=t[i.quickTranslationAdvantageExpiry],c=t[i.lastCopyPasteReduction];if(!n)return{reduced:!1,daysRemaining:0,expired:!1};let l=new Date().toDateString();if((c?c.toDate().toDateString():null)===l){let e=n.toDate(),s=new Date,t=Math.max(0,Math.ceil((e.getTime()-s.getTime())/864e5));return{reduced:!1,daysRemaining:t,expired:0===t}}let d=n.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let m=(0,a.H9)(r.db,o.users,e);if(u<=new Date)return await (0,a.mZ)(m,{[i.quickTranslationAdvantageExpiry]:null,[i.lastCopyPasteReduction]:a.Dc.now()}),console.log("\uD83D\uDCC5 Copy-paste permission expired for user ".concat(e)),{reduced:!0,daysRemaining:0,expired:!0};{await (0,a.mZ)(m,{[i.quickTranslationAdvantageExpiry]:a.Dc.fromDate(u),[i.lastCopyPasteReduction]:a.Dc.now()});let s=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log("\uD83D\uDCC5 Reduced copy-paste days for user ".concat(e,": ").concat(s," days remaining")),{reduced:!0,daysRemaining:s,expired:!1}}}catch(s){return console.error("Error reducing copy-paste days for user ".concat(e,":"),s),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,a.getDocs)((0,a.collection)(r.db,o.users)),s=0,t=0,i=0,c=0;for(let r of e.docs)try{s++;let e=await n.reduceCopyPasteDays(r.id);e.reduced&&(t++,e.expired&&i++)}catch(e){c++,console.error("Error processing copy-paste reduction for user ".concat(r.id,":"),e)}return console.log("✅ Daily copy-paste reduction complete:"),console.log("   - Processed: ".concat(s," users")),console.log("   - Reduced: ".concat(t," users")),console.log("   - Expired: ".concat(i," users")),console.log("   - Errors: ".concat(c," users")),{processed:s,reduced:t,expired:i,errors:c}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let s=await n.checkCopyPastePermission(e);return{hasPermission:s.hasPermission,daysRemaining:s.daysRemaining,expiryDate:s.expiryDate?s.expiryDate.toDateString():null}}catch(s){return console.error("Error getting copy-paste status for user ".concat(e,":"),s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let c=n.checkCopyPastePermission,l=n.grantCopyPastePermission,d=n.removeCopyPastePermission,u=n.reduceCopyPasteDays,m=n.processAllUsersCopyPasteReduction;n.getCopyPasteStatus}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,8818,6681,8441,1684,7358],()=>s(7050)),_N_E=e.O()}]);