"use strict";exports.id=3582,exports.ids=[3582],exports.modules={3582:(e,t,r)=>{r.d(t,{$T:()=>y,AX:()=>P,COLLECTIONS:()=>i,HY:()=>w,I0:()=>g,II:()=>v,IK:()=>A,QD:()=>M,Ss:()=>B,Yr:()=>o,_I:()=>T,_f:()=>N,addTransaction:()=>u,b6:()=>d,bA:()=>U,fP:()=>I,getPlanValidityDays:()=>D,getUserData:()=>s,getUserVideoSettings:()=>E,getVideoCountData:()=>c,getWalletData:()=>l,gj:()=>p,i8:()=>H,iA:()=>_,mm:()=>b,mv:()=>q,pl:()=>f,ul:()=>x,updateWalletBalance:()=>m,w1:()=>k,wT:()=>$,x4:()=>G,xj:()=>R,z8:()=>S,zb:()=>h});var a=r(75535),n=r(33784);let o={name:"name",email:"email",mobile:"mobile",referralCode:"referralCode",referredBy:"referredBy",referralBonusCredited:"referralBonusCredited",plan:"plan",planExpiry:"planExpiry",activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate",joinedDate:"joinedDate",wallet:"wallet",bankAccountHolderName:"bankAccountHolderName",bankAccountNumber:"bankAccountNumber",bankIfscCode:"bankIfscCode",bankName:"bankName",bankDetailsUpdated:"bankDetailsUpdated",totalTranslations:"totalTranslations",todayTranslations:"todayTranslations",lastTranslationDate:"lastTranslationDate",translationDuration:"translationDuration",quickTranslationAdvantage:"quickTranslationAdvantage",quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",quickTranslationAdvantageDays:"quickTranslationAdvantageDays",quickTranslationAdvantageSeconds:"quickTranslationAdvantageSeconds",quickTranslationAdvantageGrantedBy:"quickTranslationAdvantageGrantedBy",quickTranslationAdvantageGrantedAt:"quickTranslationAdvantageGrantedAt",type:"type",amount:"amount",date:"date",status:"status",description:"description",userId:"userId"},i={users:"users",transactions:"transactions",withdrawals:"withdrawals",plans:"plans",settings:"settings",notifications:"notifications",adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function s(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUserData:",e),null;let t=await (0,a.x7)((0,a.H9)(n.db,i.users,e));if(t.exists()){let e=t.data(),r={name:String(e[o.name]||""),email:String(e[o.email]||""),mobile:String(e[o.mobile]||""),referralCode:String(e[o.referralCode]||""),referredBy:String(e[o.referredBy]||""),referralBonusCredited:!!e[o.referralBonusCredited],plan:String(e[o.plan]||"Trial"),planExpiry:e[o.planExpiry]?.toDate()||null,activeDays:Number(e[o.activeDays]||0),lastActiveDayUpdate:e[o.lastActiveDayUpdate]?.toDate()||null,joinedDate:e[o.joinedDate]?.toDate()||new Date,translationDuration:Number(e[o.translationDuration]||("Trial"===e[o.plan]?30:300)),quickTranslationAdvantage:!!e[o.quickTranslationAdvantage],quickTranslationAdvantageExpiry:e[o.quickTranslationAdvantageExpiry]?.toDate()||null,quickTranslationAdvantageDays:Number(e[o.quickTranslationAdvantageDays]||0),quickTranslationAdvantageSeconds:Number(e[o.quickTranslationAdvantageSeconds]||30),quickTranslationAdvantageGrantedBy:String(e[o.quickTranslationAdvantageGrantedBy]||""),quickTranslationAdvantageGrantedAt:e[o.quickTranslationAdvantageGrantedAt]?.toDate()||null};return console.log("getUserData result:",r),r}return null}catch(e){return console.error("Error getting user data:",e),null}}async function l(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getWalletData:",e),{wallet:0};let t=await (0,a.x7)((0,a.H9)(n.db,i.users,e));if(t.exists()){let e=t.data(),r={wallet:Number(e[o.wallet]||0)};return console.log("getWalletData result:",r),r}return{wallet:0}}catch(e){return console.error("Error getting wallet data:",e),{wallet:0}}}async function c(e){try{let t=(0,a.H9)(n.db,i.users,e),r=await (0,a.x7)(t);if(r.exists()){let n=r.data(),i=n[o.totalTranslations]||0,s=n[o.todayTranslations]||0,l=n[o.lastTranslationDate]?.toDate(),c=new Date;return(!l||l.toDateString()!==c.toDateString())&&s>0&&(console.log(`🔄 Resetting daily translation count for user ${e} (was ${s})`),await (0,a.mZ)(t,{[o.todayTranslations]:0}),s=0,console.log("\uD83D\uDCC5 Active days will be updated by centralized scheduler")),{totalTranslations:i,todayTranslations:s,remainingTranslations:Math.max(0,50-s)}}return{totalTranslations:0,todayTranslations:0,remainingTranslations:50}}catch(e){throw console.error("Error getting video count data:",e),e}}async function d(e,t){try{await (0,a.mZ)((0,a.H9)(n.db,i.users,e),t)}catch(e){throw console.error("Error updating user data:",e),e}}async function u(e,t){try{let r={[o.userId]:e,[o.type]:t.type,[o.amount]:t.amount,[o.description]:t.description,[o.status]:t.status||"completed",[o.date]:a.Dc.now()};await (0,a.gS)((0,a.collection)(n.db,i.transactions),r)}catch(e){throw console.error("Error adding transaction:",e),e}}async function g(e,t=10){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getTransactions:",e),[];let r=(0,a.P)((0,a.collection)(n.db,i.transactions),(0,a._M)(o.userId,"==",e),(0,a.AB)(t)),s=(await (0,a.getDocs)(r)).docs.map(e=>({id:e.id,...e.data(),date:e.data()[o.date]?.toDate()}));return s.sort((e,t)=>{let r=e.date||new Date(0);return(t.date||new Date(0)).getTime()-r.getTime()}),s}catch(e){return console.error("Error getting transactions:",e),[]}}async function f(e){try{let t=(0,a.P)((0,a.collection)(n.db,i.users),(0,a._M)(o.referredBy,"==",e));return(await (0,a.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[o.joinedDate]?.toDate()}))}catch(e){throw console.error("Error getting referrals:",e),e}}async function y(e){try{if(!e)return 0;let t=(0,a.P)((0,a.collection)(n.db,i.users),(0,a._M)(o.referredBy,"==",e));return(await (0,a.getDocs)(t)).size}catch(e){return console.error("Error getting referral count:",e),0}}async function w(e){try{let t=(0,a.H9)(n.db,i.users,e);await (0,a.mZ)(t,{[o.todayTranslations]:0}),console.log(`✅ Reset daily translation count for user ${e}`)}catch(e){throw console.error("Error resetting daily translation count:",e),e}}async function p(){try{console.log("\uD83D\uDD27 Starting to fix all users active days...");let{ActiveDaysService:e}=await r.e(1705).then(r.bind(r,51705)),t=await (0,a.getDocs)((0,a.collection)(n.db,i.users)),o=0,s=0;for(let r of t.docs)try{await e.updateUserActiveDays(r.id),o++}catch(e){console.error(`Error fixing active days for user ${r.id}:`,e),s++}return console.log(`✅ Fixed active days for ${o} users, ${s} errors`),{fixedCount:o,errorCount:s}}catch(e){throw console.error("Error fixing all users active days:",e),e}}async function m(e,t){try{let r=(0,a.H9)(n.db,i.users,e);await (0,a.mZ)(r,{[o.wallet]:(0,a.GV)(t)})}catch(e){throw console.error("Error updating wallet balance:",e),e}}async function b(e,t){try{if(!e||"string"!=typeof e)throw Error("Invalid userId provided");var r=t;let{accountHolderName:s,accountNumber:l,ifscCode:c,bankName:d}=r;if(!s||s.trim().length<2)throw Error("Account holder name must be at least 2 characters long");if(!l||!/^\d{9,18}$/.test(l.trim()))throw Error("Account number must be 9-18 digits");if(!c||!/^[A-Z]{4}0[A-Z0-9]{6}$/.test(c.trim().toUpperCase()))throw Error("Invalid IFSC code format (e.g., SBIN0001234)");if(!d||d.trim().length<2)throw Error("Bank name must be at least 2 characters long");let u=(0,a.H9)(n.db,i.users,e);await (0,a.mZ)(u,{[o.bankAccountHolderName]:t.accountHolderName.trim(),[o.bankAccountNumber]:t.accountNumber.trim(),[o.bankIfscCode]:t.ifscCode.trim().toUpperCase(),[o.bankName]:t.bankName.trim(),[o.bankDetailsUpdated]:a.Dc.now()}),console.log("Bank details saved successfully for user:",e)}catch(e){throw console.error("Error saving bank details:",e),e}}async function h(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getBankDetails:",e),null;let t=await (0,a.x7)((0,a.H9)(n.db,i.users,e));if(t.exists()){let e=t.data();if(e[o.bankAccountNumber]){let t={accountHolderName:String(e[o.bankAccountHolderName]||""),accountNumber:String(e[o.bankAccountNumber]||""),ifscCode:String(e[o.bankIfscCode]||""),bankName:String(e[o.bankName]||"")};return console.log("getBankDetails result found"),t}}return console.log("No bank details found for user"),null}catch(e){return console.error("Error getting bank details:",e),null}}function D(e){return({Trial:2,Junior:30,Senior:30,Expert:30,Starter:30,Basic:30,Premium:30,Gold:30,Platinum:30,Diamond:30,499:30,1499:30,2999:30,3999:30,5999:30,9999:30})[e]||2}async function v(e,t,r){try{let s=(0,a.H9)(n.db,i.users,e);if("Trial"===t)await (0,a.mZ)(s,{[o.planExpiry]:null});else{let n;if(r)n=r;else{let e=D(t),r=new Date;n=new Date(r.getTime()+24*e*36e5)}await (0,a.mZ)(s,{[o.planExpiry]:a.Dc.fromDate(n)}),console.log(`Updated plan expiry for user ${e} to ${n.toDateString()}`)}}catch(e){throw console.error("Error updating plan expiry:",e),e}}async function A(e,t,r){try{if(console.log(`🎯 REFERRAL BONUS DEBUG: Starting process for user ${e}`),console.log(`🎯 Plan change: ${t} → ${r}`),"Trial"!==t||"Trial"===r){console.log("❌ Referral bonus only applies when upgrading from Trial to paid plan"),console.log(`❌ Current conditions: oldPlan=${t}, newPlan=${r}`);return}console.log(`✅ Processing referral bonus for user ${e} upgrading from ${t} to ${r}`);let s=await (0,a.x7)((0,a.H9)(n.db,i.users,e));if(!s.exists())return void console.log("❌ User not found in database");let l=s.data(),c=l[o.referredBy],d=l[o.referralBonusCredited];if(console.log(`🔍 User referral data:`),console.log(`   - Referred by: ${c||"None"}`),console.log(`   - Already credited: ${d}`),console.log(`   - User name: ${l[o.name]}`),!c)return void console.log("❌ User was not referred by anyone, skipping bonus processing");if(d)return void console.log("❌ Referral bonus already credited for this user, skipping");console.log(`🔍 Finding referrer with code: ${c}`);let g=(0,a.P)((0,a.collection)(n.db,i.users),(0,a._M)(o.referralCode,"==",c),(0,a.AB)(1)),f=await (0,a.getDocs)(g);if(f.empty){console.log(`❌ Referral code not found: ${c}`),console.log("❌ No referrer found with this code in database");return}let y=f.docs[0],w=y.id,p=y.data(),b={Trial:0,Junior:300,Senior:700,Expert:1200,499:50,1499:150,2999:300,3999:400,5999:700,9999:1200,Starter:50,Basic:150,Premium:300,Gold:400,Platinum:700,Diamond:1200}[r]||0;if(console.log(`✅ Found referrer:`),console.log(`   - Referrer ID: ${w}`),console.log(`   - Referrer name: ${p[o.name]}`),console.log(`   - Bonus amount: ₹${b}`),console.log(`   - Plan: ${r}`),b>0){console.log(`💰 Processing bonus payment...`),await m(w,b),console.log(`✅ Added ₹${b} to referrer's wallet`);let t=(0,a.H9)(n.db,i.users,w);await (0,a.mZ)(t,{[o.totalTranslations]:(0,a.GV)(50)}),console.log(`✅ Added 50 bonus translations to referrer`);let s=(0,a.H9)(n.db,i.users,e);await (0,a.mZ)(s,{[o.referralBonusCredited]:!0}),console.log(`✅ Marked referral bonus as credited for user ${e}`),await u(w,{type:"referral_bonus",amount:b,description:`Referral bonus for ${r} plan upgrade + 50 bonus translations (User: ${l[o.name]})`}),console.log(`✅ Added transaction record for referral bonus`),console.log(`🎉 REFERRAL BONUS COMPLETED: ₹${b} + 50 translations for referrer ${w}`)}else console.log(`❌ No bonus amount calculated for plan: ${r}`),console.log(`❌ Available plans:`,["Trial","Junior","Senior","Expert"])}catch(e){console.error("❌ Error processing referral bonus:",e),console.error("❌ Error details:",e)}}async function T(e){try{console.log(`🔧 MANUAL REFERRAL BONUS: Processing for user ${e}`);let t=await s(e);if(!t)return console.log("❌ User not found"),{success:!1,message:"User not found"};if(console.log(`🔍 User data:`),console.log(`   - Name: ${t.name}`),console.log(`   - Plan: ${t.plan}`),console.log(`   - Referred by: ${t.referredBy||"None"}`),console.log(`   - Bonus credited: ${t.referralBonusCredited}`),"Trial"===t.plan)return{success:!1,message:"User is still on Trial plan. Upgrade to paid plan first."};if(!t.referredBy)return{success:!1,message:"User was not referred by anyone"};if(t.referralBonusCredited)return{success:!1,message:"Referral bonus already credited"};return await A(e,"Trial",t.plan),{success:!0,message:`Referral bonus processed for ${t.plan} plan`}}catch(e){return console.error("❌ Error in manual referral bonus processing:",e),{success:!1,message:`Error: ${e?.message||"Unknown error"}`}}}async function E(e){try{var t;let r=await s(e);if(!r)return{translationDuration:30,earningPerBatch:25,plan:"Trial",hasQuickAdvantage:!1};let a=!!(t=r).quickTranslationAdvantage&&!!t.quickTranslationAdvantageExpiry&&new Date<t.quickTranslationAdvantageExpiry,n=r.translationDuration;return a?n=r.quickTranslationAdvantageSeconds||30:n&&"Trial"!==r.plan||(n=({Trial:30,Starter:300,Basic:300,Premium:300,Gold:180,Platinum:120,Diamond:60})[r.plan]||30),{translationDuration:n,earningPerBatch:({Trial:25,Junior:150,Senior:250,Expert:400,Starter:25,Basic:75,Premium:150,Gold:200,Platinum:250,Diamond:400})[r.plan]||25,plan:r.plan,hasQuickAdvantage:a,quickAdvantageExpiry:r.quickTranslationAdvantageExpiry}}catch(e){return console.error("Error getting user translation settings:",e),{translationDuration:30,earningPerBatch:25,plan:"Trial",hasQuickAdvantage:!1}}}async function k(e,t,a,n=30){try{if(t<=0||t>365)throw Error("Days must be between 1 and 365");if(n<1||n>420)throw Error("Seconds must be between 1 and 420 (7 minutes)");let{grantCopyPastePermission:o}=await r.e(7878).then(r.bind(r,27878));return await o(e,t),console.log(`Granted copy-paste permission to user ${e} for ${t} days by ${a}`),await u(e,{type:"quick_advantage_granted",amount:0,description:`Copy-paste permission granted for ${t} days by ${a}`}),{success:!0}}catch(e){throw console.error("Error granting quick video advantage:",e),e}}async function $(e,t){try{let{removeCopyPastePermission:a}=await r.e(7878).then(r.bind(r,27878));return await a(e),console.log(`Removed copy-paste permission from user ${e} by ${t}`),await u(e,{type:"quick_advantage_removed",amount:0,description:`Copy-paste permission removed by ${t}`}),{success:!0}}catch(e){throw console.error("Error removing quick video advantage:",e),e}}async function S(e){try{let t={title:e.title,message:e.message,type:e.type,targetUsers:e.targetUsers,userIds:e.userIds||[],createdAt:a.Dc.now(),createdBy:e.createdBy};console.log("Adding notification to Firestore:",t);let r=await (0,a.gS)((0,a.collection)(n.db,i.notifications),t);console.log("Notification added successfully with ID:",r.id);let o=await (0,a.x7)(r);return o.exists()?console.log("Notification verified in database:",o.data()):console.warn("Notification not found after adding"),r.id}catch(e){throw console.error("Error adding notification:",e),e}}async function B(e,t=20){try{let r,o;if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUserNotifications:",e),[];console.log(`Loading notifications for user: ${e}`);try{let e=(0,a.P)((0,a.collection)(n.db,i.notifications),(0,a._M)("targetUsers","==","all"),(0,a.My)("createdAt","desc"),(0,a.AB)(t));r=await (0,a.getDocs)(e),console.log(`Found ${r.docs.length} notifications for all users`)}catch(o){console.warn("Error querying all users notifications, trying without orderBy:",o);let e=(0,a.P)((0,a.collection)(n.db,i.notifications),(0,a._M)("targetUsers","==","all"),(0,a.AB)(t));r=await (0,a.getDocs)(e)}try{let r=(0,a.P)((0,a.collection)(n.db,i.notifications),(0,a._M)("targetUsers","==","specific"),(0,a._M)("userIds","array-contains",e),(0,a.My)("createdAt","desc"),(0,a.AB)(t));o=await (0,a.getDocs)(r),console.log(`Found ${o.docs.length} notifications for specific user`)}catch(s){console.warn("Error querying specific user notifications, trying without orderBy:",s);let r=(0,a.P)((0,a.collection)(n.db,i.notifications),(0,a._M)("targetUsers","==","specific"),(0,a._M)("userIds","array-contains",e),(0,a.AB)(t));o=await (0,a.getDocs)(r)}let s=[];r.docs.forEach(e=>{s.push({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate()||new Date})}),o.docs.forEach(e=>{s.push({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate()||new Date})}),s.sort((e,t)=>t.createdAt.getTime()-e.createdAt.getTime());let l=s.slice(0,t);return console.log(`Returning ${l.length} total notifications for user`),l}catch(e){return console.error("Error getting user notifications:",e),[]}}async function N(e=50){try{let t=(0,a.P)((0,a.collection)(n.db,i.notifications),(0,a.My)("createdAt","desc"),(0,a.AB)(e));return(await (0,a.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate()||new Date}))}catch(e){return console.error("Error getting all notifications:",e),[]}}async function I(e){try{if(!e||"string"!=typeof e)throw Error("Invalid notification ID provided");console.log("Deleting notification:",e),await (0,a.kd)((0,a.H9)(n.db,i.notifications,e)),console.log("Notification deleted successfully")}catch(e){throw console.error("Error deleting notification:",e),e}}async function U(e,t){try{let r=JSON.parse(localStorage.getItem(`read_notifications_${t}`)||"[]");r.includes(e)||(r.push(e),localStorage.setItem(`read_notifications_${t}`,JSON.stringify(r)))}catch(e){console.error("Error marking notification as read:",e)}}function q(e,t){try{return JSON.parse(localStorage.getItem(`read_notifications_${t}`)||"[]").includes(e)}catch(e){return console.error("Error checking notification read status:",e),!1}}function x(e,t){try{let r=JSON.parse(localStorage.getItem(`read_notifications_${t}`)||"[]");return e.filter(e=>!r.includes(e.id)).length}catch(e){return console.error("Error getting unread notification count:",e),0}}async function P(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUnreadNotifications:",e),[];console.log(`Loading unread notifications for user: ${e}`);let t=await B(e,50),r=JSON.parse(localStorage.getItem(`read_notifications_${e}`)||"[]"),a=t.filter(e=>e.id&&!r.includes(e.id));return console.log(`Found ${a.length} unread notifications`),a}catch(e){return console.error("Error getting unread notifications:",e),[]}}async function _(e){try{return(await P(e)).length>0}catch(e){return console.error("Error checking for unread notifications:",e),!1}}async function C(e){try{let t=(0,a.P)((0,a.collection)(n.db,i.withdrawals),(0,a._M)("userId","==",e),(0,a._M)("status","==","pending"),(0,a.AB)(1));return!(await (0,a.getDocs)(t)).empty}catch(e){return console.error("Error checking pending withdrawals:",e),!1}}async function M(e){try{let t=await s(e);if(!t)return{allowed:!1,reason:"Unable to verify user information. Please try again."};if("Trial"===t.plan)return{allowed:!1,reason:"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals."};if(await C(e))return{allowed:!1,reason:"You have a pending withdrawal request. Please wait for it to be processed before submitting a new request."};let a=new Date,n=a.getHours();if(n<10||n>=18)return{allowed:!1,reason:"Withdrawals are only allowed between 10:00 AM to 6:00 PM"};let{isAdminLeaveDay:o}=await r.e(7087).then(r.bind(r,87087));if(await o(a))return{allowed:!1,reason:"Withdrawals are not allowed on admin leave/holiday days"};let{isUserOnLeave:i}=await r.e(7087).then(r.bind(r,87087));if(await i(e,a))return{allowed:!1,reason:"Withdrawals are not allowed on your leave days"};return{allowed:!0}}catch(e){return console.error("Error checking withdrawal allowed:",e),{allowed:!1,reason:"Unable to verify withdrawal eligibility. Please try again."}}}async function R(e,t,r){try{if(t<50)throw Error("Minimum withdrawal amount is ₹50");let o=await M(e);if(!o.allowed)throw Error(o.reason);if((await l(e)).wallet<t)throw Error("Insufficient wallet balance");await m(e,-t),await u(e,{type:"withdrawal_request",amount:-t,description:`Withdrawal request submitted - ₹${t} debited from wallet`});let s={userId:e,amount:t,bankDetails:r,status:"pending",date:a.Dc.now(),createdAt:a.Dc.now()};return(await (0,a.gS)((0,a.collection)(n.db,i.withdrawals),s)).id}catch(e){throw console.error("Error creating withdrawal request:",e),e}}async function H(e,t=20){try{let r=(0,a.P)((0,a.collection)(n.db,i.withdrawals),(0,a._M)("userId","==",e),(0,a.My)("date","desc"),(0,a.AB)(t));return(await (0,a.getDocs)(r)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}))}catch(e){return console.error("Error getting user withdrawals:",e),[]}}async function G(){try{try{let e=(0,a.collection)(n.db,i.users),t=((await (0,a.d_)(e)).data().count+1).toString().padStart(4,"0");return`TN${t}`}catch(r){console.warn("Failed to get count from server, using fallback method:",r);let e=Date.now().toString().slice(-4),t=Math.random().toString(36).substring(2,4).toUpperCase();return`TN${e}${t}`}}catch(t){console.error("Error generating unique referral code:",t);let e=Date.now().toString().slice(-4);return`TN${e}`}}}};