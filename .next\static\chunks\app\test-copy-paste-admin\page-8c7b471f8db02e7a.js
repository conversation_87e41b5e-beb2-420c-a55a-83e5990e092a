(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4559,7718],{526:(e,s,t)=>{Promise.resolve().then(t.bind(t,9349))},7718:(e,s,t)=>{"use strict";t.d(s,{Mk:()=>m,checkCopyPastePermission:()=>c,grantCopyPastePermission:()=>l,i7:()=>u,removeCopyPastePermission:()=>d});var a=t(6104),r=t(5317);let i={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},n={users:"users"};class o{static async checkCopyPastePermission(e){try{let s=await (0,r.x7)((0,r.H9)(a.db,n.users,e));if(!s.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let t=s.data()[i.quickTranslationAdvantageExpiry];if(!t)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let o=t.toDate(),c=new Date,l=o>c,d=l?Math.ceil((o.getTime()-c.getTime())/864e5):0;return{hasPermission:l,daysRemaining:d,expiryDate:o}}catch(s){return console.error("Error checking copy-paste permission for user ".concat(e,":"),s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,s){try{let t=new Date;t.setDate(t.getDate()+s);let o=(0,r.H9)(a.db,n.users,e);await (0,r.mZ)(o,{[i.quickTranslationAdvantageExpiry]:r.Dc.fromDate(t),[i.lastCopyPasteReduction]:r.Dc.now()}),console.log("✅ Granted copy-paste permission to user ".concat(e," for ").concat(s," days (expires: ").concat(t.toDateString(),")"))}catch(s){throw console.error("Error granting copy-paste permission to user ".concat(e,":"),s),s}}static async removeCopyPastePermission(e){try{let s=(0,r.H9)(a.db,n.users,e);await (0,r.mZ)(s,{[i.quickTranslationAdvantageExpiry]:null}),console.log("✅ Removed copy-paste permission from user ".concat(e))}catch(s){throw console.error("Error removing copy-paste permission from user ".concat(e,":"),s),s}}static async reduceCopyPasteDays(e){try{let s=await (0,r.x7)((0,r.H9)(a.db,n.users,e));if(!s.exists())return{reduced:!1,daysRemaining:0,expired:!1};let t=s.data(),o=t[i.quickTranslationAdvantageExpiry],c=t[i.lastCopyPasteReduction];if(!o)return{reduced:!1,daysRemaining:0,expired:!1};let l=new Date().toDateString();if((c?c.toDate().toDateString():null)===l){let e=o.toDate(),s=new Date,t=Math.max(0,Math.ceil((e.getTime()-s.getTime())/864e5));return{reduced:!1,daysRemaining:t,expired:0===t}}let d=o.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let m=(0,r.H9)(a.db,n.users,e);if(u<=new Date)return await (0,r.mZ)(m,{[i.quickTranslationAdvantageExpiry]:null,[i.lastCopyPasteReduction]:r.Dc.now()}),console.log("\uD83D\uDCC5 Copy-paste permission expired for user ".concat(e)),{reduced:!0,daysRemaining:0,expired:!0};{await (0,r.mZ)(m,{[i.quickTranslationAdvantageExpiry]:r.Dc.fromDate(u),[i.lastCopyPasteReduction]:r.Dc.now()});let s=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log("\uD83D\uDCC5 Reduced copy-paste days for user ".concat(e,": ").concat(s," days remaining")),{reduced:!0,daysRemaining:s,expired:!1}}}catch(s){return console.error("Error reducing copy-paste days for user ".concat(e,":"),s),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,r.getDocs)((0,r.collection)(a.db,n.users)),s=0,t=0,i=0,c=0;for(let a of e.docs)try{s++;let e=await o.reduceCopyPasteDays(a.id);e.reduced&&(t++,e.expired&&i++)}catch(e){c++,console.error("Error processing copy-paste reduction for user ".concat(a.id,":"),e)}return console.log("✅ Daily copy-paste reduction complete:"),console.log("   - Processed: ".concat(s," users")),console.log("   - Reduced: ".concat(t," users")),console.log("   - Expired: ".concat(i," users")),console.log("   - Errors: ".concat(c," users")),{processed:s,reduced:t,expired:i,errors:c}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let s=await o.checkCopyPastePermission(e);return{hasPermission:s.hasPermission,daysRemaining:s.daysRemaining,expiryDate:s.expiryDate?s.expiryDate.toDateString():null}}catch(s){return console.error("Error getting copy-paste status for user ".concat(e,":"),s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let c=o.checkCopyPastePermission,l=o.grantCopyPastePermission,d=o.removeCopyPastePermission,u=o.reduceCopyPasteDays,m=o.processAllUsersCopyPasteReduction;o.getCopyPasteStatus},9349:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var a=t(5155),r=t(2115),i=t(6681),n=t(3592),o=t(7718);function c(){let{user:e,loading:s}=(0,i.Nu)(),[t,c]=(0,r.useState)(""),[l,d]=(0,r.useState)(1),[u,m]=(0,r.useState)(null),[p,y]=(0,r.useState)(!1),[g,h]=(0,r.useState)(null),x=async()=>{if(!t.trim())return void alert("Please enter a user ID");y(!0),m(null);try{console.log("\uD83D\uDD27 Granting copy-paste permission to user ".concat(t," for ").concat(l," days")),await (0,n.w1)(t.trim(),l,(null==e?void 0:e.email)||"admin",30);let s=await (0,o.checkCopyPastePermission)(t.trim());h(s),m({success:!0,message:"Copy-paste permission granted for ".concat(l," days"),action:"granted"})}catch(e){m({success:!1,message:"Error: ".concat((null==e?void 0:e.message)||"Unknown error"),action:"grant_failed"})}finally{y(!1)}},D=async()=>{if(!t.trim())return void alert("Please enter a user ID");y(!0),m(null);try{console.log("\uD83D\uDD27 Removing copy-paste permission from user ".concat(t)),await (0,n.wT)(t.trim(),(null==e?void 0:e.email)||"admin");let s=await (0,o.checkCopyPastePermission)(t.trim());h(s),m({success:!0,message:"Copy-paste permission removed",action:"removed"})}catch(e){m({success:!1,message:"Error: ".concat((null==e?void 0:e.message)||"Unknown error"),action:"remove_failed"})}finally{y(!1)}},b=async()=>{if(!t.trim())return void alert("Please enter a user ID");y(!0);try{let e=await (0,o.checkCopyPastePermission)(t.trim());h(e),m({success:!0,message:"Status checked successfully",action:"status_checked"})}catch(e){m({success:!1,message:"Error: ".concat((null==e?void 0:e.message)||"Unknown error"),action:"status_failed"})}finally{y(!1)}};return s?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,a.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Copy-Paste Admin Functions"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white mb-2",children:"User ID:"}),(0,a.jsx)("input",{type:"text",value:t,onChange:e=>c(e.target.value),className:"w-full p-3 rounded-lg bg-white/10 text-white border border-white/20",placeholder:"Enter user ID"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white mb-2",children:"Days to Grant:"}),(0,a.jsx)("input",{type:"number",value:l,onChange:e=>d(parseInt(e.target.value)||1),min:"1",max:"365",className:"w-full p-3 rounded-lg bg-white/10 text-white border border-white/20"})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("button",{onClick:x,disabled:p,className:"flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:p?"Processing...":"Grant Permission"}),(0,a.jsx)("button",{onClick:D,disabled:p,className:"flex-1 bg-red-600 hover:bg-red-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:p?"Processing...":"Remove Permission"}),(0,a.jsx)("button",{onClick:b,disabled:p,className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:p?"Checking...":"Check Status"})]}),g&&(0,a.jsxs)("div",{className:"p-4 bg-white/10 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-white font-bold mb-2",children:"Current Permission Status:"}),(0,a.jsxs)("div",{className:"text-white space-y-1",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Has Permission:"})," ",g.hasPermission?"Yes":"No"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Days Remaining:"})," ",g.daysRemaining]}),g.expiryDate&&(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Expires:"})," ",new Date(g.expiryDate).toLocaleDateString()]})]})]}),u&&(0,a.jsxs)("div",{className:"p-4 rounded-lg ".concat(u.success?"bg-green-500/20 border border-green-500/30":"bg-red-500/20 border border-red-500/30"),children:[(0,a.jsx)("h3",{className:"font-bold ".concat(u.success?"text-green-400":"text-red-400"),children:u.success?"Success!":"Failed"}),(0,a.jsx)("p",{className:"text-white mt-2",children:u.message}),(0,a.jsxs)("p",{className:"text-white/70 text-sm mt-1",children:["Action: ",u.action]})]})]}),(0,a.jsxs)("div",{className:"mt-8 p-4 bg-white/5 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,a.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Enter a valid user ID"}),(0,a.jsx)("li",{children:'• Use "Grant Permission" to enable copy-paste'}),(0,a.jsx)("li",{children:'• Use "Remove Permission" to disable copy-paste'}),(0,a.jsx)("li",{children:'• Use "Check Status" to see current permission state'}),(0,a.jsx)("li",{children:"• Check browser console for detailed logs"})]})]})]})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,8818,6681,3592,8441,1684,7358],()=>s(526)),_N_E=e.O()}]);