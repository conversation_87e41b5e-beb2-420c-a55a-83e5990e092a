"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_activeDaysService_ts";
exports.ids = ["_ssr_src_lib_activeDaysService_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/activeDaysService.ts":
/*!**************************************!*\
  !*** ./src/lib/activeDaysService.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActiveDaysService: () => (/* binding */ ActiveDaysService),\n/* harmony export */   calculateActiveDays: () => (/* binding */ calculateActiveDays),\n/* harmony export */   forceUpdateActiveDays: () => (/* binding */ forceUpdateActiveDays),\n/* harmony export */   getActiveDaysDisplay: () => (/* binding */ getActiveDaysDisplay),\n/* harmony export */   getActiveDaysStatistics: () => (/* binding */ getActiveDaysStatistics),\n/* harmony export */   getUserActiveDays: () => (/* binding */ getUserActiveDays),\n/* harmony export */   initializeActiveDaysForNewUser: () => (/* binding */ initializeActiveDaysForNewUser),\n/* harmony export */   isUserPlanExpired: () => (/* binding */ isUserPlanExpired),\n/* harmony export */   processAllUsersActiveDays: () => (/* binding */ processAllUsersActiveDays),\n/* harmony export */   updateUserActiveDays: () => (/* binding */ updateUserActiveDays)\n/* harmony export */ });\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n\n\n// Field names and collections\nconst FIELD_NAMES = {\n    activeDays: 'activeDays',\n    lastActiveDayUpdate: 'lastActiveDayUpdate',\n    joinedDate: 'joinedDate',\n    plan: 'plan',\n    name: 'name',\n    email: 'email'\n};\nconst COLLECTIONS = {\n    users: 'users'\n};\n/**\n * UNIFIED ACTIVE DAYS SERVICE\n *\n * This is the SINGLE source of truth for all active days calculations.\n * All pages, admin functions, and services should use this service.\n *\n * Key Features:\n * - Ensures active days increment by 1 only once per day\n * - Handles all user types: Trial, Paid Plans, Admin\n * - Respects user leave days\n * - Provides comprehensive logging and error handling\n * - Thread-safe with timestamp-based deduplication\n */ class ActiveDaysService {\n    /**\n   * Calculate active days for a user based on their registration and leave history\n   */ static async calculateActiveDays(userId) {\n        try {\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId));\n            if (!userDoc.exists()) {\n                console.error(`User ${userId} not found`);\n                return {\n                    activeDays: 0,\n                    shouldUpdate: false,\n                    isNewDay: false\n                };\n            }\n            const userData = userDoc.data();\n            const joinedDate = userData.joinedDate?.toDate() || new Date();\n            const lastUpdate = userData.lastActiveDayUpdate?.toDate();\n            const currentActiveDays = userData.activeDays || 0;\n            const plan = userData.plan || 'Trial';\n            const today = new Date();\n            const todayString = today.toDateString();\n            const lastUpdateString = lastUpdate ? lastUpdate.toDateString() : null;\n            console.log(`📅 Calculating active days for user ${userId}:`);\n            console.log(`   - Joined: ${joinedDate.toDateString()}`);\n            console.log(`   - Current active days: ${currentActiveDays}`);\n            console.log(`   - Last update: ${lastUpdateString || 'Never'}`);\n            console.log(`   - Today: ${todayString}`);\n            console.log(`   - Plan: ${plan}`);\n            console.log(`   - Is new day: ${lastUpdateString !== todayString}`);\n            // Check if it's a new day\n            const isNewDay = lastUpdateString !== todayString;\n            if (!isNewDay) {\n                console.log(`✅ Already updated today for user ${userId}`);\n                return {\n                    activeDays: currentActiveDays,\n                    shouldUpdate: false,\n                    isNewDay: false\n                };\n            }\n            // Skip active days increment for admins\n            if (plan === 'Admin') {\n                console.log(`⏭️ Skipping active days increment for admin user ${userId}`);\n                await ActiveDaysService.updateLastActiveDayUpdate(userId);\n                return {\n                    activeDays: currentActiveDays,\n                    shouldUpdate: false,\n                    isNewDay: true\n                };\n            }\n            // Check if user is on leave today\n            const isOnLeave = await ActiveDaysService.isUserOnLeaveToday(userId);\n            if (isOnLeave) {\n                console.log(`🏖️ User ${userId} is on leave today, not incrementing active days`);\n                await ActiveDaysService.updateLastActiveDayUpdate(userId);\n                return {\n                    activeDays: currentActiveDays,\n                    shouldUpdate: false,\n                    isNewDay: true\n                };\n            }\n            // Calculate new active days\n            let newActiveDays;\n            if (plan === 'Trial') {\n                // For trial users: calculate based on days since joining\n                const daysSinceJoining = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24));\n                newActiveDays = daysSinceJoining + 1 // Day 1 starts on registration day\n                ;\n            } else {\n                // For paid plans: increment from current active days\n                newActiveDays = currentActiveDays + 1;\n            }\n            console.log(`📈 New active days calculated: ${currentActiveDays} → ${newActiveDays}`);\n            return {\n                activeDays: newActiveDays,\n                shouldUpdate: newActiveDays !== currentActiveDays,\n                isNewDay: true\n            };\n        } catch (error) {\n            console.error(`Error calculating active days for user ${userId}:`, error);\n            return {\n                activeDays: 0,\n                shouldUpdate: false,\n                isNewDay: false\n            };\n        }\n    }\n    /**\n   * Update user's active days\n   */ static async updateUserActiveDays(userId) {\n        try {\n            const calculation = await ActiveDaysService.calculateActiveDays(userId);\n            if (calculation.shouldUpdate) {\n                const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId);\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(userRef, {\n                    [FIELD_NAMES.activeDays]: calculation.activeDays,\n                    [FIELD_NAMES.lastActiveDayUpdate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n                });\n                console.log(`✅ Updated active days for user ${userId}: ${calculation.activeDays}`);\n            } else if (calculation.isNewDay) {\n                // Update timestamp even if active days didn't change\n                await ActiveDaysService.updateLastActiveDayUpdate(userId);\n            }\n            return calculation.activeDays;\n        } catch (error) {\n            console.error(`Error updating active days for user ${userId}:`, error);\n            throw error;\n        }\n    }\n    /**\n   * Update only the last active day update timestamp\n   */ static async updateLastActiveDayUpdate(userId) {\n        try {\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(userRef, {\n                [FIELD_NAMES.lastActiveDayUpdate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n            });\n        } catch (error) {\n            console.error(`Error updating last active day timestamp for user ${userId}:`, error);\n        }\n    }\n    /**\n   * Check if user is on leave today\n   */ static async isUserOnLeaveToday(userId) {\n        try {\n            const { isUserOnLeave } = await __webpack_require__.e(/*! import() */ \"_ssr_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(ssr)/./src/lib/leaveService.ts\"));\n            return await isUserOnLeave(userId, new Date());\n        } catch (error) {\n            console.error(`Error checking leave status for user ${userId}:`, error);\n            return false // Default to not on leave to avoid blocking work\n            ;\n        }\n    }\n    /**\n   * Process all users' active days (daily scheduler)\n   */ static async processAllUsersActiveDays() {\n        try {\n            console.log('🔄 Starting daily active days processing for all users...');\n            const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users));\n            let processed = 0;\n            let updated = 0;\n            let errors = 0;\n            for (const userDoc of usersSnapshot.docs){\n                try {\n                    processed++;\n                    const calculation = await ActiveDaysService.calculateActiveDays(userDoc.id);\n                    if (calculation.shouldUpdate || calculation.isNewDay) {\n                        await ActiveDaysService.updateUserActiveDays(userDoc.id);\n                        if (calculation.shouldUpdate) updated++;\n                    }\n                } catch (error) {\n                    errors++;\n                    console.error(`Error processing active days for user ${userDoc.id}:`, error);\n                }\n            }\n            console.log(`✅ Daily active days processing complete:`);\n            console.log(`   - Processed: ${processed} users`);\n            console.log(`   - Updated: ${updated} users`);\n            console.log(`   - Errors: ${errors} users`);\n            return {\n                processed,\n                updated,\n                errors\n            };\n        } catch (error) {\n            console.error('Error in daily active days processing:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get active days for a user (read-only)\n   */ static async getUserActiveDays(userId) {\n        try {\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId));\n            if (!userDoc.exists()) {\n                return 0;\n            }\n            const userData = userDoc.data();\n            return userData.activeDays || 0;\n        } catch (error) {\n            console.error(`Error getting active days for user ${userId}:`, error);\n            return 0;\n        }\n    }\n    /**\n   * Initialize active days for new user (called during registration)\n   */ static async initializeActiveDaysForNewUser(userId) {\n        try {\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(userRef, {\n                [FIELD_NAMES.activeDays]: 1,\n                [FIELD_NAMES.lastActiveDayUpdate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n            });\n            console.log(`✅ Initialized active days for new user ${userId}: Day 1`);\n        } catch (error) {\n            console.error(`Error initializing active days for user ${userId}:`, error);\n            throw error;\n        }\n    }\n    /**\n   * Get active days display format for UI (current/total)\n   */ static async getActiveDaysDisplay(userId) {\n        try {\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId));\n            if (!userDoc.exists()) {\n                return {\n                    current: 0,\n                    total: 2,\n                    displayText: '0/2'\n                };\n            }\n            const userData = userDoc.data();\n            const plan = userData.plan || 'Trial';\n            const activeDays = userData.activeDays || 0;\n            let total;\n            if (plan === 'Trial') {\n                total = 2;\n            } else {\n                total = 30 // Standard plan duration (expires on 31st day)\n                ;\n            }\n            return {\n                current: activeDays,\n                total,\n                displayText: `${activeDays}/${total}`\n            };\n        } catch (error) {\n            console.error(`Error getting active days display for user ${userId}:`, error);\n            return {\n                current: 0,\n                total: 2,\n                displayText: '0/2'\n            };\n        }\n    }\n    /**\n   * Check if user's plan is expired based on active days\n   */ static async isUserPlanExpired(userId) {\n        try {\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId));\n            if (!userDoc.exists()) {\n                return {\n                    expired: true,\n                    reason: 'User not found'\n                };\n            }\n            const userData = userDoc.data();\n            const plan = userData.plan || 'Trial';\n            const activeDays = userData.activeDays || 0;\n            const planExpiry = userData.planExpiry;\n            console.log(`📅 Checking plan expiry for user ${userId}:`, {\n                plan,\n                activeDays,\n                hasPlanExpiry: !!planExpiry,\n                planExpiryDate: planExpiry ? planExpiry.toDate() : null\n            });\n            if (plan === 'Admin') {\n                const result = {\n                    expired: false,\n                    activeDays\n                };\n                console.log(`📅 Plan expiry result for admin user ${userId}:`, result);\n                return result;\n            }\n            if (plan === 'Trial') {\n                const daysLeft = Math.max(0, 2 - activeDays);\n                const result = {\n                    expired: daysLeft <= 0,\n                    reason: daysLeft <= 0 ? 'Trial period expired' : undefined,\n                    daysLeft,\n                    activeDays\n                };\n                console.log(`📅 Plan expiry result for trial user ${userId}:`, result);\n                return result;\n            }\n            // For paid plans, check if planExpiry is set\n            if (planExpiry) {\n                const today = new Date();\n                const expiryDate = planExpiry.toDate();\n                const expired = today > expiryDate;\n                const daysLeft = expired ? 0 : Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                const result = {\n                    expired,\n                    reason: expired ? 'Plan subscription expired' : undefined,\n                    daysLeft,\n                    activeDays\n                };\n                console.log(`📅 Plan expiry result for user ${userId} (using planExpiry field):`, result);\n                return result;\n            }\n            // If planExpiry field is empty, use active days to determine expiry\n            // Paid plans (Junior, Senior, Expert) expire after 30 active days\n            const daysLeft = Math.max(0, 30 - activeDays);\n            const expired = activeDays > 30 // Expire if user has more than 30 active days\n            ;\n            const result = {\n                expired,\n                reason: expired ? `Plan expired - You have used ${activeDays} days out of 30 allowed days` : undefined,\n                daysLeft,\n                activeDays\n            };\n            console.log(`📅 Plan expiry result for user ${userId}:`, result);\n            return result;\n        } catch (error) {\n            console.error(`Error checking plan expiry for user ${userId}:`, error);\n            return {\n                expired: true,\n                reason: 'Error checking plan status'\n            };\n        }\n    }\n    /**\n   * Set plan expiry date for users who don't have it set\n   * This is useful for migrating existing users\n   */ static async setPlanExpiryForUser(userId, planDurationDays = 30) {\n        try {\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId);\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(userDocRef);\n            if (!userDoc.exists()) {\n                console.error(`User ${userId} not found`);\n                return false;\n            }\n            const userData = userDoc.data();\n            const plan = userData.plan || 'Trial';\n            // Don't set expiry for Trial or Admin users\n            if (plan === 'Trial' || plan === 'Admin') {\n                console.log(`Skipping plan expiry setup for ${plan} user: ${userId}`);\n                return false;\n            }\n            // Don't overwrite existing planExpiry\n            if (userData.planExpiry) {\n                console.log(`User ${userId} already has plan expiry set: ${userData.planExpiry.toDate()}`);\n                return false;\n            }\n            // Calculate expiry date based on join date + plan duration\n            const joinedDate = userData.joinedDate?.toDate() || new Date();\n            const expiryDate = new Date(joinedDate);\n            expiryDate.setDate(expiryDate.getDate() + planDurationDays);\n            // Update user document with plan expiry\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(userDocRef, {\n                planExpiry: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(expiryDate),\n                planExpirySetDate: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n            });\n            console.log(`✅ Set plan expiry for user ${userId}: ${expiryDate}`);\n            return true;\n        } catch (error) {\n            console.error(`Error setting plan expiry for user ${userId}:`, error);\n            return false;\n        }\n    }\n    /**\n   * Force update active days for a specific user (admin use only)\n   */ static async forceUpdateActiveDays(userId, newActiveDays, adminId) {\n        try {\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(userRef, {\n                [FIELD_NAMES.activeDays]: newActiveDays,\n                [FIELD_NAMES.lastActiveDayUpdate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n            });\n            console.log(`🔧 Admin ${adminId} force updated active days for user ${userId}: ${newActiveDays}`);\n        } catch (error) {\n            console.error(`Error force updating active days for user ${userId}:`, error);\n            throw error;\n        }\n    }\n    /**\n   * Get comprehensive active days statistics for admin dashboard\n   */ static async getActiveDaysStatistics() {\n        try {\n            const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users));\n            let totalUsers = 0;\n            let trialUsers = 0;\n            let paidUsers = 0;\n            let adminUsers = 0;\n            let totalActiveDays = 0;\n            let usersUpdatedToday = 0;\n            const today = new Date().toDateString();\n            for (const userDoc of usersSnapshot.docs){\n                const userData = userDoc.data();\n                const plan = userData.plan || 'Trial';\n                const activeDays = userData.activeDays || 0;\n                const lastUpdate = userData.lastActiveDayUpdate?.toDate();\n                totalUsers++;\n                totalActiveDays += activeDays;\n                if (plan === 'Trial') trialUsers++;\n                else if (plan === 'Admin') adminUsers++;\n                else paidUsers++;\n                if (lastUpdate && lastUpdate.toDateString() === today) {\n                    usersUpdatedToday++;\n                }\n            }\n            return {\n                totalUsers,\n                trialUsers,\n                paidUsers,\n                adminUsers,\n                averageActiveDays: totalUsers > 0 ? Math.round(totalActiveDays / totalUsers * 100) / 100 : 0,\n                usersUpdatedToday\n            };\n        } catch (error) {\n            console.error('Error getting active days statistics:', error);\n            throw error;\n        }\n    }\n}\n// Export convenience functions for backward compatibility\nconst calculateActiveDays = ActiveDaysService.calculateActiveDays;\nconst updateUserActiveDays = ActiveDaysService.updateUserActiveDays;\nconst processAllUsersActiveDays = ActiveDaysService.processAllUsersActiveDays;\nconst getUserActiveDays = ActiveDaysService.getUserActiveDays;\nconst initializeActiveDaysForNewUser = ActiveDaysService.initializeActiveDaysForNewUser;\n// Export new unified functions\nconst getActiveDaysDisplay = ActiveDaysService.getActiveDaysDisplay;\nconst isUserPlanExpired = ActiveDaysService.isUserPlanExpired;\nconst forceUpdateActiveDays = ActiveDaysService.forceUpdateActiveDays;\nconst getActiveDaysStatistics = ActiveDaysService.getActiveDaysStatistics;\n// Export the main class for direct usage (recommended)\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/activeDaysService.ts\n");

/***/ })

};
;