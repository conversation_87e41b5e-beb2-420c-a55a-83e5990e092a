(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6104,6912],{1469:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return c},getImageProps:function(){return r}});let a=s(8229),n=s(8883),i=s(3063),l=a._(s(1193));function r(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let c=i.Image},6104:(e,t,s)=>{"use strict";s.d(t,{Cn:()=>m,auth:()=>o,db:()=>d});var a=s(3915),n=s(3004),i=s(5317),l=s(858),r=s(2144);let c=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),o=(0,n.xI)(c),d=(0,i.aU)(c);(0,l.c7)(c);let m=(0,r.Uz)(c)},6766:(e,t,s)=>{"use strict";s.d(t,{default:()=>n.a});var a=s(1469),n=s.n(a)},7434:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var a=s(5155),n=s(2115),i=s(6874),l=s.n(i),r=s(6766),c=s(3004),o=s(5317),d=s(6104),m=s(3592),x=s(4752),h=s.n(x);function u(){let[e,t]=(0,n.useState)(!1),[s,i]=(0,n.useState)(!1),x=async()=>{t(!0);try{let e="<EMAIL>",t=(await (0,c.eJ)(d.auth,e,"123456")).user,s={[m.Yr.name]:"Instra Global Admin",[m.Yr.email]:e,[m.Yr.mobile]:"9999999999",[m.Yr.referralCode]:"TN0000",[m.Yr.referredBy]:"",[m.Yr.plan]:"Admin",[m.Yr.planExpiry]:null,[m.Yr.activeDays]:999999,[m.Yr.totalTranslations]:0,[m.Yr.todayTranslations]:0,[m.Yr.lastTranslationDate]:null,[m.Yr.wallet]:0,[m.Yr.joinedDate]:o.Dc.now(),status:"active",[m.Yr.translationDuration]:300,role:"admin",isAdmin:!0,permissions:["all"]};await (0,o.BN)((0,o.H9)(d.db,m.COLLECTIONS.users,t.uid),s);let a={email:e,name:"Instra Global Admin",role:"super_admin",permissions:["all"],createdAt:o.Dc.now(),isActive:!0};await (0,o.BN)((0,o.H9)(d.db,"admins",t.uid),a),await d.auth.signOut(),i(!0),h().fire({icon:"success",title:"Admin Account Created!",html:'\n          <div class="text-left">\n            <p><strong>Email:</strong> <EMAIL></p>\n            <p><strong>Password:</strong> 123456</p>\n            <br>\n            <p>The admin account has been successfully created. You can now login using these credentials.</p>\n          </div>\n        ',confirmButtonText:"Go to Admin Login"}).then(()=>{window.location.href="/admin/login"})}catch(t){console.error("Error creating admin account:",t);let e="Failed to create admin account";"auth/email-already-in-use"===t.code?(e="Admin account already exists! You can login with: <EMAIL> / 123456",h().fire({icon:"info",title:"Admin Account Exists",html:'\n            <div class="text-left">\n              <p><strong>Email:</strong> <EMAIL></p>\n              <p><strong>Password:</strong> 123456</p>\n              <br>\n              <p>The admin account already exists. Use these credentials to login.</p>\n            </div>\n          ',confirmButtonText:"Go to Admin Login"}).then(()=>{window.location.href="/admin/login"})):h().fire({icon:"error",title:"Setup Failed",text:e})}finally{t(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,a.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,a.jsx)(r.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:50,height:50,className:"mr-3"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-white",children:"Instra Global"})]}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Admin Setup"}),(0,a.jsx)("p",{className:"text-white/80",children:"Create the admin account for Instra Global"})]}),s?(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-green-400 text-6xl mb-4",children:(0,a.jsx)("i",{className:"fas fa-check-circle"})}),(0,a.jsx)("h2",{className:"text-xl font-bold text-white mb-2",children:"Setup Complete!"}),(0,a.jsx)("p",{className:"text-white/80 mb-6",children:"Admin account has been created successfully."}),(0,a.jsxs)(l(),{href:"/admin/login",className:"btn-primary inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Go to Admin Login"]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-500/20 rounded-lg p-4 border border-blue-500/30",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Admin Account Details"]}),(0,a.jsxs)("div",{className:"text-white/80 text-sm space-y-1",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Password:"})," 123456"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Role:"})," Super Administrator"]})]})]}),(0,a.jsx)("button",{onClick:x,disabled:e,className:"w-full btn-primary flex items-center justify-center",children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Creating Admin Account..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-user-shield mr-2"}),"Create Admin Account"]})}),(0,a.jsx)("div",{className:"bg-yellow-500/20 rounded-lg p-4 border border-yellow-500/30",children:(0,a.jsxs)("div",{className:"flex items-start text-yellow-300",children:[(0,a.jsx)("i",{className:"fas fa-exclamation-triangle mr-2 mt-1"}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("p",{className:"font-semibold mb-1",children:"Security Notice:"}),(0,a.jsx)("p",{children:"This will create an admin account with full system access. Make sure to change the password after first login for security."})]})]})})]}),(0,a.jsxs)("div",{className:"mt-8 text-center space-y-2",children:[(0,a.jsxs)(l(),{href:"/admin/login",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Admin Login"]}),(0,a.jsx)("br",{}),(0,a.jsxs)(l(),{href:"/",className:"text-white/60 hover:text-white/80 transition-colors inline-flex items-center text-sm",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})]})]})})}},8532:(e,t,s)=>{Promise.resolve().then(s.bind(s,7434))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6874,3063,3592,8441,1684,7358],()=>t(8532)),_N_E=e.O()}]);