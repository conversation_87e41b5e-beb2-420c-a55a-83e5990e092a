(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7646,7718],{3737:(e,t,a)=>{"use strict";function s(e,t,a){if(!e||0===e.length)return void alert("No data to export");let s=a||Object.keys(e[0]),n=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],i=new Blob(["\uFEFF"+[s.join(","),...e.map(e=>s.map(t=>{let a=e[t];if(null==a)return"";let s=n.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return'"'.concat(e,'"')}return a instanceof Date?'"'.concat(a.toLocaleDateString(),'"'):"object"==typeof a&&null!==a&&a.toDate?'"'.concat(a.toDate().toLocaleDateString(),'"'):s&&("number"==typeof a||!isNaN(Number(a)))?'"'.concat(a,'"'):"number"==typeof a?a.toString():'"'.concat(String(a),'"')}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),r=document.createElement("a");if(void 0!==r.download){let e=URL.createObjectURL(i);r.setAttribute("href",e),r.setAttribute("download","".concat(t,"_").concat(new Date().toISOString().split("T")[0],".csv")),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r)}}function n(e){return e.map(t=>{let a=0,s=null,n="No",i=t.quickTranslationAdvantageExpiry||t.quickVideoAdvantageExpiry;if(i)try{i instanceof Date?s=i:i.toDate&&"function"==typeof i.toDate?s=i.toDate():s=new Date(i);let r=new Date,o=s.getTime()-r.getTime();n=(a=Math.max(0,Math.ceil(o/864e5)))>0?"Yes":"No",5>e.indexOf(t)&&console.log("\uD83D\uDCCA Export debug for user ".concat(t.email,":"),{expiryField:i,expiryFieldType:typeof i,copyPasteExpiryDate:s,copyPasteRemainingDays:a,copyPastePermission:n,hasQuickTranslationAdvantageExpiry:!!t.quickTranslationAdvantageExpiry,hasQuickVideoAdvantageExpiry:!!t.quickVideoAdvantageExpiry})}catch(e){console.error("❌ Error calculating copy-paste days for user ".concat(t.email,":"),e)}else 5>e.indexOf(t)&&console.log("\uD83D\uDCCA Export debug for user ".concat(t.email,": No copy-paste expiry field found"));return{"User ID":t.id||"",Name:t.name||"",Email:t.email||"",Mobile:String(t.mobile||""),"Referral Code":t.referralCode||"","Referred By":t.referredBy||"Direct","Referrals Count":t.referralCount||0,Plan:t.plan||"","Plan Expiry":t.planExpiry instanceof Date?t.planExpiry.toLocaleDateString():t.planExpiry?new Date(t.planExpiry).toLocaleDateString():"","Active Days":t.activeDays||0,"Total Translations":t.totalTranslations||t.totalVideos||0,"Today Translations":t.todayTranslations||t.todayVideos||0,"Last Translation Date":t.lastTranslationDate instanceof Date?t.lastTranslationDate.toLocaleDateString():t.lastTranslationDate?new Date(t.lastTranslationDate).toLocaleDateString():t.lastVideoDate instanceof Date?t.lastVideoDate.toLocaleDateString():t.lastVideoDate?new Date(t.lastVideoDate).toLocaleDateString():"","Copy-Paste Permission":n,"Copy-Paste Remaining Days":a,"Copy-Paste Expiry":s?s.toLocaleDateString():"","Copy-Paste Granted By":t.quickTranslationAdvantageGrantedBy||t.quickVideoAdvantageGrantedBy||"","Copy-Paste Granted At":t.quickTranslationAdvantageGrantedAt?t.quickTranslationAdvantageGrantedAt instanceof Date?t.quickTranslationAdvantageGrantedAt.toLocaleDateString():new Date(t.quickTranslationAdvantageGrantedAt).toLocaleDateString():"","Wallet Balance":t.wallet||0,"Referral Bonus Credited":t.referralBonusCredited?"Yes":"No",Status:t.status||"","Joined Date":t.joinedDate instanceof Date?t.joinedDate.toLocaleDateString():t.joinedDate?new Date(t.joinedDate).toLocaleDateString():"","Joined Time":t.joinedDate instanceof Date?t.joinedDate.toLocaleTimeString():t.joinedDate?new Date(t.joinedDate).toLocaleTimeString():""}})}function i(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function r(e){return e.map(e=>{var t,a,s,n;return{"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":(null==(t=e.bankDetails)?void 0:t.accountHolderName)||"","Bank Name":(null==(a=e.bankDetails)?void 0:a.bankName)||"","Account Number":String((null==(s=e.bankDetails)?void 0:s.accountNumber)||""),"IFSC Code":(null==(n=e.bankDetails)?void 0:n.ifscCode)||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}})}function o(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>s,Fz:()=>n,Pe:()=>o,dB:()=>r,sL:()=>i})},6675:(e,t,a)=>{Promise.resolve().then(a.bind(a,9520))},7718:(e,t,a)=>{"use strict";a.d(t,{Mk:()=>p,checkCopyPastePermission:()=>c,grantCopyPastePermission:()=>l,i7:()=>u,removeCopyPastePermission:()=>d});var s=a(6104),n=a(5317);let i={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},r={users:"users"};class o{static async checkCopyPastePermission(e){try{let t=await (0,n.x7)((0,n.H9)(s.db,r.users,e));if(!t.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let a=t.data()[i.quickTranslationAdvantageExpiry];if(!a)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let o=a.toDate(),c=new Date,l=o>c,d=l?Math.ceil((o.getTime()-c.getTime())/864e5):0;return{hasPermission:l,daysRemaining:d,expiryDate:o}}catch(t){return console.error("Error checking copy-paste permission for user ".concat(e,":"),t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,t){try{let a=new Date;a.setDate(a.getDate()+t);let o=(0,n.H9)(s.db,r.users,e);await (0,n.mZ)(o,{[i.quickTranslationAdvantageExpiry]:n.Dc.fromDate(a),[i.lastCopyPasteReduction]:n.Dc.now()}),console.log("✅ Granted copy-paste permission to user ".concat(e," for ").concat(t," days (expires: ").concat(a.toDateString(),")"))}catch(t){throw console.error("Error granting copy-paste permission to user ".concat(e,":"),t),t}}static async removeCopyPastePermission(e){try{let t=(0,n.H9)(s.db,r.users,e);await (0,n.mZ)(t,{[i.quickTranslationAdvantageExpiry]:null}),console.log("✅ Removed copy-paste permission from user ".concat(e))}catch(t){throw console.error("Error removing copy-paste permission from user ".concat(e,":"),t),t}}static async reduceCopyPasteDays(e){try{let t=await (0,n.x7)((0,n.H9)(s.db,r.users,e));if(!t.exists())return{reduced:!1,daysRemaining:0,expired:!1};let a=t.data(),o=a[i.quickTranslationAdvantageExpiry],c=a[i.lastCopyPasteReduction];if(!o)return{reduced:!1,daysRemaining:0,expired:!1};let l=new Date().toDateString();if((c?c.toDate().toDateString():null)===l){let e=o.toDate(),t=new Date,a=Math.max(0,Math.ceil((e.getTime()-t.getTime())/864e5));return{reduced:!1,daysRemaining:a,expired:0===a}}let d=o.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let p=(0,n.H9)(s.db,r.users,e);if(u<=new Date)return await (0,n.mZ)(p,{[i.quickTranslationAdvantageExpiry]:null,[i.lastCopyPasteReduction]:n.Dc.now()}),console.log("\uD83D\uDCC5 Copy-paste permission expired for user ".concat(e)),{reduced:!0,daysRemaining:0,expired:!0};{await (0,n.mZ)(p,{[i.quickTranslationAdvantageExpiry]:n.Dc.fromDate(u),[i.lastCopyPasteReduction]:n.Dc.now()});let t=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log("\uD83D\uDCC5 Reduced copy-paste days for user ".concat(e,": ").concat(t," days remaining")),{reduced:!0,daysRemaining:t,expired:!1}}}catch(t){return console.error("Error reducing copy-paste days for user ".concat(e,":"),t),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,n.getDocs)((0,n.collection)(s.db,r.users)),t=0,a=0,i=0,c=0;for(let s of e.docs)try{t++;let e=await o.reduceCopyPasteDays(s.id);e.reduced&&(a++,e.expired&&i++)}catch(e){c++,console.error("Error processing copy-paste reduction for user ".concat(s.id,":"),e)}return console.log("✅ Daily copy-paste reduction complete:"),console.log("   - Processed: ".concat(t," users")),console.log("   - Reduced: ".concat(a," users")),console.log("   - Expired: ".concat(i," users")),console.log("   - Errors: ".concat(c," users")),{processed:t,reduced:a,expired:i,errors:c}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let t=await o.checkCopyPastePermission(e);return{hasPermission:t.hasPermission,daysRemaining:t.daysRemaining,expiryDate:t.expiryDate?t.expiryDate.toDateString():null}}catch(t){return console.error("Error getting copy-paste status for user ".concat(e,":"),t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let c=o.checkCopyPastePermission,l=o.grantCopyPastePermission,d=o.removeCopyPastePermission,u=o.reduceCopyPasteDays,p=o.processAllUsersCopyPasteReduction;o.getCopyPasteStatus},9520:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var s=a(5155),n=a(2115),i=a(6681),r=a(6779),o=a(3737),c=a(7718);function l(){let{user:e,loading:t}=(0,i.Nu)(),[a,l]=(0,n.useState)(null),[d,u]=(0,n.useState)(!1),p=async()=>{u(!0),l(null);try{console.log("\uD83E\uDDEA Testing copy-paste export functionality...");let e=await (0,r.CF)();console.log("\uD83D\uDCCA Retrieved ".concat(e.length," users for testing"));let t=e.slice(0,5),a=[];for(let e of t)try{let t=await (0,c.checkCopyPastePermission)(e.id),s={email:e.email,name:e.name,hasQuickTranslationAdvantageExpiry:!!e.quickTranslationAdvantageExpiry,hasQuickVideoAdvantageExpiry:!!e.quickVideoAdvantageExpiry,quickTranslationAdvantageExpiry:e.quickTranslationAdvantageExpiry,quickVideoAdvantageExpiry:e.quickVideoAdvantageExpiry,servicePermission:t.hasPermission,serviceDaysRemaining:t.daysRemaining,serviceExpiryDate:t.expiryDate};a.push(s),console.log("\uD83D\uDCCB User ".concat(e.email,":"),s)}catch(t){console.error("❌ Error testing user ".concat(e.email,":"),t)}let s=(0,o.Fz)(t);console.log("\uD83D\uDCCA Export data sample:",s.slice(0,2));let n=e.filter(e=>e.quickTranslationAdvantageExpiry||e.quickVideoAdvantageExpiry);l({success:!0,totalUsers:e.length,usersWithCopyPaste:n.length,testUsers:a,exportSample:s.slice(0,2)})}catch(e){console.error("❌ Test failed:",e),l({success:!1,error:e.message})}finally{u(!1)}},y=async()=>{try{let e=await (0,r.CF)(),t=(0,o.Fz)(e);(0,o.Bf)(t,"test_copy_paste_export"),alert("Downloaded test export with ".concat(e.length," users. Check the Copy-Paste columns!"))}catch(e){console.error("Export failed:",e),alert("Export failed. Check console for details.")}};return t?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner mb-4"}),(0,s.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,s.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Copy-Paste Export"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)("button",{onClick:p,disabled:d,className:"bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold disabled:opacity-50",children:d?"Testing...":"Test Copy-Paste Data"}),(0,s.jsx)("button",{onClick:y,className:"bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg font-semibold",children:"Download Test Export"})]}),a&&(0,s.jsx)("div",{className:"p-4 bg-white/10 rounded-lg",children:a.success?(0,s.jsxs)("div",{className:"text-white space-y-4",children:[(0,s.jsx)("h3",{className:"font-bold text-green-400",children:"Test Results:"}),(0,s.jsx)("div",{className:"grid grid-cols-2 gap-4",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Total Users:"})," ",a.totalUsers]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Users with Copy-Paste:"})," ",a.usersWithCopyPaste]})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-bold mb-2",children:"Sample Users Test:"}),(0,s.jsx)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:a.testUsers.map((e,t)=>(0,s.jsxs)("div",{className:"p-2 bg-white/5 rounded text-sm",children:[(0,s.jsx)("p",{children:(0,s.jsx)("strong",{children:e.email})}),(0,s.jsxs)("p",{children:["Service Permission: ",e.servicePermission?"Yes":"No"]}),(0,s.jsxs)("p",{children:["Service Days Remaining: ",e.serviceDaysRemaining]}),(0,s.jsxs)("p",{children:["Has Expiry Field: ",e.hasQuickTranslationAdvantageExpiry?"Yes":"No"]})]},t))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-bold mb-2",children:"Export Sample:"}),(0,s.jsx)("div",{className:"text-xs bg-black/20 p-2 rounded overflow-x-auto",children:(0,s.jsx)("pre",{children:JSON.stringify(a.exportSample,null,2)})})]})]}):(0,s.jsxs)("div",{className:"text-red-400",children:[(0,s.jsx)("h3",{className:"font-bold",children:"Test Failed:"}),(0,s.jsx)("p",{children:a.error})]})})]}),(0,s.jsxs)("div",{className:"mt-8 p-4 bg-white/5 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,s.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,s.jsx)("li",{children:'• Click "Test Copy-Paste Data" to analyze first 5 users'}),(0,s.jsx)("li",{children:'• Click "Download Test Export" to get full CSV with copy-paste data'}),(0,s.jsx)("li",{children:"• Check console for detailed debugging information"}),(0,s.jsx)("li",{children:"• Verify Copy-Paste columns in the downloaded CSV"})]})]})]})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6681,3592,6779,8441,1684,7358],()=>t(6675)),_N_E=e.O()}]);