(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{347:()=>{},1340:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var a=s(5155),r=s(2115);function n(){let[e,t]=(0,r.useState)(null),[s,n]=(0,r.useState)(!1);(0,r.useEffect)(()=>{"serviceWorker"in navigator&&navigator.serviceWorker.register("/sw.js").then(e=>{console.log("SW registered: ",e)}).catch(e=>{console.log("SW registration failed: ",e)});let e=e=>{e.preventDefault(),t(e),n(!0)};return window.addEventListener("beforeinstallprompt",e),window.matchMedia("(display-mode: standalone)").matches&&n(!1),()=>{window.removeEventListener("beforeinstallprompt",e)}},[]);let o=async()=>{if(!e)return;e.prompt();let{outcome:s}=await e.userChoice;"accepted"===s?console.log("User accepted the install prompt"):console.log("User dismissed the install prompt"),t(null),n(!1)};return s?(0,a.jsx)("div",{className:"fixed bottom-4 right-4 z-50",children:(0,a.jsxs)("button",{onClick:o,className:"glass-button px-4 py-3 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Install App"]})}):null}},3711:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var a=s(2115),r=s(6681);function n(){let{user:e,loading:t}=(0,r.hD)();return(0,a.useEffect)(()=>{if(t||!e)return void console.log("⏸️ DailyActiveDaysScheduler: Waiting for authentication...");let s=async()=>{try{let e=new Date().toDateString(),t=localStorage.getItem("lastDailyProcessing");if(t===e)return void console.log("✅ Daily processing already completed today");let s=new Date,a=new Date(s.toLocaleString("en-US",{timeZone:"Asia/Kolkata"})),r=new Date(a);if(r.setHours(0,0,0,0),a>=r&&t!==e){console.log("\uD83C\uDF05 First user after 12 AM IST - triggering daily processing...");let t=await fetch("https://us-central1-instra-global.cloudfunctions.net/dailyProcessingCron"),s=await t.json();s.success?(console.log("✅ Daily processing completed successfully:",s),localStorage.setItem("lastDailyProcessing",e)):console.error("❌ Daily processing failed:",s)}else console.log("⏸️ Not time for daily processing yet or already processed")}catch(e){console.error("❌ Error in daily processing trigger:",e)}};console.log("\uD83D\uDD04 DailyActiveDaysScheduler: Checking if daily processing needed..."),s()},[e,t]),null}},5080:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(5155),r=s(2115);class n extends r.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"glass-card p-8 text-center max-w-md",children:[(0,a.jsx)("i",{className:"fas fa-exclamation-triangle text-red-400 text-4xl mb-4"}),(0,a.jsx)("h2",{className:"text-xl font-bold text-white mb-2",children:"Something went wrong"}),(0,a.jsx)("p",{className:"text-white/80 mb-4",children:"An error occurred while loading this page. Please refresh and try again."}),(0,a.jsxs)("button",{onClick:()=>window.location.reload(),className:"btn-primary",children:[(0,a.jsx)("i",{className:"fas fa-refresh mr-2"}),"Refresh Page"]})]})}):this.props.children}constructor(e){super(e),this.state={hasError:!1}}}let o=n},5170:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(5155),r=s(2115),n=s(6681);function o(e){let{children:t}=e,[s,o]=(0,r.useState)(!0),[l,i]=(0,r.useState)(!1),[c,d]=(0,r.useState)(null),{user:m}=(0,n.hD)();return((0,r.useEffect)(()=>{"undefined"!=typeof navigator&&o(navigator.onLine);let e=()=>{if(console.log("\uD83C\uDF10 Network: Back online"),o(!0),i(!1),d(null),c){let e=Math.round((Date.now()-c.getTime())/1e3);console.log("\uD83D\uDCF6 Reconnected after ".concat(e," seconds offline"))}},t=()=>{console.log("\uD83D\uDCF5 Network: Offline"),o(!1),d(new Date),setTimeout(()=>{navigator.onLine||i(!0)},2e3)};return window.addEventListener("online",e),window.addEventListener("offline",t),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",t)}},[c]),l)?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4",children:(0,a.jsx)("div",{className:"max-w-md w-full",children:(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-8 text-center border border-white/20",children:[(0,a.jsx)("div",{className:"mx-auto w-20 h-20 bg-red-500/20 rounded-full flex items-center justify-center mb-6",children:(0,a.jsx)("i",{className:"fas fa-wifi-slash text-red-400 text-3xl"})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:"You're Offline"}),(0,a.jsx)("p",{className:"text-white/80 mb-6 leading-relaxed",children:"Your internet connection seems to be down. Don't worry - your work progress is safely saved locally and will be restored when you're back online."}),(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-white/60",children:"Connection Status:"}),(0,a.jsxs)("span",{className:"text-red-400 font-medium",children:[(0,a.jsx)("i",{className:"fas fa-circle text-xs mr-1"}),"Offline"]})]}),c&&(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm mt-2",children:[(0,a.jsx)("span",{className:"text-white/60",children:"Offline Since:"}),(0,a.jsx)("span",{className:"text-white/80",children:c.toLocaleTimeString()})]}),m&&(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm mt-2",children:[(0,a.jsx)("span",{className:"text-white/60",children:"User Session:"}),(0,a.jsxs)("span",{className:"text-green-400 font-medium",children:[(0,a.jsx)("i",{className:"fas fa-check text-xs mr-1"}),"Protected"]})]})]}),(0,a.jsxs)("div",{className:"text-left mb-6",children:[(0,a.jsx)("h3",{className:"text-white font-semibold mb-3 text-center",children:"Available Offline:"}),(0,a.jsxs)("ul",{className:"space-y-2 text-white/80 text-sm",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-2"}),"Continue current translation work"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-2"}),"Auto-save progress locally"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-2"}),"View completed translations"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-times text-red-400 mr-2"}),"Submit translations for earnings"]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{onClick:()=>window.location.reload(),className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Check Connection"]}),(0,a.jsx)("button",{onClick:()=>i(!1),className:"w-full bg-white/10 hover:bg-white/20 text-white font-semibold py-3 px-6 rounded-lg transition-colors border border-white/20",children:"Continue Offline"})]}),(0,a.jsx)("div",{className:"mt-6 p-4 bg-yellow-500/10 rounded-lg border border-yellow-500/20",children:(0,a.jsxs)("p",{className:"text-yellow-200 text-xs",children:[(0,a.jsx)("i",{className:"fas fa-lightbulb mr-1"}),(0,a.jsx)("strong",{children:"Tip:"})," Your work is automatically saved every 10 seconds. When you reconnect, everything will sync automatically."]})})]})})}):(0,a.jsxs)("div",{className:"relative",children:[!s&&(0,a.jsxs)("div",{className:"fixed top-0 left-0 right-0 bg-red-600 text-white text-center py-2 text-sm z-50",children:[(0,a.jsx)("i",{className:"fas fa-wifi-slash mr-2"}),"You're offline - work is saved locally",(0,a.jsx)("button",{onClick:()=>i(!0),className:"ml-4 underline hover:no-underline",children:"View Details"})]}),(0,a.jsx)("div",{className:s?"":"pt-10",children:t})]})}},7320:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var a=s(5155),r=s(2115),n=s(4752),o=s.n(n);function l(e){let{children:t}=e,[s,n]=(0,r.useState)(!1),[l,i]=(0,r.useState)(null),[c,d]=(0,r.useState)("");(0,r.useEffect)(()=>("serviceWorker"in navigator&&m(),window.addEventListener("beforeunload",g),()=>{window.removeEventListener("beforeunload",g)}),[]);let m=async()=>{try{let e=await navigator.serviceWorker.register("/sw.js",{scope:"/",updateViaCache:"none"});i(e),console.log("✅ Service Worker registered successfully"),e.update(),e.addEventListener("updatefound",()=>{let t=e.installing;t&&(console.log("\uD83D\uDD04 New service worker found, installing..."),t.addEventListener("statechange",()=>{"installed"===t.state&&navigator.serviceWorker.controller&&(console.log("✨ New service worker installed, update available"),n(!0),u())}))}),navigator.serviceWorker.addEventListener("message",e=>{e.data&&"SW_UPDATED"===e.data.type&&(console.log("\uD83D\uDCF1 Service worker updated to version:",e.data.version),d(e.data.version))}),e.waiting&&(n(!0),u()),f()}catch(e){console.error("❌ Service Worker registration failed:",e)}},f=async()=>{if("serviceWorker"in navigator&&navigator.serviceWorker.controller)try{let e=new MessageChannel;e.port1.onmessage=e=>{e.data&&e.data.version&&(d(e.data.version),console.log("\uD83D\uDCF1 Current app version:",e.data.version))},navigator.serviceWorker.controller.postMessage({type:"GET_VERSION"},[e.port2])}catch(e){console.error("Error getting version:",e)}},u=()=>{o().fire({icon:"info",title:"App Update Available!",text:"A new version of Instra Global is available. Update now for the latest features and improvements.",showCancelButton:!0,confirmButtonText:"Update Now",cancelButtonText:"Later",confirmButtonColor:"#3b82f6",cancelButtonColor:"#6b7280",allowOutsideClick:!1,allowEscapeKey:!1}).then(e=>{e.isConfirmed?x():setTimeout(()=>{s&&h()},18e5)})},h=()=>{o().fire({icon:"warning",title:"Update Reminder",text:"Please update the app to ensure optimal performance and security.",confirmButtonText:"Update Now",confirmButtonColor:"#3b82f6",timer:1e4,timerProgressBar:!0}).then(e=>{e.isConfirmed&&x()})},x=async()=>{if(!l||!l.waiting)return void console.log("No waiting service worker found");try{o().fire({title:"Updating App...",text:"Please wait while we update the app.",allowOutsideClick:!1,allowEscapeKey:!1,showConfirmButton:!1,didOpen:()=>{o().showLoading()}}),l.waiting.postMessage({type:"SKIP_WAITING"}),navigator.serviceWorker.addEventListener("controllerchange",()=>{console.log("\uD83D\uDD04 New service worker took control, reloading..."),"caches"in window&&caches.keys().then(e=>{e.forEach(e=>{e.includes("instra-global")&&caches.delete(e)})}),window.location.reload()})}catch(e){console.error("Error applying update:",e),o().fire({icon:"error",title:"Update Failed",text:"Failed to update the app. Please refresh the page manually.",confirmButtonText:"Refresh Page"}).then(()=>{window.location.reload()})}},g=()=>{console.log("\uD83D\uDCBE Saving data before page unload")};return(0,r.useEffect)(()=>{let e=setInterval(()=>{l&&(console.log("\uD83D\uDD0D Checking for app updates..."),l.update())},18e5);return()=>clearInterval(e)},[l]),(0,a.jsxs)(a.Fragment,{children:[t,s&&(0,a.jsx)("div",{className:"fixed bottom-4 right-4 z-50",children:(0,a.jsxs)("button",{onClick:x,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2 animate-pulse",children:[(0,a.jsx)("i",{className:"fas fa-download"}),(0,a.jsx)("span",{children:"Update Available"})]})}),!1]})}},8802:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,9398,23)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,3711)),Promise.resolve().then(s.bind(s,5080)),Promise.resolve().then(s.bind(s,5170)),Promise.resolve().then(s.bind(s,1340)),Promise.resolve().then(s.bind(s,7320))},9398:e=>{e.exports={style:{fontFamily:"'Poppins', 'Poppins Fallback'",fontStyle:"normal"},className:"__className_51684b",variable:"__variable_51684b"}}},e=>{var t=t=>e(e.s=t);e.O(0,[2756,2992,7416,8320,8818,6681,8441,1684,7358],()=>t(8802)),_N_E=e.O()}]);