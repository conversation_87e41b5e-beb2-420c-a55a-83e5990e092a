(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{1469:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,s){for(var t in s)Object.defineProperty(e,t,{enumerable:!0,get:s[t]})}(s,{default:function(){return o},getImageProps:function(){return n}});let a=t(8229),r=t(8883),i=t(3063),l=a._(t(1193));function n(e){let{props:s}=(0,r.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,t]of Object.entries(s))void 0===t&&delete s[e];return{props:s}}let o=i.Image},6766:(e,s,t)=>{"use strict";t.d(s,{default:()=>r.a});var a=t(1469),r=t.n(a)},8565:(e,s,t)=>{Promise.resolve().then(t.bind(t,9690))},9690:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var a=t(5155),r=t(2115),i=t(6874),l=t.n(i),n=t(6766),o=t(3004),c=t(6104),d=t(6681),m=t(4752),u=t.n(m);function h(){let{user:e,loading:s}=(0,d.hD)(),[t,i]=(0,r.useState)(""),[m,h]=(0,r.useState)(""),[f,x]=(0,r.useState)(!1),[g,p]=(0,r.useState)(!1);(0,r.useEffect)(()=>{e&&!s&&(window.location.href="/dashboard")},[e,s]);let b=async e=>{if(e.preventDefault(),!t||!m)return void u().fire({icon:"error",title:"Error",text:"Please fill in all fields",background:"rgba(255, 255, 255, 0.95)",backdrop:"rgba(0, 0, 0, 0.8)"});x(!0);try{await (0,o.x9)(c.auth,t,m)}catch(s){console.error("Login error:",s);let e="An error occurred during login";switch(s.code){case"auth/user-not-found":e="No account found with this email address";break;case"auth/wrong-password":e="Incorrect password";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/user-disabled":e="This account has been disabled";break;case"auth/too-many-requests":e="Too many failed attempts. Please try again later";break;default:e=s.message||"Login failed"}u().fire({icon:"error",title:"Login Failed",text:e,background:"rgba(255, 255, 255, 0.95)",backdrop:"rgba(0, 0, 0, 0.8)"}),h("")}finally{x(!1)}};return s?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"spinner"})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,a.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,a.jsx)(n.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:50,height:50,className:"mr-3"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-white",children:"Instra Global"})]}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Welcome Back"}),(0,a.jsx)("p",{className:"text-white/80",children:"Sign in to continue earning"})]}),(0,a.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address"}),(0,a.jsx)("input",{type:"email",id:"email",value:t,onChange:e=>i(e.target.value),className:"form-input",placeholder:"Enter your email",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:g?"text":"password",id:"password",value:m,onChange:e=>h(e.target.value),className:"form-input pr-12",placeholder:"Enter your password",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>p(!g),className:"password-toggle-btn","aria-label":g?"Hide password":"Show password",children:(0,a.jsx)("i",{className:"fas ".concat(g?"fa-eye-slash":"fa-eye")})})]})]}),(0,a.jsx)("button",{type:"submit",disabled:f,className:"w-full btn-primary flex items-center justify-center",children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Logging in..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Login"]})})]}),(0,a.jsxs)("div",{className:"mt-6 text-center space-y-3",children:[(0,a.jsx)(l(),{href:"/forgot-password",className:"text-white/80 hover:text-white transition-colors",children:"Forgot your password?"}),(0,a.jsxs)("div",{className:"text-white/60",children:["Don't have an account?"," ",(0,a.jsx)(l(),{href:"/register",className:"text-white font-semibold hover:underline",children:"Sign up here"})]})]}),(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsxs)(l(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,8818,6874,3063,6681,8441,1684,7358],()=>s(8565)),_N_E=e.O()}]);