(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2922,7718],{2649:(e,t,s)=>{"use strict";s.d(t,{Ou:()=>m,Ov:()=>v,PY:()=>D,ck:()=>f,e5:()=>w,iM:()=>g,lA:()=>x,rB:()=>p,tv:()=>b,wh:()=>y});var a=s(2144),r=s(6104);async function i(){let{auth:e}=await Promise.resolve().then(s.bind(s,6104)),t=e.currentUser;if(!t)throw Error("User not authenticated");try{await t.getIdToken(!0),console.log("✅ User token refreshed and validated")}catch(e){throw console.error("❌ Error refreshing user token:",e),Error("Authentication token expired. Please refresh the page.")}}let o=(0,a.Qg)(r.Cn,"getUserWorkData"),n=(0,a.Qg)(r.Cn,"submitTranslationBatch"),c=(0,a.Qg)(r.Cn,"getUserDashboardData"),l=((0,a.Qg)(r.Cn,"getAdminDashboardData"),(0,a.Qg)(r.Cn,"getUserTransactions")),d=(0,a.Qg)(r.Cn,"processWithdrawalRequest"),u=(0,a.Qg)(r.Cn,"processDailyActiveDays"),h=((0,a.Qg)(r.Cn,"processDailyCopyPasteReduction"),(0,a.Qg)(r.Cn,"grantCopyPastePermission"),(0,a.Qg)(r.Cn,"updateUserPlan"),(0,a.Qg)(r.Cn,"getPlatformStats")),m={functionsUsed:0,firestoreReadsAvoided:0,firestoreWritesOptimized:0,incrementFunctionUsage(){this.functionsUsed++,console.log("\uD83D\uDE80 Firebase Functions used: ".concat(this.functionsUsed))},addReadsAvoided(e){this.firestoreReadsAvoided+=e,console.log("\uD83D\uDCB0 Firestore reads avoided: ".concat(e," (Total: ").concat(this.firestoreReadsAvoided,")"))},addWritesOptimized(e){this.firestoreWritesOptimized+=e,console.log("⚡ Firestore writes optimized: ".concat(e," (Total: ").concat(this.firestoreWritesOptimized,")"))},getStats(){return{functionsUsed:this.functionsUsed,firestoreReadsAvoided:this.firestoreReadsAvoided,firestoreWritesOptimized:this.firestoreWritesOptimized,estimatedCostSavings:(36e-5*this.firestoreReadsAvoided+.00108*this.firestoreWritesOptimized).toFixed(4)}}};async function p(){try{console.log("\uD83D\uDE80 Fetching user work data via Firebase Function..."),await i();let e=(await o()).data;return m.incrementFunctionUsage(),m.addReadsAvoided(4),console.log("✅ User work data fetched successfully:",e),e}catch(t){var e;if(console.error("❌ Error fetching user work data:",t),"unauthenticated"===t.code||(null==(e=t.message)?void 0:e.includes("User must be authenticated")))throw Error("Authentication required. Please refresh the page and try again.");throw Error("Failed to fetch user work data")}}async function g(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;try{console.log("\uD83D\uDE80 Submitting translation batch via Firebase Function: ".concat(e," translations")),await i();let t=(await n({batchSize:e})).data;return m.incrementFunctionUsage(),m.addWritesOptimized(3),console.log("✅ Translation batch submitted successfully:",t),t}catch(e){var t;if(console.error("❌ Error submitting translation batch:",e),"unauthenticated"===e.code||(null==(t=e.message)?void 0:t.includes("User must be authenticated")))throw Error("Authentication required. Please refresh the page and try again.");if("permission-denied"===e.code)throw Error("Permission denied. Please check your account status.");throw Error("Failed to submit translation batch")}}async function y(){try{console.log("\uD83D\uDE80 Fetching user dashboard data via Firebase Function..."),await i();let e=(await c()).data;return m.incrementFunctionUsage(),m.addReadsAvoided(3),console.log("✅ User dashboard data fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching user dashboard data:",e),Error("Failed to fetch user dashboard data")}}async function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20,t=arguments.length>1?arguments[1]:void 0;try{console.log("\uD83D\uDE80 Fetching user transactions via Firebase Function: limit=".concat(e));let s=(await l({limit:e,startAfter:t})).data;return m.incrementFunctionUsage(),m.addReadsAvoided(Math.max(0,100-e)),console.log("✅ User transactions fetched successfully:",s),s}catch(e){throw console.error("❌ Error fetching user transactions:",e),Error("Failed to fetch user transactions")}}async function f(e,t){try{console.log("\uD83D\uDE80 Processing withdrawal request via Firebase Function: ₹".concat(e));let s=(await d({amount:e,upiId:t})).data;return m.incrementFunctionUsage(),m.addWritesOptimized(2),console.log("✅ Withdrawal request processed successfully:",s),s}catch(e){throw console.error("❌ Error processing withdrawal request:",e),Error("Failed to process withdrawal request")}}async function w(){try{console.log("\uD83D\uDE80 Processing daily active days via Firebase Function...");let e=(await u()).data;return m.incrementFunctionUsage(),m.addWritesOptimized(e.updated||0),console.log("✅ Daily active days processed successfully:",e),e}catch(e){throw console.error("❌ Error processing daily active days:",e),Error("Failed to process daily active days")}}async function b(){try{console.log("\uD83D\uDE80 Fetching platform stats via Firebase Function...");let e=(await h()).data;return m.incrementFunctionUsage(),m.addReadsAvoided(10),console.log("✅ Platform stats fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching platform stats:",e),Error("Failed to fetch platform stats")}}async function v(){try{console.log("\uD83D\uDE80 Getting optimized user work data..."),await i();let e=await o();return console.log("✅ Optimized user work data retrieved"),e.data}catch(e){throw console.error("❌ Error getting optimized user work data:",e),e}}async function D(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;try{console.log("\uD83D\uDE80 Submitting optimized translation batch: ".concat(e," translations"));let t=await n({batchSize:e});return console.log("✅ Optimized translation batch submitted"),t.data}catch(e){throw console.error("❌ Error submitting optimized translation batch:",e),e}}},4172:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var a=s(5155),r=s(2115),i=s(6681),o=s(7718),n=s(2649);function c(){let{user:e,loading:t}=(0,i.Nu)(),[c,l]=(0,r.useState)(null),[d,u]=(0,r.useState)(!1),h=async()=>{u(!0),l(null);try{console.log("\uD83E\uDDEA Testing daily reduction processes..."),console.log("1. Testing active days increment via Firebase Function...");let e=await (0,n.e5)();console.log("2. Testing copy-paste reduction...");let t=await (0,o.Mk)();l({success:!0,activeDaysResult:e,copyPasteResult:t,message:"Daily reduction test completed successfully"})}catch(e){l({success:!1,error:e.message,message:"Daily reduction test failed"})}finally{u(!1)}},m=async()=>{let e=prompt("Enter user email to check copy-paste status:");if(e){u(!0);try{let{searchUsers:t}=await Promise.all([s.e(3592),s.e(6779)]).then(s.bind(s,6779)),a=(await t(e)).find(t=>{var s;return(null==(s=t.email)?void 0:s.toLowerCase())===e.toLowerCase()});if(!a)return void l({success:!1,message:"User not found: ".concat(e)});let r=await (0,o.checkCopyPastePermission)(a.id);l({success:!0,userEmail:e,userId:a.id,copyPasteStatus:r,message:"Copy-paste status retrieved successfully"})}catch(e){l({success:!1,error:e.message,message:"Failed to check copy-paste status"})}finally{u(!1)}}};return t?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,a.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Sample Upload & Daily Reduction"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-white font-bold mb-3",children:"1. Download Test CSV"}),(0,a.jsx)("p",{className:"text-white/80 mb-3",children:"Download a test CSV file with sample data including copy-paste days."}),(0,a.jsxs)("button",{onClick:()=>{let e=new Blob(["email,totalTranslations,walletBalance,activeDays,copyPasteDays\<EMAIL>,50,250,10,7\<EMAIL>,100,500,15,14\<EMAIL>,25,125,5,3\<EMAIL>,75,375,12,0"],{type:"text/csv"}),t=URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="test-sample-upload.csv",s.click(),URL.revokeObjectURL(t)},className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Download Test CSV"]})]}),(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-white font-bold mb-3",children:"2. Upload Test Data"}),(0,a.jsxs)("p",{className:"text-white/80 mb-3",children:["Go to ",(0,a.jsx)("strong",{children:"Admin → Simple Upload"})," and upload the test CSV file."]}),(0,a.jsxs)("a",{href:"/admin/simple-upload",className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg inline-block",children:[(0,a.jsx)("i",{className:"fas fa-upload mr-2"}),"Go to Simple Upload"]})]}),(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-white font-bold mb-3",children:"3. Test Daily Reduction"}),(0,a.jsx)("p",{className:"text-white/80 mb-3",children:"Test the daily reduction process for active days and copy-paste permissions."}),(0,a.jsx)("button",{onClick:h,disabled:d,className:"bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-4 h-4 inline-block"}),"Testing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-clock mr-2"}),"Test Daily Reduction"]})})]}),(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-white font-bold mb-3",children:"4. Check User Copy-Paste Status"}),(0,a.jsx)("p",{className:"text-white/80 mb-3",children:"Check the copy-paste permission status for a specific user."}),(0,a.jsx)("button",{onClick:m,disabled:d,className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-4 h-4 inline-block"}),"Checking..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-search mr-2"}),"Check User Status"]})})]}),(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-white font-bold mb-3",children:"5. Test Export with Copy-Paste Days"}),(0,a.jsx)("p",{className:"text-white/80 mb-3",children:"Export users to verify copy-paste remaining days are included."}),(0,a.jsxs)("a",{href:"/admin/users",className:"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg inline-block",children:[(0,a.jsx)("i",{className:"fas fa-users mr-2"}),"Go to Users & Export"]})]}),c&&(0,a.jsxs)("div",{className:"rounded-lg p-4 ".concat(c.success?"bg-green-500/20 border border-green-500/30":"bg-red-500/20 border border-red-500/30"),children:[(0,a.jsx)("h3",{className:"font-bold ".concat(c.success?"text-green-400":"text-red-400"),children:c.success?"Test Results - Success!":"Test Results - Failed"}),(0,a.jsx)("p",{className:"text-white mt-2",children:c.message}),c.activeDaysResult&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Active Days Result:"}),(0,a.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(c.activeDaysResult,null,2)})]}),c.copyPasteResult&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Copy-Paste Reduction Result:"}),(0,a.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(c.copyPasteResult,null,2)})]}),c.copyPasteStatus&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"User Copy-Paste Status:"}),(0,a.jsxs)("div",{className:"text-white/80 text-sm mt-1",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Email:"})," ",c.userEmail]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"User ID:"})," ",c.userId]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Has Permission:"})," ",c.copyPasteStatus.hasPermission?"Yes":"No"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Days Remaining:"})," ",c.copyPasteStatus.daysRemaining]}),c.copyPasteStatus.expiryDate&&(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Expiry Date:"})," ",c.copyPasteStatus.expiryDate.toDateString()]})]})]}),c.error&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-red-500/20 rounded",children:[(0,a.jsx)("h4",{className:"text-red-400 font-semibold",children:"Error Details:"}),(0,a.jsx)("p",{className:"text-white/80 text-sm mt-1",children:c.error})]})]}),(0,a.jsxs)("div",{className:"bg-blue-500/10 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-blue-400 font-bold mb-3",children:"Testing Instructions:"}),(0,a.jsxs)("ol",{className:"text-blue-300 text-sm space-y-2 list-decimal list-inside",children:[(0,a.jsx)("li",{children:"Download the test CSV file"}),(0,a.jsx)("li",{children:"Go to Simple Upload and upload the CSV"}),(0,a.jsx)("li",{children:"Verify users are updated with copy-paste permissions"}),(0,a.jsx)("li",{children:"Test daily reduction to see copy-paste days decrease"}),(0,a.jsx)("li",{children:"Check individual user status"}),(0,a.jsx)("li",{children:"Export users to verify copy-paste remaining days are included"}),(0,a.jsx)("li",{children:"Check browser console for detailed logs"})]})]})]})]})})})}},6147:(e,t,s)=>{Promise.resolve().then(s.bind(s,4172))},7718:(e,t,s)=>{"use strict";s.d(t,{Mk:()=>h,checkCopyPastePermission:()=>c,grantCopyPastePermission:()=>l,i7:()=>u,removeCopyPastePermission:()=>d});var a=s(6104),r=s(5317);let i={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},o={users:"users"};class n{static async checkCopyPastePermission(e){try{let t=await (0,r.x7)((0,r.H9)(a.db,o.users,e));if(!t.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let s=t.data()[i.quickTranslationAdvantageExpiry];if(!s)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let n=s.toDate(),c=new Date,l=n>c,d=l?Math.ceil((n.getTime()-c.getTime())/864e5):0;return{hasPermission:l,daysRemaining:d,expiryDate:n}}catch(t){return console.error("Error checking copy-paste permission for user ".concat(e,":"),t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,t){try{let s=new Date;s.setDate(s.getDate()+t);let n=(0,r.H9)(a.db,o.users,e);await (0,r.mZ)(n,{[i.quickTranslationAdvantageExpiry]:r.Dc.fromDate(s),[i.lastCopyPasteReduction]:r.Dc.now()}),console.log("✅ Granted copy-paste permission to user ".concat(e," for ").concat(t," days (expires: ").concat(s.toDateString(),")"))}catch(t){throw console.error("Error granting copy-paste permission to user ".concat(e,":"),t),t}}static async removeCopyPastePermission(e){try{let t=(0,r.H9)(a.db,o.users,e);await (0,r.mZ)(t,{[i.quickTranslationAdvantageExpiry]:null}),console.log("✅ Removed copy-paste permission from user ".concat(e))}catch(t){throw console.error("Error removing copy-paste permission from user ".concat(e,":"),t),t}}static async reduceCopyPasteDays(e){try{let t=await (0,r.x7)((0,r.H9)(a.db,o.users,e));if(!t.exists())return{reduced:!1,daysRemaining:0,expired:!1};let s=t.data(),n=s[i.quickTranslationAdvantageExpiry],c=s[i.lastCopyPasteReduction];if(!n)return{reduced:!1,daysRemaining:0,expired:!1};let l=new Date().toDateString();if((c?c.toDate().toDateString():null)===l){let e=n.toDate(),t=new Date,s=Math.max(0,Math.ceil((e.getTime()-t.getTime())/864e5));return{reduced:!1,daysRemaining:s,expired:0===s}}let d=n.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let h=(0,r.H9)(a.db,o.users,e);if(u<=new Date)return await (0,r.mZ)(h,{[i.quickTranslationAdvantageExpiry]:null,[i.lastCopyPasteReduction]:r.Dc.now()}),console.log("\uD83D\uDCC5 Copy-paste permission expired for user ".concat(e)),{reduced:!0,daysRemaining:0,expired:!0};{await (0,r.mZ)(h,{[i.quickTranslationAdvantageExpiry]:r.Dc.fromDate(u),[i.lastCopyPasteReduction]:r.Dc.now()});let t=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log("\uD83D\uDCC5 Reduced copy-paste days for user ".concat(e,": ").concat(t," days remaining")),{reduced:!0,daysRemaining:t,expired:!1}}}catch(t){return console.error("Error reducing copy-paste days for user ".concat(e,":"),t),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,r.getDocs)((0,r.collection)(a.db,o.users)),t=0,s=0,i=0,c=0;for(let a of e.docs)try{t++;let e=await n.reduceCopyPasteDays(a.id);e.reduced&&(s++,e.expired&&i++)}catch(e){c++,console.error("Error processing copy-paste reduction for user ".concat(a.id,":"),e)}return console.log("✅ Daily copy-paste reduction complete:"),console.log("   - Processed: ".concat(t," users")),console.log("   - Reduced: ".concat(s," users")),console.log("   - Expired: ".concat(i," users")),console.log("   - Errors: ".concat(c," users")),{processed:t,reduced:s,expired:i,errors:c}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let t=await n.checkCopyPastePermission(e);return{hasPermission:t.hasPermission,daysRemaining:t.daysRemaining,expiryDate:t.expiryDate?t.expiryDate.toDateString():null}}catch(t){return console.error("Error getting copy-paste status for user ".concat(e,":"),t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let c=n.checkCopyPastePermission,l=n.grantCopyPastePermission,d=n.removeCopyPastePermission,u=n.reduceCopyPasteDays,h=n.processAllUsersCopyPasteReduction;n.getCopyPasteStatus}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6681,8441,1684,7358],()=>t(6147)),_N_E=e.O()}]);