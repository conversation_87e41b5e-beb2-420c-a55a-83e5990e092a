import { httpsCallable } from 'firebase/functions';
import { functions } from './firebase';

// Utility function to check if user is authenticated
export async function checkAuthenticationStatus(): Promise<{
  isAuthenticated: boolean;
  user: any;
  tokenValid: boolean;
  error?: string;
}> {
  try {
    const { auth } = await import('./firebase');
    const currentUser = auth.currentUser;

    if (!currentUser) {
      return {
        isAuthenticated: false,
        user: null,
        tokenValid: false,
        error: 'No user logged in'
      };
    }

    // Check if token is valid
    try {
      const token = await currentUser.getIdToken(false);

      return {
        isAuthenticated: true,
        user: {
          uid: currentUser.uid,
          email: currentUser.email,
          displayName: currentUser.displayName
        },
        tokenValid: !!token,
        error: undefined
      };
    } catch (tokenError) {
      return {
        isAuthenticated: true,
        user: {
          uid: currentUser.uid,
          email: currentUser.email,
          displayName: currentUser.displayName
        },
        tokenValid: false,
        error: 'Token invalid or expired'
      };
    }
  } catch (error: any) {
    return {
      isAuthenticated: false,
      user: null,
      tokenValid: false,
      error: error.message || 'Authentication check failed'
    };
  }
}

// Utility function to ensure user authentication and refresh token
async function ensureAuthenticated(): Promise<void> {
  const { auth } = await import('./firebase');
  const currentUser = auth.currentUser;

  if (!currentUser) {
    console.error('❌ No current user found');
    throw new Error('User not authenticated. Please log in again.');
  }

  // Check if user is still valid
  try {
    // First try to get the current token without forcing refresh
    const currentToken = await currentUser.getIdToken(false);

    if (!currentToken) {
      console.error('❌ No ID token available');
      throw new Error('Authentication token not available');
    }

    // Check token expiration (tokens expire after 1 hour)
    const tokenResult = await currentUser.getIdTokenResult(false);
    const expirationTime = new Date(tokenResult.expirationTime);
    const now = new Date();
    const timeUntilExpiry = expirationTime.getTime() - now.getTime();

    // If token expires in less than 5 minutes, refresh it
    if (timeUntilExpiry < 5 * 60 * 1000) {
      console.log('🔄 Token expires soon, refreshing...');
      await currentUser.getIdToken(true); // Force refresh
      console.log('✅ User token refreshed proactively');
    } else {
      console.log('✅ User token is valid');
    }
  } catch (tokenError: any) {
    console.error('❌ Error with user token:', tokenError);

    // Try to refresh the token as a last resort
    try {
      console.log('🔄 Attempting token refresh...');
      await currentUser.getIdToken(true); // Force refresh
      console.log('✅ User token refreshed after error');
    } catch (refreshError: any) {
      console.error('❌ Failed to refresh token:', refreshError);

      // If token refresh fails, the user needs to re-authenticate
      if (refreshError.code === 'auth/user-token-expired' ||
          refreshError.code === 'auth/invalid-user-token') {
        throw new Error('Your session has expired. Please log in again.');
      }

      throw new Error('Authentication error. Please refresh the page and try again.');
    }
  }
}

// Firebase Functions for optimized Firestore operations
const getUserWorkDataFunction = httpsCallable(functions, 'getUserWorkData');
const submitTranslationBatchFunction = httpsCallable(functions, 'submitTranslationBatch');
const getUserDashboardDataFunction = httpsCallable(functions, 'getUserDashboardData');
const getAdminDashboardDataFunction = httpsCallable(functions, 'getAdminDashboardData');
const getUserTransactionsFunction = httpsCallable(functions, 'getUserTransactions');
const processWithdrawalRequestFunction = httpsCallable(functions, 'processWithdrawalRequest');
const processDailyActiveDaysFunction = httpsCallable(functions, 'processDailyActiveDays');
const processDailyCopyPasteReductionFunction = httpsCallable(functions, 'processDailyCopyPasteReduction');
const grantCopyPastePermissionFunction = httpsCallable(functions, 'grantCopyPastePermission');
const updateUserPlanFunction = httpsCallable(functions, 'updateUserPlan');
const getPlatformStatsFunction = httpsCallable(functions, 'getPlatformStats');

// Type definitions for Firebase Functions
export interface UserWorkData {
  userProfile: {
    name: string;
    email: string;
    plan: string;
    wallet: number;
    activeDays: number;
    joinedDate: any;
  };
  translationProgress: {
    totalTranslations: number;
    todayTranslations: number;
    remainingTranslations: number;
    isNewDay: boolean;
  };
  planStatus: {
    expired: boolean;
    daysLeft: number;
    earningPerBatch: number;
  };
  copyPastePermissions: {
    hasQuickAdvantage: boolean;
    daysRemaining: number;
  };
  workAccess: {
    canWork: boolean;
    reason: string | null;
  };
  timestamp: string;
}

export interface UserDashboardData {
  profile: {
    name: string;
    email: string;
    mobile: string;
    plan: string;
    activeDays: number;
    joinedDate: string;
  };
  wallet: {
    balance: number;
  };
  translations: {
    total: number;
    today: number;
  };
  recentTransactions: any[];
  timestamp: string;
}

export interface TranslationBatchResult {
  success: boolean;
  newTotalTranslations: number;
  newTodayTranslations: number;
  earningAmount: number;
  newWallet: number;
  remainingTranslations: number;
}

export interface AdminDashboardData {
  userStats: {
    total: number;
    trial: number;
    paid: number;
    admin: number;
    activeToday: number;
  };
  financialStats: {
    totalWallet: number;
    totalEarnings: number;
    totalWithdrawals: number;
    pendingWithdrawals: number;
    platformBalance: number;
  };
  translationStats: {
    totalTranslations: number;
    averagePerUser: number;
  };
  pendingWithdrawalsCount: number;
  timestamp: string;
}

export interface PlatformStats {
  users: {
    total: number;
    activeToday: number;
    activityRate: number;
  };
  translations: {
    total: number;
    averagePerUser: number;
  };
  financials: {
    totalEarnings: number;
    totalWithdrawals: number;
    totalWallet: number;
    platformRevenue: number;
  };
  timestamp: string;
}

// Cost optimization tracking
export const COST_OPTIMIZATION_STATS = {
  functionsUsed: 0,
  firestoreReadsAvoided: 0,
  firestoreWritesOptimized: 0,

  incrementFunctionUsage() {
    this.functionsUsed++;
    console.log(`🚀 Firebase Functions used: ${this.functionsUsed}`);
  },

  addReadsAvoided(count: number) {
    this.firestoreReadsAvoided += count;
    console.log(`💰 Firestore reads avoided: ${count} (Total: ${this.firestoreReadsAvoided})`);
  },

  addWritesOptimized(count: number) {
    this.firestoreWritesOptimized += count;
    console.log(`⚡ Firestore writes optimized: ${count} (Total: ${this.firestoreWritesOptimized})`);
  },

  getStats() {
    return {
      functionsUsed: this.functionsUsed,
      firestoreReadsAvoided: this.firestoreReadsAvoided,
      firestoreWritesOptimized: this.firestoreWritesOptimized,
      estimatedCostSavings: (this.firestoreReadsAvoided * 0.00036 + this.firestoreWritesOptimized * 0.00108).toFixed(4)
    };
  }
};

/**
 * Get comprehensive user work data with minimal Firestore reads
 * Replaces multiple separate data fetching operations
 */
export async function getUserWorkData(): Promise<UserWorkData> {
  let retryCount = 0;
  const maxRetries = 2;

  while (retryCount <= maxRetries) {
    try {
      console.log(`🚀 Fetching user work data via Firebase Function... (attempt ${retryCount + 1}/${maxRetries + 1})`);

      // Ensure user is authenticated and token is fresh
      await ensureAuthenticated();

      const result = await getUserWorkDataFunction();
      const data = result.data as UserWorkData;

      // Track optimization
      COST_OPTIMIZATION_STATS.incrementFunctionUsage();
      COST_OPTIMIZATION_STATS.addReadsAvoided(4); // Avoided 4 separate Firestore reads

      console.log('✅ User work data fetched successfully:', data);
      return data;
    } catch (error: any) {
      console.error(`❌ Error fetching user work data (attempt ${retryCount + 1}):`, error);

      // Check if it's an authentication error
      if (error.code === 'unauthenticated' ||
          error.message?.includes('User must be authenticated') ||
          error.message?.includes('session has expired')) {

        if (retryCount < maxRetries) {
          console.log('🔄 Authentication error, retrying with fresh token...');
          retryCount++;

          // Wait a bit before retrying
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
          continue;
        } else {
          throw new Error('Authentication failed. Please refresh the page and log in again.');
        }
      }

      // Check if it's a network error
      if (error.code === 'unavailable' ||
          error.message?.includes('network') ||
          error.message?.includes('fetch')) {

        if (retryCount < maxRetries) {
          console.log('🔄 Network error, retrying...');
          retryCount++;

          // Wait longer for network errors
          await new Promise(resolve => setTimeout(resolve, 2000 * retryCount));
          continue;
        } else {
          throw new Error('Network error. Please check your connection and try again.');
        }
      }

      // For other errors, don't retry
      throw new Error(`Failed to fetch user work data: ${error.message || 'Unknown error'}`);
    }
  }

  throw new Error('Failed to fetch user work data after multiple attempts');
}

/**
 * Submit translation batch with atomic transaction
 * Replaces multiple separate Firestore operations
 */
export async function submitTranslationBatch(batchSize: number = 50): Promise<TranslationBatchResult> {
  try {
    console.log(`🚀 Submitting translation batch via Firebase Function: ${batchSize} translations`);

    // Ensure user is authenticated and token is fresh
    await ensureAuthenticated();

    const result = await submitTranslationBatchFunction({ batchSize });
    const data = result.data as TranslationBatchResult;

    // Track optimization
    COST_OPTIMIZATION_STATS.incrementFunctionUsage();
    COST_OPTIMIZATION_STATS.addWritesOptimized(3); // Avoided 3 separate write operations

    console.log('✅ Translation batch submitted successfully:', data);
    return data;
  } catch (error: any) {
    console.error('❌ Error submitting translation batch:', error);

    // If it's an authentication error, provide more specific error message
    if (error.code === 'unauthenticated' || error.message?.includes('User must be authenticated')) {
      throw new Error('Authentication required. Please refresh the page and try again.');
    }

    // If it's a permission error, provide helpful message
    if (error.code === 'permission-denied') {
      throw new Error('Permission denied. Please check your account status.');
    }

    throw new Error('Failed to submit translation batch');
  }
}

/**
 * Get user dashboard data with minimal reads
 * Combines profile, wallet, and recent transactions
 */
export async function getUserDashboardData(): Promise<UserDashboardData> {
  let retryCount = 0;
  const maxRetries = 2;

  while (retryCount <= maxRetries) {
    try {
      console.log(`🚀 Fetching user dashboard data via Firebase Function... (attempt ${retryCount + 1}/${maxRetries + 1})`);

      // Ensure user is authenticated and token is fresh
      await ensureAuthenticated();

      const result = await getUserDashboardDataFunction();
      const data = result.data as UserDashboardData;

      // Track optimization
      COST_OPTIMIZATION_STATS.incrementFunctionUsage();
      COST_OPTIMIZATION_STATS.addReadsAvoided(3); // Avoided 3 separate reads (user + transactions query)

      console.log('✅ User dashboard data fetched successfully:', data);
      return data;
    } catch (error: any) {
      console.error(`❌ Error fetching user dashboard data (attempt ${retryCount + 1}):`, error);

      // Check if it's an authentication error
      if (error.code === 'unauthenticated' ||
          error.message?.includes('User must be authenticated') ||
          error.message?.includes('session has expired')) {

        if (retryCount < maxRetries) {
          console.log('🔄 Authentication error, retrying with fresh token...');
          retryCount++;

          // Wait a bit before retrying
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
          continue;
        } else {
          throw new Error('Authentication failed. Please refresh the page and log in again.');
        }
      }

      // Check if it's a network error
      if (error.code === 'unavailable' ||
          error.message?.includes('network') ||
          error.message?.includes('fetch')) {

        if (retryCount < maxRetries) {
          console.log('🔄 Network error, retrying...');
          retryCount++;

          // Wait longer for network errors
          await new Promise(resolve => setTimeout(resolve, 2000 * retryCount));
          continue;
        } else {
          throw new Error('Network error. Please check your connection and try again.');
        }
      }

      // For other errors, don't retry
      throw new Error(`Failed to fetch user dashboard data: ${error.message || 'Unknown error'}`);
    }
  }

  throw new Error('Failed to fetch user dashboard data after multiple attempts');
}

/**
 * Get admin dashboard data with aggregated statistics
 */
export async function getAdminDashboardData(): Promise<AdminDashboardData> {
  try {
    console.log('🚀 Fetching admin dashboard data via Firebase Function...');

    const result = await getAdminDashboardDataFunction();
    const data = result.data as AdminDashboardData;

    // Track optimization
    COST_OPTIMIZATION_STATS.incrementFunctionUsage();
    COST_OPTIMIZATION_STATS.addReadsAvoided(15); // Avoided multiple collection scans

    console.log('✅ Admin dashboard data fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error fetching admin dashboard data:', error);
    throw new Error('Failed to fetch admin dashboard data');
  }
}

/**
 * Get user transactions with server-side pagination
 */
export async function getUserTransactions(limit: number = 20, startAfter?: string): Promise<any> {
  try {
    console.log(`🚀 Fetching user transactions via Firebase Function: limit=${limit}`);

    const result = await getUserTransactionsFunction({ limit, startAfter });
    const data = result.data;

    // Track optimization
    COST_OPTIMIZATION_STATS.incrementFunctionUsage();
    COST_OPTIMIZATION_STATS.addReadsAvoided(Math.max(0, 100 - limit)); // Avoided reading all transactions

    console.log('✅ User transactions fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error fetching user transactions:', error);
    throw new Error('Failed to fetch user transactions');
  }
}

/**
 * Process withdrawal request with validation
 */
export async function processWithdrawalRequest(amount: number, upiId: string): Promise<any> {
  try {
    console.log(`🚀 Processing withdrawal request via Firebase Function: ₹${amount}`);

    const result = await processWithdrawalRequestFunction({ amount, upiId });
    const data = result.data;

    // Track optimization
    COST_OPTIMIZATION_STATS.incrementFunctionUsage();
    COST_OPTIMIZATION_STATS.addWritesOptimized(2); // Atomic transaction instead of separate writes

    console.log('✅ Withdrawal request processed successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error processing withdrawal request:', error);
    throw new Error('Failed to process withdrawal request');
  }
}

/**
 * Admin function: Process daily active days for all users
 */
export async function processDailyActiveDays(): Promise<any> {
  try {
    console.log('🚀 Processing daily active days via Firebase Function...');

    const result = await processDailyActiveDaysFunction();
    const data = result.data;

    // Track optimization
    COST_OPTIMIZATION_STATS.incrementFunctionUsage();
    COST_OPTIMIZATION_STATS.addWritesOptimized((data as any).updated || 0); // Batch processing

    console.log('✅ Daily active days processed successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error processing daily active days:', error);
    throw new Error('Failed to process daily active days');
  }
}

/**
 * Admin function: Process daily copy-paste reduction for all users
 */
export async function processDailyCopyPasteReduction(): Promise<any> {
  try {
    console.log('🚀 Processing daily copy-paste reduction via Firebase Function...');

    const result = await processDailyCopyPasteReductionFunction();
    const data = result.data;

    // Track optimization
    COST_OPTIMIZATION_STATS.incrementFunctionUsage();
    COST_OPTIMIZATION_STATS.addWritesOptimized((data as any).reduced || 0); // Batch processing

    console.log('✅ Daily copy-paste reduction processed successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error processing daily copy-paste reduction:', error);
    throw new Error('Failed to process daily copy-paste reduction');
  }
}

/**
 * Admin function: Grant copy-paste permission to user
 */
export async function grantCopyPastePermission(userId: string, days: number): Promise<any> {
  try {
    console.log(`🚀 Granting copy-paste permission via Firebase Function: ${days} days to ${userId}`);

    const result = await grantCopyPastePermissionFunction({ userId, days });
    const data = result.data;

    // Track optimization
    COST_OPTIMIZATION_STATS.incrementFunctionUsage();

    console.log('✅ Copy-paste permission granted successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error granting copy-paste permission:', error);
    throw new Error('Failed to grant copy-paste permission');
  }
}

/**
 * Admin function: Update user plan
 */
export async function updateUserPlan(userId: string, plan: string, duration?: number): Promise<any> {
  try {
    console.log(`🚀 Updating user plan via Firebase Function: ${plan} for ${userId}`);

    const result = await updateUserPlanFunction({ userId, plan, duration });
    const data = result.data;

    // Track optimization
    COST_OPTIMIZATION_STATS.incrementFunctionUsage();

    console.log('✅ User plan updated successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error updating user plan:', error);
    throw new Error('Failed to update user plan');
  }
}

/**
 * Admin function: Get platform statistics
 */
export async function getPlatformStats(): Promise<PlatformStats> {
  try {
    console.log('🚀 Fetching platform stats via Firebase Function...');

    const result = await getPlatformStatsFunction();
    const data = result.data as PlatformStats;

    // Track optimization
    COST_OPTIMIZATION_STATS.incrementFunctionUsage();
    COST_OPTIMIZATION_STATS.addReadsAvoided(10); // Avoided multiple collection scans

    console.log('✅ Platform stats fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error fetching platform stats:', error);
    throw new Error('Failed to fetch platform stats');
  }
}

// Types
export interface UserWorkData {
  name: string;
  email: string;
  plan: string;
  wallet: number;
  totalTranslations: number;
  todayTranslations: number;
  canWork: boolean;
  earningPerBatch: number;
  planExpired: boolean;
  daysLeft: number;
  activeDays: number;
  hasCopyPastePermission: boolean;
  copyPasteDaysRemaining: number;
  isNewDay: boolean;
  lastTranslationDate: string | null;
}

export interface TranslationBatchResult {
  success: boolean;
  earningAmount: number;
  newTotalTranslations: number;
  newWalletBalance: number;
  newTodayTranslations: number;
}

export interface AdminDashboardData {
  users: {
    total: number;
    active: number;
    trial: number;
    paid: number;
  };
  financials: {
    totalWalletBalance: number;
    todayEarnings: number;
    todayWithdrawals: number;
    pendingWithdrawals: number;
    pendingWithdrawalAmount: number;
  };
  activity: {
    todayTransactions: number;
    activeUsers: number;
  };
  timestamp: string;
}

export interface UserTransaction {
  id: string;
  type: string;
  amount: number;
  description: string;
  status: string;
  date: string;
}

export interface UserTransactionsResult {
  transactions: UserTransaction[];
  hasMore: boolean;
  lastDocId: string | null;
}

export interface WithdrawalResult {
  success: boolean;
  withdrawalId: string;
  newWalletBalance: number;
}

/**
 * Get optimized user work data with minimal Firestore reads
 * Replaces multiple separate API calls with a single function call
 */
export async function getOptimizedUserWorkData(): Promise<UserWorkData> {
  try {
    console.log('🚀 Getting optimized user work data...');

    // Ensure user is authenticated and token is fresh
    await ensureAuthenticated();

    const result = await getUserWorkDataFunction();
    console.log('✅ Optimized user work data retrieved');
    return result.data as UserWorkData;
  } catch (error) {
    console.error('❌ Error getting optimized user work data:', error);
    throw error;
  }
}

/**
 * Submit translation batch with optimized writes
 * Combines multiple operations into a single atomic transaction
 */
export async function submitOptimizedTranslationBatch(batchSize: number = 50): Promise<TranslationBatchResult> {
  try {
    console.log(`🚀 Submitting optimized translation batch: ${batchSize} translations`);
    const result = await submitTranslationBatchFunction({ batchSize });
    console.log('✅ Optimized translation batch submitted');
    return result.data as TranslationBatchResult;
  } catch (error) {
    console.error('❌ Error submitting optimized translation batch:', error);
    throw error;
  }
}

/**
 * Get optimized admin dashboard data with minimal reads
 * Aggregates data from multiple collections in a single call
 */
export async function getOptimizedAdminDashboardData(): Promise<AdminDashboardData> {
  try {
    console.log('🚀 Getting optimized admin dashboard data...');
    const result = await getAdminDashboardDataFunction();
    console.log('✅ Optimized admin dashboard data retrieved');
    return result.data as AdminDashboardData;
  } catch (error) {
    console.error('❌ Error getting optimized admin dashboard data:', error);
    throw error;
  }
}

/**
 * Get user transactions with server-side pagination
 * Reduces client-side data processing and network overhead
 */
export async function getOptimizedUserTransactions(
  limit: number = 20,
  lastDocId: string | null = null,
  type: string | null = null
): Promise<UserTransactionsResult> {
  try {
    console.log(`🚀 Getting optimized user transactions: limit=${limit}, type=${type}`);
    const result = await getUserTransactionsFunction({ limit, lastDocId, type });
    console.log('✅ Optimized user transactions retrieved');
    return result.data as UserTransactionsResult;
  } catch (error) {
    console.error('❌ Error getting optimized user transactions:', error);
    throw error;
  }
}

/**
 * Process withdrawal request with optimized validation and writes
 * Combines validation, wallet debit, and record creation in a single transaction
 */
export async function processOptimizedWithdrawalRequest(
  amount: number,
  bankDetails: any
): Promise<WithdrawalResult> {
  try {
    console.log(`🚀 Processing optimized withdrawal request: ₹${amount}`);
    const result = await processWithdrawalRequestFunction({ amount, bankDetails });
    console.log('✅ Optimized withdrawal request processed');
    return result.data as WithdrawalResult;
  } catch (error) {
    console.error('❌ Error processing optimized withdrawal request:', error);
    throw error;
  }
}

/**
 * Utility function to check if Firebase Functions are available
 * Useful for fallback to direct Firestore operations if needed
 */
export function areFirebaseFunctionsAvailable(): boolean {
  try {
    return !!functions;
  } catch (error) {
    console.warn('Firebase Functions not available:', error);
    return false;
  }
}

/**
 * Get estimated cost savings from using Firebase Functions
 * This is for monitoring and optimization purposes
 */
export function getEstimatedCostSavings() {
  return {
    userWorkData: {
      directReads: 5, // getUserData, getTranslationData, checkCopyPaste, checkPlanExpiry, getSettings
      optimizedReads: 1, // Single function call
      savings: '80% reduction in reads'
    },
    translationBatch: {
      directWrites: 3, // updateUser, addTransaction, updateWallet
      optimizedWrites: 1, // Single transaction
      savings: '67% reduction in writes'
    },
    adminDashboard: {
      directReads: 15, // Multiple collection scans
      optimizedReads: 3, // Parallel collection reads
      savings: '80% reduction in reads'
    },
    userTransactions: {
      directReads: 'Variable (all transactions)', 
      optimizedReads: 'Paginated (20 per call)',
      savings: 'Up to 90% reduction for large datasets'
    }
  };
}


