(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2439,5587],{2439:(e,t,a)=>{"use strict";a.d(t,{ActiveDaysService:()=>o,S3:()=>y,i7:()=>l,isUserPlanExpired:()=>p,mH:()=>d,nd:()=>u,updateUserActiveDays:()=>n});var s=a(6104),r=a(5317);let i={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},c={users:"users"};class o{static async calculateActiveDays(e){try{var t,a;let i,l=await (0,r.x7)((0,r.H9)(s.db,c.users,e));if(!l.exists())return console.error("User ".concat(e," not found")),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let n=l.data(),d=(null==(t=n.joinedDate)?void 0:t.toDate())||new Date,u=null==(a=n.lastActiveDayUpdate)?void 0:a.toDate(),y=n.activeDays||0,p=n.plan||"Trial",g=new Date,v=g.toDateString(),D=u?u.toDateString():null;if(console.log("\uD83D\uDCC5 Calculating active days for user ".concat(e,":")),console.log("   - Joined: ".concat(d.toDateString())),console.log("   - Current active days: ".concat(y)),console.log("   - Last update: ".concat(D||"Never")),console.log("   - Today: ".concat(v)),console.log("   - Plan: ".concat(p)),console.log("   - Is new day: ".concat(D!==v)),D===v)return console.log("✅ Already updated today for user ".concat(e)),{activeDays:y,shouldUpdate:!1,isNewDay:!1};if("Admin"===p)return console.log("⏭️ Skipping active days increment for admin user ".concat(e)),await o.updateLastActiveDayUpdate(e),{activeDays:y,shouldUpdate:!1,isNewDay:!0};if(await o.isUserOnLeaveToday(e))return console.log("\uD83C\uDFD6️ User ".concat(e," is on leave today, not incrementing active days")),await o.updateLastActiveDayUpdate(e),{activeDays:y,shouldUpdate:!1,isNewDay:!0};return i="Trial"===p?Math.floor((g.getTime()-d.getTime())/864e5)+1:y+1,console.log("\uD83D\uDCC8 New active days calculated: ".concat(y," → ").concat(i)),{activeDays:i,shouldUpdate:i!==y,isNewDay:!0}}catch(t){return console.error("Error calculating active days for user ".concat(e,":"),t),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(e){try{let t=await o.calculateActiveDays(e);if(t.shouldUpdate){let a=(0,r.H9)(s.db,c.users,e);await (0,r.mZ)(a,{[i.activeDays]:t.activeDays,[i.lastActiveDayUpdate]:r.Dc.now()}),console.log("✅ Updated active days for user ".concat(e,": ").concat(t.activeDays))}else t.isNewDay&&await o.updateLastActiveDayUpdate(e);return t.activeDays}catch(t){throw console.error("Error updating active days for user ".concat(e,":"),t),t}}static async updateLastActiveDayUpdate(e){try{let t=(0,r.H9)(s.db,c.users,e);await (0,r.mZ)(t,{[i.lastActiveDayUpdate]:r.Dc.now()})}catch(t){console.error("Error updating last active day timestamp for user ".concat(e,":"),t)}}static async isUserOnLeaveToday(e){try{let{isUserOnLeave:t}=await a.e(9567).then(a.bind(a,9567));return await t(e,new Date)}catch(t){return console.error("Error checking leave status for user ".concat(e,":"),t),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let e=await (0,r.getDocs)((0,r.collection)(s.db,c.users)),t=0,a=0,i=0;for(let s of e.docs)try{t++;let e=await o.calculateActiveDays(s.id);(e.shouldUpdate||e.isNewDay)&&(await o.updateUserActiveDays(s.id),e.shouldUpdate&&a++)}catch(e){i++,console.error("Error processing active days for user ".concat(s.id,":"),e)}return console.log("✅ Daily active days processing complete:"),console.log("   - Processed: ".concat(t," users")),console.log("   - Updated: ".concat(a," users")),console.log("   - Errors: ".concat(i," users")),{processed:t,updated:a,errors:i}}catch(e){throw console.error("Error in daily active days processing:",e),e}}static async getUserActiveDays(e){try{let t=await (0,r.x7)((0,r.H9)(s.db,c.users,e));if(!t.exists())return 0;return t.data().activeDays||0}catch(t){return console.error("Error getting active days for user ".concat(e,":"),t),0}}static async initializeActiveDaysForNewUser(e){try{let t=(0,r.H9)(s.db,c.users,e);await (0,r.mZ)(t,{[i.activeDays]:1,[i.lastActiveDayUpdate]:r.Dc.now()}),console.log("✅ Initialized active days for new user ".concat(e,": Day 1"))}catch(t){throw console.error("Error initializing active days for user ".concat(e,":"),t),t}}static async getActiveDaysDisplay(e){try{let t,a=await (0,r.x7)((0,r.H9)(s.db,c.users,e));if(!a.exists())return{current:0,total:2,displayText:"0/2"};let i=a.data(),o=i.plan||"Trial",l=i.activeDays||0;return t="Trial"===o?2:30,{current:l,total:t,displayText:"".concat(l,"/").concat(t)}}catch(t){return console.error("Error getting active days display for user ".concat(e,":"),t),{current:0,total:2,displayText:"0/2"}}}static async isUserPlanExpired(e){try{let t=await (0,r.x7)((0,r.H9)(s.db,c.users,e));if(!t.exists())return{expired:!0,reason:"User not found"};let a=t.data(),i=a.plan||"Trial",o=a.activeDays||0,l=a.planExpiry;if(console.log("\uD83D\uDCC5 Checking plan expiry for user ".concat(e,":"),{plan:i,activeDays:o,hasPlanExpiry:!!l,planExpiryDate:l?l.toDate():null}),"Admin"===i){let t={expired:!1,activeDays:o};return console.log("\uD83D\uDCC5 Plan expiry result for admin user ".concat(e,":"),t),t}if("Trial"===i){let t=Math.max(0,2-o),a={expired:t<=0,reason:t<=0?"Trial period expired":void 0,daysLeft:t,activeDays:o};return console.log("\uD83D\uDCC5 Plan expiry result for trial user ".concat(e,":"),a),a}if(l){let t=new Date,a=l.toDate(),s=t>a,r=s?0:Math.ceil((a.getTime()-t.getTime())/864e5),i={expired:s,reason:s?"Plan subscription expired":void 0,daysLeft:r,activeDays:o};return console.log("\uD83D\uDCC5 Plan expiry result for user ".concat(e," (using planExpiry field):"),i),i}let n=Math.max(0,30-o),d=o>30,u={expired:d,reason:d?"Plan expired - You have used ".concat(o," days out of 30 allowed days"):void 0,daysLeft:n,activeDays:o};return console.log("\uD83D\uDCC5 Plan expiry result for user ".concat(e,":"),u),u}catch(t){return console.error("Error checking plan expiry for user ".concat(e,":"),t),{expired:!0,reason:"Error checking plan status"}}}static async setPlanExpiryForUser(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30;try{var a;let i=(0,r.H9)(s.db,c.users,e),o=await (0,r.x7)(i);if(!o.exists())return console.error("User ".concat(e," not found")),!1;let l=o.data(),n=l.plan||"Trial";if("Trial"===n||"Admin"===n)return console.log("Skipping plan expiry setup for ".concat(n," user: ").concat(e)),!1;if(l.planExpiry)return console.log("User ".concat(e," already has plan expiry set: ").concat(l.planExpiry.toDate())),!1;let d=(null==(a=l.joinedDate)?void 0:a.toDate())||new Date,u=new Date(d);return u.setDate(u.getDate()+t),await (0,r.mZ)(i,{planExpiry:r.Dc.fromDate(u),planExpirySetDate:r.Dc.now()}),console.log("✅ Set plan expiry for user ".concat(e,": ").concat(u)),!0}catch(t){return console.error("Error setting plan expiry for user ".concat(e,":"),t),!1}}static async forceUpdateActiveDays(e,t,a){try{let o=(0,r.H9)(s.db,c.users,e);await (0,r.mZ)(o,{[i.activeDays]:t,[i.lastActiveDayUpdate]:r.Dc.now()}),console.log("\uD83D\uDD27 Admin ".concat(a," force updated active days for user ").concat(e,": ").concat(t))}catch(t){throw console.error("Error force updating active days for user ".concat(e,":"),t),t}}static async getActiveDaysStatistics(){try{let t=await (0,r.getDocs)((0,r.collection)(s.db,c.users)),a=0,i=0,o=0,l=0,n=0,d=0,u=new Date().toDateString();for(let s of t.docs){var e;let t=s.data(),r=t.plan||"Trial",c=t.activeDays||0,y=null==(e=t.lastActiveDayUpdate)?void 0:e.toDate();a++,n+=c,"Trial"===r?i++:"Admin"===r?l++:o++,y&&y.toDateString()===u&&d++}return{totalUsers:a,trialUsers:i,paidUsers:o,adminUsers:l,averageActiveDays:a>0?Math.round(n/a*100)/100:0,usersUpdatedToday:d}}catch(e){throw console.error("Error getting active days statistics:",e),e}}}let l=o.calculateActiveDays,n=o.updateUserActiveDays,d=o.processAllUsersActiveDays,u=o.getUserActiveDays,y=o.initializeActiveDaysForNewUser;o.getActiveDaysDisplay;let p=o.isUserPlanExpired;o.forceUpdateActiveDays,o.getActiveDaysStatistics},6601:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var s=a(5155),r=a(2115),i=a(6681),c=a(2439);function o(){let{user:e,loading:t}=(0,i.Nu)(),[a,o]=(0,r.useState)(""),[l,n]=(0,r.useState)(null),[d,u]=(0,r.useState)(!1),[y,p]=(0,r.useState)(null),g=async()=>{if(!a.trim())return void alert("Please enter a user ID");u(!0),n(null);try{console.log("\uD83E\uDDEA Testing unified active days service for user: ".concat(a));let e=await c.ActiveDaysService.calculateActiveDays(a),t=await c.ActiveDaysService.getUserActiveDays(a),s=await c.ActiveDaysService.getActiveDaysDisplay(a),r=await c.ActiveDaysService.isUserPlanExpired(a),i={userId:a,timestamp:new Date().toISOString(),calculation:e,currentActiveDays:t,display:s,planStatus:r,success:!0};n(i),console.log("✅ Test completed:",i)}catch(e){console.error("❌ Test failed:",e),n({userId:a,timestamp:new Date().toISOString(),error:e instanceof Error?e.message:"Unknown error",success:!1})}finally{u(!1)}},v=async()=>{if(!a.trim())return void alert("Please enter a user ID");u(!0);try{console.log("\uD83D\uDD04 Testing active days update for user: ".concat(a));let e=await c.ActiveDaysService.updateUserActiveDays(a);alert("✅ Active days updated successfully: ".concat(e)),await g()}catch(e){console.error("❌ Update test failed:",e),alert("❌ Update failed: ".concat(e instanceof Error?e.message:"Unknown error"))}finally{u(!1)}},D=async()=>{u(!0);try{console.log("\uD83D\uDD04 Testing active days processing for all users... (⚠️ TEST ONLY - Production uses Firebase Function)");let e=await c.ActiveDaysService.processAllUsersActiveDays();alert("✅ All users processed:\n- Processed: ".concat(e.processed,"\n- Updated: ").concat(e.updated,"\n- Errors: ").concat(e.errors))}catch(e){console.error("❌ All users test failed:",e),alert("❌ Processing failed: ".concat(e instanceof Error?e.message:"Unknown error"))}finally{u(!1)}},x=async()=>{u(!0);try{console.log("\uD83D\uDCCA Loading active days statistics...");let e=await c.ActiveDaysService.getActiveDaysStatistics();p(e),console.log("✅ Statistics loaded:",e)}catch(e){console.error("❌ Statistics loading failed:",e),alert("❌ Statistics failed: ".concat(e instanceof Error?e.message:"Unknown error"))}finally{u(!1)}};return t?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-white text-xl",children:"Loading..."})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6 mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:"\uD83E\uDDEA Unified Active Days Service Test"}),(0,s.jsx)("p",{className:"text-white/80 mb-4",children:"Test the centralized active days calculation system to ensure it works correctly."}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"User ID to Test"}),(0,s.jsx)("input",{type:"text",value:a,onChange:e=>o(e.target.value),placeholder:"Enter user ID...",className:"w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-500"})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsx)("button",{onClick:g,disabled:d||!a.trim(),className:"w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors",children:d?"Testing...":"Test Single User"})})]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-3 gap-4 mb-6",children:[(0,s.jsx)("button",{onClick:v,disabled:d||!a.trim(),className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors",children:d?"Updating...":"Update User"}),(0,s.jsx)("button",{onClick:D,disabled:d,className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors",children:d?"Processing...":"Process All Users"}),(0,s.jsx)("button",{onClick:x,disabled:d,className:"bg-orange-600 hover:bg-orange-700 disabled:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors",children:d?"Loading...":"Load Statistics"})]})]}),l&&(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:"Test Results"}),(0,s.jsx)("pre",{className:"bg-black/30 p-4 rounded-lg text-white text-sm overflow-auto max-h-96",children:JSON.stringify(l,null,2)})]}),y&&(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:"Active Days Statistics"}),(0,s.jsxs)("div",{className:"grid md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"bg-blue-500/20 rounded-lg p-4 text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-400",children:y.totalUsers}),(0,s.jsx)("div",{className:"text-white/80 text-sm",children:"Total Users"})]}),(0,s.jsxs)("div",{className:"bg-green-500/20 rounded-lg p-4 text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-400",children:y.usersUpdatedToday}),(0,s.jsx)("div",{className:"text-white/80 text-sm",children:"Updated Today"})]}),(0,s.jsxs)("div",{className:"bg-purple-500/20 rounded-lg p-4 text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-400",children:y.averageActiveDays}),(0,s.jsx)("div",{className:"text-white/80 text-sm",children:"Avg Active Days"})]})]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-3 gap-4 mt-4",children:[(0,s.jsxs)("div",{className:"bg-yellow-500/20 rounded-lg p-4 text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:y.trialUsers}),(0,s.jsx)("div",{className:"text-white/80 text-sm",children:"Trial Users"})]}),(0,s.jsxs)("div",{className:"bg-indigo-500/20 rounded-lg p-4 text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-indigo-400",children:y.paidUsers}),(0,s.jsx)("div",{className:"text-white/80 text-sm",children:"Paid Users"})]}),(0,s.jsxs)("div",{className:"bg-red-500/20 rounded-lg p-4 text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-red-400",children:y.adminUsers}),(0,s.jsx)("div",{className:"text-white/80 text-sm",children:"Admin Users"})]})]})]})]})})}},6642:(e,t,a)=>{Promise.resolve().then(a.bind(a,6601))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6681,8441,1684,7358],()=>t(6642)),_N_E=e.O()}]);