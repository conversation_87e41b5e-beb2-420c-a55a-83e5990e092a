(()=>{var e={};e.id=9282,e.ids=[9282],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67132:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(60687),a=s(43210),i=s(85814),o=s.n(i),n=s(87979),l=s(95871),d=s(28879),c=s(77567);function u(){let{user:e,loading:t}=(0,n.Nu)(),[s,i]=(0,a.useState)(null),[u,h]=(0,a.useState)(!0),[m,p]=(0,a.useState)([]),[x,g]=(0,a.useState)(null),[f,b]=(0,a.useState)(0),[w,v]=(0,a.useState)(!1),[y,j]=(0,a.useState)(!1),[N,k]=(0,a.useState)(""),[S,P]=(0,a.useState)(""),[q,C]=(0,a.useState)(!1),[F,E]=(0,a.useState)(!1),[U,T]=(0,a.useState)(!1),O=()=>{if(0===m.length)return;let e=Math.floor(Math.random()*m.length),t=m[e],s=(0,d.jQ)(),r=d.cb.find(e=>e.code===s);g({id:`step_${Date.now()}_${Math.random()}`,englishText:t.english,targetLanguage:s,targetLanguageName:r?.name||"Unknown",targetTranslation:t[s]||"Translation not available",userTypedText:"",selectedLanguage:"",isTypingComplete:!1,isLanguageSelected:!1,isConverted:!1,isSubmitted:!1}),k(""),P(""),E(!1),T(!1),C(!1)},z=e=>{P(e);let t=e===x?.targetLanguageName;if(T(t),t){let e=f+1;b(e),e>=50?v(!0):setTimeout(()=>{O()},1e3)}},A=async()=>{if(s&&!(f<50))try{j(!0),console.log("\uD83D\uDE80 Submitting optimized translation batch...");let e=await (0,l.PY)(50);l.Ou.incrementFunctionUsage(),l.Ou.addWritesOptimized(2),console.log("✅ Optimized batch submitted:",e),i(t=>t?{...t,wallet:e.newWalletBalance,totalTranslations:e.newTotalTranslations,todayTranslations:e.newTodayTranslations,canWork:e.newTodayTranslations<50}:null),b(0),v(!1),g(null),c.A.fire({icon:"success",title:"Batch Completed!",text:`Congratulations! You've earned ₹${e.earningAmount} for completing 50 translations.`,timer:3e3,showConfirmButton:!1});let t=l.Ou.getStats();console.log("\uD83D\uDCB0 Cost Optimization Stats:",t)}catch(e){console.error("❌ Error submitting optimized batch:",e),c.A.fire({icon:"error",title:"Submission Failed",text:e.message||"Failed to submit batch. Please try again."})}finally{j(!1)}};return t||u?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:t?"Loading...":"Loading optimized work data..."}),(0,r.jsx)("p",{className:"text-white/60 text-sm mt-2",children:"Using Firebase Functions for optimal performance"})]})}):s&&(s.planExpired||!s.canWork)?(0,r.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,r.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,r.jsxs)("div",{className:"glass-card p-8 text-center",children:[(0,r.jsx)("i",{className:"fas fa-clock text-yellow-400 text-6xl mb-6"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:s.planExpired?"Plan Expired":"Daily Limit Reached"}),(0,r.jsx)("p",{className:"text-white/80 mb-6",children:s.planExpired?"Your plan has expired. Please upgrade to continue working.":"You have completed your daily translation limit. Come back tomorrow!"}),(0,r.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,r.jsxs)(o(),{href:"/dashboard",className:"btn-primary",children:[(0,r.jsx)("i",{className:"fas fa-home mr-2"}),"Dashboard"]}),s.planExpired&&(0,r.jsxs)(o(),{href:"/plans",className:"btn-secondary",children:[(0,r.jsx)("i",{className:"fas fa-arrow-up mr-2"}),"Upgrade Plan"]})]})]})})}):(0,r.jsxs)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:[(0,r.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(o(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Dashboard"]}),(0,r.jsx)("h1",{className:"text-xl font-bold text-white",children:"Optimized Work Page"}),(0,r.jsxs)("div",{className:"text-white text-sm",children:[(0,r.jsxs)("div",{children:["Wallet: ₹",s?.wallet||0]}),(0,r.jsx)("div",{className:"text-xs text-green-400",children:"Firebase Functions Enabled"})]})]})}),(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-white",children:"Today's Progress"}),(0,r.jsxs)("span",{className:"text-green-400 font-bold",children:[(s?.todayTranslations||0)+f,"/50"]})]}),(0,r.jsx)("div",{className:"w-full bg-white/20 rounded-full h-3 mb-4",children:(0,r.jsx)("div",{className:"bg-gradient-to-r from-green-400 to-blue-500 h-3 rounded-full transition-all duration-300",style:{width:`${Math.min(((s?.todayTranslations||0)+f)/50*100,100)}%`}})}),(0,r.jsxs)("div",{className:"text-white/70 text-sm",children:["Local Progress: ",f," | Earning: ₹",s?.earningPerBatch||0," per batch"]})]}),x&&(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-bold text-white mb-4",children:["Translation Step ",f+1]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"English Text:"}),(0,r.jsx)("div",{className:"p-4 bg-white/10 rounded-lg text-white",children:x.englishText})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Type the text above:"}),(0,r.jsx)("textarea",{value:N,onChange:e=>k(e.target.value),className:"form-input h-24",placeholder:"Start typing the English text...",disabled:F}),!F&&(0,r.jsx)("button",{onClick:()=>{x&&(E(!0),C(!0))},disabled:N.trim().length<10,className:"btn-primary mt-2",children:"Complete Typing"})]}),q&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("label",{className:"block text-white font-medium mb-2",children:["Translation (",x.targetLanguageName,"):"]}),(0,r.jsx)("div",{className:"p-4 bg-green-500/20 rounded-lg text-white",children:x.targetTranslation})]}),F&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Select the language of the translation:"}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:d.cb.slice(0,6).map(e=>(0,r.jsx)("button",{onClick:()=>z(e.name),disabled:""!==S,className:`p-3 rounded-lg font-medium transition-all ${S===e.name?U?"bg-green-500 text-white":"bg-red-500 text-white":"bg-white/10 text-white hover:bg-white/20"}`,children:e.name},e.code))})]})]}),w&&(0,r.jsx)("div",{className:"glass-card p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("i",{className:"fas fa-trophy text-yellow-400 text-4xl mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-bold text-white mb-4",children:"Batch Complete!"}),(0,r.jsxs)("p",{className:"text-white/80 mb-6",children:["You've completed 50 translations. Submit to earn ₹",s?.earningPerBatch||0,"!"]}),(0,r.jsx)("button",{onClick:A,disabled:y,className:"btn-primary text-lg px-8 py-3",children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Submitting..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-check mr-2"}),"Submit Batch (Optimized)"]})})]})}),(0,r.jsx)("div",{className:"glass-card p-4 mt-6",children:(0,r.jsxs)("div",{className:"text-center text-white/60 text-sm",children:[(0,r.jsx)("i",{className:"fas fa-bolt text-yellow-400 mr-2"}),"Powered by Firebase Functions for optimal performance and cost efficiency"]})})]})}},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},86722:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work-optimized\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work-optimized\\page.tsx","default")},89068:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),o=s.n(i),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let d={children:["",{children:["work-optimized",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,86722)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work-optimized\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work-optimized\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/work-optimized/page",pathname:"/work-optimized",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},89617:(e,t,s)=>{Promise.resolve().then(s.bind(s,67132))},90289:(e,t,s)=>{Promise.resolve().then(s.bind(s,86722))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95871:(e,t,s)=>{"use strict";s.d(t,{Ou:()=>m,Ov:()=>y,PY:()=>j,ck:()=>b,e5:()=>w,iM:()=>x,lA:()=>f,rB:()=>p,tv:()=>v,wh:()=>g});var r=s(24791),a=s(33784);async function i(){let{auth:e}=await Promise.resolve().then(s.bind(s,33784)),t=e.currentUser;if(!t)throw Error("User not authenticated");try{await t.getIdToken(!0),console.log("✅ User token refreshed and validated")}catch(e){throw console.error("❌ Error refreshing user token:",e),Error("Authentication token expired. Please refresh the page.")}}let o=(0,r.Qg)(a.Cn,"getUserWorkData"),n=(0,r.Qg)(a.Cn,"submitTranslationBatch"),l=(0,r.Qg)(a.Cn,"getUserDashboardData"),d=((0,r.Qg)(a.Cn,"getAdminDashboardData"),(0,r.Qg)(a.Cn,"getUserTransactions")),c=(0,r.Qg)(a.Cn,"processWithdrawalRequest"),u=(0,r.Qg)(a.Cn,"processDailyActiveDays"),h=((0,r.Qg)(a.Cn,"processDailyCopyPasteReduction"),(0,r.Qg)(a.Cn,"grantCopyPastePermission"),(0,r.Qg)(a.Cn,"updateUserPlan"),(0,r.Qg)(a.Cn,"getPlatformStats")),m={functionsUsed:0,firestoreReadsAvoided:0,firestoreWritesOptimized:0,incrementFunctionUsage(){this.functionsUsed++,console.log(`🚀 Firebase Functions used: ${this.functionsUsed}`)},addReadsAvoided(e){this.firestoreReadsAvoided+=e,console.log(`💰 Firestore reads avoided: ${e} (Total: ${this.firestoreReadsAvoided})`)},addWritesOptimized(e){this.firestoreWritesOptimized+=e,console.log(`⚡ Firestore writes optimized: ${e} (Total: ${this.firestoreWritesOptimized})`)},getStats(){return{functionsUsed:this.functionsUsed,firestoreReadsAvoided:this.firestoreReadsAvoided,firestoreWritesOptimized:this.firestoreWritesOptimized,estimatedCostSavings:(36e-5*this.firestoreReadsAvoided+.00108*this.firestoreWritesOptimized).toFixed(4)}}};async function p(){try{console.log("\uD83D\uDE80 Fetching user work data via Firebase Function..."),await i();let e=(await o()).data;return m.incrementFunctionUsage(),m.addReadsAvoided(4),console.log("✅ User work data fetched successfully:",e),e}catch(e){if(console.error("❌ Error fetching user work data:",e),"unauthenticated"===e.code||e.message?.includes("User must be authenticated"))throw Error("Authentication required. Please refresh the page and try again.");throw Error("Failed to fetch user work data")}}async function x(e=50){try{console.log(`🚀 Submitting translation batch via Firebase Function: ${e} translations`),await i();let t=(await n({batchSize:e})).data;return m.incrementFunctionUsage(),m.addWritesOptimized(3),console.log("✅ Translation batch submitted successfully:",t),t}catch(e){if(console.error("❌ Error submitting translation batch:",e),"unauthenticated"===e.code||e.message?.includes("User must be authenticated"))throw Error("Authentication required. Please refresh the page and try again.");if("permission-denied"===e.code)throw Error("Permission denied. Please check your account status.");throw Error("Failed to submit translation batch")}}async function g(){try{console.log("\uD83D\uDE80 Fetching user dashboard data via Firebase Function..."),await i();let e=(await l()).data;return m.incrementFunctionUsage(),m.addReadsAvoided(3),console.log("✅ User dashboard data fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching user dashboard data:",e),Error("Failed to fetch user dashboard data")}}async function f(e=20,t){try{console.log(`🚀 Fetching user transactions via Firebase Function: limit=${e}`);let s=(await d({limit:e,startAfter:t})).data;return m.incrementFunctionUsage(),m.addReadsAvoided(Math.max(0,100-e)),console.log("✅ User transactions fetched successfully:",s),s}catch(e){throw console.error("❌ Error fetching user transactions:",e),Error("Failed to fetch user transactions")}}async function b(e,t){try{console.log(`🚀 Processing withdrawal request via Firebase Function: ₹${e}`);let s=(await c({amount:e,upiId:t})).data;return m.incrementFunctionUsage(),m.addWritesOptimized(2),console.log("✅ Withdrawal request processed successfully:",s),s}catch(e){throw console.error("❌ Error processing withdrawal request:",e),Error("Failed to process withdrawal request")}}async function w(){try{console.log("\uD83D\uDE80 Processing daily active days via Firebase Function...");let e=(await u()).data;return m.incrementFunctionUsage(),m.addWritesOptimized(e.updated||0),console.log("✅ Daily active days processed successfully:",e),e}catch(e){throw console.error("❌ Error processing daily active days:",e),Error("Failed to process daily active days")}}async function v(){try{console.log("\uD83D\uDE80 Fetching platform stats via Firebase Function...");let e=(await h()).data;return m.incrementFunctionUsage(),m.addReadsAvoided(10),console.log("✅ Platform stats fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching platform stats:",e),Error("Failed to fetch platform stats")}}async function y(){try{console.log("\uD83D\uDE80 Getting optimized user work data..."),await i();let e=await o();return console.log("✅ Optimized user work data retrieved"),e.data}catch(e){throw console.error("❌ Error getting optimized user work data:",e),e}}async function j(e=50){try{console.log(`🚀 Submitting optimized translation batch: ${e} translations`);let t=await n({batchSize:e});return console.log("✅ Optimized translation batch submitted"),t.data}catch(e){throw console.error("❌ Error submitting optimized translation batch:",e),e}}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4573,6803,8879],()=>s(89068));module.exports=r})();