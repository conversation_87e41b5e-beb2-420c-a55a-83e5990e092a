(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1879],{1290:(e,t,s)=>{Promise.resolve().then(s.bind(s,3381))},2649:(e,t,s)=>{"use strict";s.d(t,{Ou:()=>g,Ov:()=>y,PY:()=>j,ck:()=>x,e5:()=>w,iM:()=>b,lA:()=>p,rB:()=>m,tv:()=>v,wh:()=>f});var a=s(2144),r=s(6104);async function i(){let{auth:e}=await Promise.resolve().then(s.bind(s,6104)),t=e.currentUser;if(!t)throw Error("User not authenticated");try{await t.getIdToken(!0),console.log("✅ User token refreshed and validated")}catch(e){throw console.error("❌ Error refreshing user token:",e),Error("Authentication token expired. Please refresh the page.")}}let o=(0,a.Qg)(r.Cn,"getUserWorkData"),n=(0,a.Qg)(r.Cn,"submitTranslationBatch"),c=(0,a.Qg)(r.Cn,"getUserDashboardData"),d=((0,a.Qg)(r.Cn,"getAdminDashboardData"),(0,a.Qg)(r.Cn,"getUserTransactions")),l=(0,a.Qg)(r.Cn,"processWithdrawalRequest"),u=(0,a.Qg)(r.Cn,"processDailyActiveDays"),h=((0,a.Qg)(r.Cn,"processDailyCopyPasteReduction"),(0,a.Qg)(r.Cn,"grantCopyPastePermission"),(0,a.Qg)(r.Cn,"updateUserPlan"),(0,a.Qg)(r.Cn,"getPlatformStats")),g={functionsUsed:0,firestoreReadsAvoided:0,firestoreWritesOptimized:0,incrementFunctionUsage(){this.functionsUsed++,console.log("\uD83D\uDE80 Firebase Functions used: ".concat(this.functionsUsed))},addReadsAvoided(e){this.firestoreReadsAvoided+=e,console.log("\uD83D\uDCB0 Firestore reads avoided: ".concat(e," (Total: ").concat(this.firestoreReadsAvoided,")"))},addWritesOptimized(e){this.firestoreWritesOptimized+=e,console.log("⚡ Firestore writes optimized: ".concat(e," (Total: ").concat(this.firestoreWritesOptimized,")"))},getStats(){return{functionsUsed:this.functionsUsed,firestoreReadsAvoided:this.firestoreReadsAvoided,firestoreWritesOptimized:this.firestoreWritesOptimized,estimatedCostSavings:(36e-5*this.firestoreReadsAvoided+.00108*this.firestoreWritesOptimized).toFixed(4)}}};async function m(){try{console.log("\uD83D\uDE80 Fetching user work data via Firebase Function..."),await i();let e=(await o()).data;return g.incrementFunctionUsage(),g.addReadsAvoided(4),console.log("✅ User work data fetched successfully:",e),e}catch(t){var e;if(console.error("❌ Error fetching user work data:",t),"unauthenticated"===t.code||(null==(e=t.message)?void 0:e.includes("User must be authenticated")))throw Error("Authentication required. Please refresh the page and try again.");throw Error("Failed to fetch user work data")}}async function b(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;try{console.log("\uD83D\uDE80 Submitting translation batch via Firebase Function: ".concat(e," translations")),await i();let t=(await n({batchSize:e})).data;return g.incrementFunctionUsage(),g.addWritesOptimized(3),console.log("✅ Translation batch submitted successfully:",t),t}catch(e){var t;if(console.error("❌ Error submitting translation batch:",e),"unauthenticated"===e.code||(null==(t=e.message)?void 0:t.includes("User must be authenticated")))throw Error("Authentication required. Please refresh the page and try again.");if("permission-denied"===e.code)throw Error("Permission denied. Please check your account status.");throw Error("Failed to submit translation batch")}}async function f(){try{console.log("\uD83D\uDE80 Fetching user dashboard data via Firebase Function..."),await i();let e=(await c()).data;return g.incrementFunctionUsage(),g.addReadsAvoided(3),console.log("✅ User dashboard data fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching user dashboard data:",e),Error("Failed to fetch user dashboard data")}}async function p(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20,t=arguments.length>1?arguments[1]:void 0;try{console.log("\uD83D\uDE80 Fetching user transactions via Firebase Function: limit=".concat(e));let s=(await d({limit:e,startAfter:t})).data;return g.incrementFunctionUsage(),g.addReadsAvoided(Math.max(0,100-e)),console.log("✅ User transactions fetched successfully:",s),s}catch(e){throw console.error("❌ Error fetching user transactions:",e),Error("Failed to fetch user transactions")}}async function x(e,t){try{console.log("\uD83D\uDE80 Processing withdrawal request via Firebase Function: ₹".concat(e));let s=(await l({amount:e,upiId:t})).data;return g.incrementFunctionUsage(),g.addWritesOptimized(2),console.log("✅ Withdrawal request processed successfully:",s),s}catch(e){throw console.error("❌ Error processing withdrawal request:",e),Error("Failed to process withdrawal request")}}async function w(){try{console.log("\uD83D\uDE80 Processing daily active days via Firebase Function...");let e=(await u()).data;return g.incrementFunctionUsage(),g.addWritesOptimized(e.updated||0),console.log("✅ Daily active days processed successfully:",e),e}catch(e){throw console.error("❌ Error processing daily active days:",e),Error("Failed to process daily active days")}}async function v(){try{console.log("\uD83D\uDE80 Fetching platform stats via Firebase Function...");let e=(await h()).data;return g.incrementFunctionUsage(),g.addReadsAvoided(10),console.log("✅ Platform stats fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching platform stats:",e),Error("Failed to fetch platform stats")}}async function y(){try{console.log("\uD83D\uDE80 Getting optimized user work data..."),await i();let e=await o();return console.log("✅ Optimized user work data retrieved"),e.data}catch(e){throw console.error("❌ Error getting optimized user work data:",e),e}}async function j(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;try{console.log("\uD83D\uDE80 Submitting optimized translation batch: ".concat(e," translations"));let t=await n({batchSize:e});return console.log("✅ Optimized translation batch submitted"),t.data}catch(e){throw console.error("❌ Error submitting optimized translation batch:",e),e}}},3381:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(5155),r=s(2115),i=s(6681),o=s(2649);function n(){let{user:e,loading:t}=(0,i.Nu)(),[s,n]=(0,r.useState)([]),[c,d]=(0,r.useState)(!1),l=(e,t,s,a)=>{n(r=>[...r,{functionName:e,success:t,data:s,error:(null==a?void 0:a.message)||(null==a?void 0:a.code)||a,timestamp:new Date().toISOString()}])},u=async(e,t)=>{try{d(!0),console.log("\uD83E\uDDEA Testing ".concat(e,"..."));let s=await t();l(e,!0,s),console.log("✅ ".concat(e," test passed:"),s)}catch(t){l(e,!1,null,t),console.error("❌ ".concat(e," test failed:"),t)}finally{d(!1)}},h=async()=>{n([]),await u("getUserWorkData",()=>(0,o.rB)()),await u("getUserDashboardData",()=>(0,o.wh)()),await u("getUserTransactions",()=>(0,o.lA)(5)),await u("getPlatformStats",()=>(0,o.tv)()),console.log("\uD83D\uDCB0 Final cost optimization stats:",o.Ou.getStats())},g=async()=>{await u("submitTranslationBatch",()=>(0,o.iM)(1))},m=async()=>{await u("processWithdrawalRequest",()=>(0,o.ck)(1,"test@paytm"))};return t?(0,a.jsx)("div",{className:"p-8",children:"Loading..."}):e?(0,a.jsx)("div",{className:"min-h-screen p-8 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white mb-8",children:"\uD83E\uDDEA Firebase Functions Test Page"}),(0,a.jsxs)("div",{className:"bg-blue-600/20 border border-blue-500 rounded-lg p-4 mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-bold text-white mb-2",children:"\uD83D\uDCCB Test Information"}),(0,a.jsxs)("div",{className:"text-white/80 text-sm space-y-1",children:[(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"Safe Tests:"})," Read-only functions that don't modify data"]}),(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"Submit Batch:"})," May fail if daily limit (50) already reached"]}),(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"Withdrawal:"})," May fail if insufficient wallet balance"]}),(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"Errors are expected"})," for some functions based on user state"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:"Test Controls"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("button",{onClick:h,disabled:c,className:"w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:c?"Testing...":"Run All Safe Tests"}),(0,a.jsx)("button",{onClick:g,disabled:c,className:"w-full bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:"Test Submit Batch (⚠️ Will add 1 translation)"}),(0,a.jsx)("button",{onClick:m,disabled:c,className:"w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:"Test Withdrawal (⚠️ Will create ₹1 withdrawal)"}),(0,a.jsx)("button",{onClick:()=>n([]),className:"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg",children:"Clear Results"})]})]}),(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:"Cost Optimization Stats"}),(0,a.jsxs)("div",{className:"text-white space-y-2",children:[(0,a.jsxs)("p",{children:["Functions Used: ",o.Ou.functionsUsed]}),(0,a.jsxs)("p",{children:["Reads Avoided: ",o.Ou.firestoreReadsAvoided]}),(0,a.jsxs)("p",{children:["Writes Optimized: ",o.Ou.firestoreWritesOptimized]}),(0,a.jsxs)("p",{className:"text-green-400 font-bold",children:["Estimated Savings: $",o.Ou.getStats().estimatedCostSavings]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:"Test Results"}),(0,a.jsx)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:0===s.length?(0,a.jsx)("p",{className:"text-white/70",children:'No tests run yet. Click "Run All Safe Tests" to start.'}):s.map((e,t)=>{var s;return(0,a.jsxs)("div",{className:"p-4 rounded-lg ".concat(e.success?"bg-green-600/20 border border-green-500":"bg-red-600/20 border border-red-500"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h3",{className:"font-bold text-white",children:e.functionName}),(0,a.jsx)("span",{className:"px-2 py-1 rounded text-sm ".concat(e.success?"bg-green-600 text-white":"bg-red-600 text-white"),children:e.success?"✅ Success":"❌ Failed"})]}),(0,a.jsxs)("div",{className:"text-sm text-white/80",children:[(0,a.jsxs)("p",{className:"mb-2",children:["Time: ",new Date(e.timestamp).toLocaleTimeString()]}),e.success?(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold mb-1",children:"Result:"}),(0,a.jsx)("pre",{className:"bg-black/20 p-2 rounded text-xs overflow-x-auto",children:JSON.stringify(e.data,null,2)})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold mb-1 text-red-300",children:"Error:"}),(0,a.jsx)("pre",{className:"bg-black/20 p-2 rounded text-xs overflow-x-auto text-red-200",children:(null==(s=e.error)?void 0:s.message)||JSON.stringify(e.error,null,2)})]})]})]},t)})})]})]})}):(0,a.jsx)("div",{className:"p-8",children:"Please log in to test Firebase Functions"})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6681,8441,1684,7358],()=>t(1290)),_N_E=e.O()}]);