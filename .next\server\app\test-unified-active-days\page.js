(()=>{var e={};e.id=5587,e.ids=[1705,5587],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8311:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-unified-active-days\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-unified-active-days\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31918:(e,t,s)=>{Promise.resolve().then(s.bind(s,8311))},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},42386:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),o=s.n(i),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c={children:["",{children:["test-unified-active-days",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8311)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-unified-active-days\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-unified-active-days\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-unified-active-days/page",pathname:"/test-unified-active-days",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},51705:(e,t,s)=>{"use strict";s.d(t,{ActiveDaysService:()=>n,S3:()=>p,i7:()=>l,isUserPlanExpired:()=>x,mH:()=>d,nd:()=>u,updateUserActiveDays:()=>c});var r=s(33784),a=s(75535);let i={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},o={users:"users"};class n{static async calculateActiveDays(e){try{let t,s=await (0,a.x7)((0,a.H9)(r.db,o.users,e));if(!s.exists())return console.error(`User ${e} not found`),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let i=s.data(),l=i.joinedDate?.toDate()||new Date,c=i.lastActiveDayUpdate?.toDate(),d=i.activeDays||0,u=i.plan||"Trial",p=new Date,x=p.toDateString(),y=c?c.toDateString():null;if(console.log(`📅 Calculating active days for user ${e}:`),console.log(`   - Joined: ${l.toDateString()}`),console.log(`   - Current active days: ${d}`),console.log(`   - Last update: ${y||"Never"}`),console.log(`   - Today: ${x}`),console.log(`   - Plan: ${u}`),console.log(`   - Is new day: ${y!==x}`),y===x)return console.log(`✅ Already updated today for user ${e}`),{activeDays:d,shouldUpdate:!1,isNewDay:!1};if("Admin"===u)return console.log(`⏭️ Skipping active days increment for admin user ${e}`),await n.updateLastActiveDayUpdate(e),{activeDays:d,shouldUpdate:!1,isNewDay:!0};if(await n.isUserOnLeaveToday(e))return console.log(`🏖️ User ${e} is on leave today, not incrementing active days`),await n.updateLastActiveDayUpdate(e),{activeDays:d,shouldUpdate:!1,isNewDay:!0};return t="Trial"===u?Math.floor((p.getTime()-l.getTime())/864e5)+1:d+1,console.log(`📈 New active days calculated: ${d} → ${t}`),{activeDays:t,shouldUpdate:t!==d,isNewDay:!0}}catch(t){return console.error(`Error calculating active days for user ${e}:`,t),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(e){try{let t=await n.calculateActiveDays(e);if(t.shouldUpdate){let s=(0,a.H9)(r.db,o.users,e);await (0,a.mZ)(s,{[i.activeDays]:t.activeDays,[i.lastActiveDayUpdate]:a.Dc.now()}),console.log(`✅ Updated active days for user ${e}: ${t.activeDays}`)}else t.isNewDay&&await n.updateLastActiveDayUpdate(e);return t.activeDays}catch(t){throw console.error(`Error updating active days for user ${e}:`,t),t}}static async updateLastActiveDayUpdate(e){try{let t=(0,a.H9)(r.db,o.users,e);await (0,a.mZ)(t,{[i.lastActiveDayUpdate]:a.Dc.now()})}catch(t){console.error(`Error updating last active day timestamp for user ${e}:`,t)}}static async isUserOnLeaveToday(e){try{let{isUserOnLeave:t}=await s.e(7087).then(s.bind(s,87087));return await t(e,new Date)}catch(t){return console.error(`Error checking leave status for user ${e}:`,t),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let e=await (0,a.getDocs)((0,a.collection)(r.db,o.users)),t=0,s=0,i=0;for(let r of e.docs)try{t++;let e=await n.calculateActiveDays(r.id);(e.shouldUpdate||e.isNewDay)&&(await n.updateUserActiveDays(r.id),e.shouldUpdate&&s++)}catch(e){i++,console.error(`Error processing active days for user ${r.id}:`,e)}return console.log(`✅ Daily active days processing complete:`),console.log(`   - Processed: ${t} users`),console.log(`   - Updated: ${s} users`),console.log(`   - Errors: ${i} users`),{processed:t,updated:s,errors:i}}catch(e){throw console.error("Error in daily active days processing:",e),e}}static async getUserActiveDays(e){try{let t=await (0,a.x7)((0,a.H9)(r.db,o.users,e));if(!t.exists())return 0;return t.data().activeDays||0}catch(t){return console.error(`Error getting active days for user ${e}:`,t),0}}static async initializeActiveDaysForNewUser(e){try{let t=(0,a.H9)(r.db,o.users,e);await (0,a.mZ)(t,{[i.activeDays]:1,[i.lastActiveDayUpdate]:a.Dc.now()}),console.log(`✅ Initialized active days for new user ${e}: Day 1`)}catch(t){throw console.error(`Error initializing active days for user ${e}:`,t),t}}static async getActiveDaysDisplay(e){try{let t,s=await (0,a.x7)((0,a.H9)(r.db,o.users,e));if(!s.exists())return{current:0,total:2,displayText:"0/2"};let i=s.data(),n=i.plan||"Trial",l=i.activeDays||0;return t="Trial"===n?2:30,{current:l,total:t,displayText:`${l}/${t}`}}catch(t){return console.error(`Error getting active days display for user ${e}:`,t),{current:0,total:2,displayText:"0/2"}}}static async isUserPlanExpired(e){try{let t=await (0,a.x7)((0,a.H9)(r.db,o.users,e));if(!t.exists())return{expired:!0,reason:"User not found"};let s=t.data(),i=s.plan||"Trial",n=s.activeDays||0,l=s.planExpiry;if(console.log(`📅 Checking plan expiry for user ${e}:`,{plan:i,activeDays:n,hasPlanExpiry:!!l,planExpiryDate:l?l.toDate():null}),"Admin"===i){let t={expired:!1,activeDays:n};return console.log(`📅 Plan expiry result for admin user ${e}:`,t),t}if("Trial"===i){let t=Math.max(0,2-n),s={expired:t<=0,reason:t<=0?"Trial period expired":void 0,daysLeft:t,activeDays:n};return console.log(`📅 Plan expiry result for trial user ${e}:`,s),s}if(l){let t=new Date,s=l.toDate(),r=t>s,a=r?0:Math.ceil((s.getTime()-t.getTime())/864e5),i={expired:r,reason:r?"Plan subscription expired":void 0,daysLeft:a,activeDays:n};return console.log(`📅 Plan expiry result for user ${e} (using planExpiry field):`,i),i}let c=Math.max(0,30-n),d=n>30,u={expired:d,reason:d?`Plan expired - You have used ${n} days out of 30 allowed days`:void 0,daysLeft:c,activeDays:n};return console.log(`📅 Plan expiry result for user ${e}:`,u),u}catch(t){return console.error(`Error checking plan expiry for user ${e}:`,t),{expired:!0,reason:"Error checking plan status"}}}static async setPlanExpiryForUser(e,t=30){try{let s=(0,a.H9)(r.db,o.users,e),i=await (0,a.x7)(s);if(!i.exists())return console.error(`User ${e} not found`),!1;let n=i.data(),l=n.plan||"Trial";if("Trial"===l||"Admin"===l)return console.log(`Skipping plan expiry setup for ${l} user: ${e}`),!1;if(n.planExpiry)return console.log(`User ${e} already has plan expiry set: ${n.planExpiry.toDate()}`),!1;let c=n.joinedDate?.toDate()||new Date,d=new Date(c);return d.setDate(d.getDate()+t),await (0,a.mZ)(s,{planExpiry:a.Dc.fromDate(d),planExpirySetDate:a.Dc.now()}),console.log(`✅ Set plan expiry for user ${e}: ${d}`),!0}catch(t){return console.error(`Error setting plan expiry for user ${e}:`,t),!1}}static async forceUpdateActiveDays(e,t,s){try{let n=(0,a.H9)(r.db,o.users,e);await (0,a.mZ)(n,{[i.activeDays]:t,[i.lastActiveDayUpdate]:a.Dc.now()}),console.log(`🔧 Admin ${s} force updated active days for user ${e}: ${t}`)}catch(t){throw console.error(`Error force updating active days for user ${e}:`,t),t}}static async getActiveDaysStatistics(){try{let e=await (0,a.getDocs)((0,a.collection)(r.db,o.users)),t=0,s=0,i=0,n=0,l=0,c=0,d=new Date().toDateString();for(let r of e.docs){let e=r.data(),a=e.plan||"Trial",o=e.activeDays||0,u=e.lastActiveDayUpdate?.toDate();t++,l+=o,"Trial"===a?s++:"Admin"===a?n++:i++,u&&u.toDateString()===d&&c++}return{totalUsers:t,trialUsers:s,paidUsers:i,adminUsers:n,averageActiveDays:t>0?Math.round(l/t*100)/100:0,usersUpdatedToday:c}}catch(e){throw console.error("Error getting active days statistics:",e),e}}}let l=n.calculateActiveDays,c=n.updateUserActiveDays,d=n.processAllUsersActiveDays,u=n.getUserActiveDays,p=n.initializeActiveDaysForNewUser;n.getActiveDaysDisplay;let x=n.isUserPlanExpired;n.forceUpdateActiveDays,n.getActiveDaysStatistics},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94966:(e,t,s)=>{Promise.resolve().then(s.bind(s,95065))},95065:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(60687),a=s(43210),i=s(87979),o=s(51705);function n(){let{user:e,loading:t}=(0,i.Nu)(),[s,n]=(0,a.useState)(""),[l,c]=(0,a.useState)(null),[d,u]=(0,a.useState)(!1),[p,x]=(0,a.useState)(null),y=async()=>{if(!s.trim())return void alert("Please enter a user ID");u(!0),c(null);try{console.log(`🧪 Testing unified active days service for user: ${s}`);let e=await o.ActiveDaysService.calculateActiveDays(s),t=await o.ActiveDaysService.getUserActiveDays(s),r=await o.ActiveDaysService.getActiveDaysDisplay(s),a=await o.ActiveDaysService.isUserPlanExpired(s),i={userId:s,timestamp:new Date().toISOString(),calculation:e,currentActiveDays:t,display:r,planStatus:a,success:!0};c(i),console.log("✅ Test completed:",i)}catch(e){console.error("❌ Test failed:",e),c({userId:s,timestamp:new Date().toISOString(),error:e instanceof Error?e.message:"Unknown error",success:!1})}finally{u(!1)}},v=async()=>{if(!s.trim())return void alert("Please enter a user ID");u(!0);try{console.log(`🔄 Testing active days update for user: ${s}`);let e=await o.ActiveDaysService.updateUserActiveDays(s);alert(`✅ Active days updated successfully: ${e}`),await y()}catch(e){console.error("❌ Update test failed:",e),alert(`❌ Update failed: ${e instanceof Error?e.message:"Unknown error"}`)}finally{u(!1)}},g=async()=>{u(!0);try{console.log("\uD83D\uDD04 Testing active days processing for all users... (⚠️ TEST ONLY - Production uses Firebase Function)");let e=await o.ActiveDaysService.processAllUsersActiveDays();alert(`✅ All users processed:
- Processed: ${e.processed}
- Updated: ${e.updated}
- Errors: ${e.errors}`)}catch(e){console.error("❌ All users test failed:",e),alert(`❌ Processing failed: ${e instanceof Error?e.message:"Unknown error"}`)}finally{u(!1)}},h=async()=>{u(!0);try{console.log("\uD83D\uDCCA Loading active days statistics...");let e=await o.ActiveDaysService.getActiveDaysStatistics();x(e),console.log("✅ Statistics loaded:",e)}catch(e){console.error("❌ Statistics loading failed:",e),alert(`❌ Statistics failed: ${e instanceof Error?e.message:"Unknown error"}`)}finally{u(!1)}};return t?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center",children:(0,r.jsx)("div",{className:"text-white text-xl",children:"Loading..."})}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6 mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:"\uD83E\uDDEA Unified Active Days Service Test"}),(0,r.jsx)("p",{className:"text-white/80 mb-4",children:"Test the centralized active days calculation system to ensure it works correctly."}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"User ID to Test"}),(0,r.jsx)("input",{type:"text",value:s,onChange:e=>n(e.target.value),placeholder:"Enter user ID...",className:"w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-500"})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsx)("button",{onClick:y,disabled:d||!s.trim(),className:"w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors",children:d?"Testing...":"Test Single User"})})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-3 gap-4 mb-6",children:[(0,r.jsx)("button",{onClick:v,disabled:d||!s.trim(),className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors",children:d?"Updating...":"Update User"}),(0,r.jsx)("button",{onClick:g,disabled:d,className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors",children:d?"Processing...":"Process All Users"}),(0,r.jsx)("button",{onClick:h,disabled:d,className:"bg-orange-600 hover:bg-orange-700 disabled:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors",children:d?"Loading...":"Load Statistics"})]})]}),l&&(0,r.jsxs)("div",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:"Test Results"}),(0,r.jsx)("pre",{className:"bg-black/30 p-4 rounded-lg text-white text-sm overflow-auto max-h-96",children:JSON.stringify(l,null,2)})]}),p&&(0,r.jsxs)("div",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:"Active Days Statistics"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"bg-blue-500/20 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-400",children:p.totalUsers}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Total Users"})]}),(0,r.jsxs)("div",{className:"bg-green-500/20 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-400",children:p.usersUpdatedToday}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Updated Today"})]}),(0,r.jsxs)("div",{className:"bg-purple-500/20 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-purple-400",children:p.averageActiveDays}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Avg Active Days"})]})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-3 gap-4 mt-4",children:[(0,r.jsxs)("div",{className:"bg-yellow-500/20 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:p.trialUsers}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Trial Users"})]}),(0,r.jsxs)("div",{className:"bg-indigo-500/20 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-indigo-400",children:p.paidUsers}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Paid Users"})]}),(0,r.jsxs)("div",{className:"bg-red-500/20 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-400",children:p.adminUsers}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Admin Users"})]})]})]})]})})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4573,6803],()=>s(42386));module.exports=r})();