(()=>{var e={};e.id=4559,e.ids=[4559,7878],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3962:(e,s,r)=>{Promise.resolve().then(r.bind(r,80393))},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27878:(e,s,r)=>{"use strict";r.d(s,{Mk:()=>u,checkCopyPastePermission:()=>c,grantCopyPastePermission:()=>l,i7:()=>p,removeCopyPastePermission:()=>d});var t=r(33784),i=r(75535);let o={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},a={users:"users"};class n{static async checkCopyPastePermission(e){try{let s=await (0,i.x7)((0,i.H9)(t.db,a.users,e));if(!s.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let r=s.data()[o.quickTranslationAdvantageExpiry];if(!r)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let n=r.toDate(),c=new Date,l=n>c,d=l?Math.ceil((n.getTime()-c.getTime())/864e5):0;return{hasPermission:l,daysRemaining:d,expiryDate:n}}catch(s){return console.error(`Error checking copy-paste permission for user ${e}:`,s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,s){try{let r=new Date;r.setDate(r.getDate()+s);let n=(0,i.H9)(t.db,a.users,e);await (0,i.mZ)(n,{[o.quickTranslationAdvantageExpiry]:i.Dc.fromDate(r),[o.lastCopyPasteReduction]:i.Dc.now()}),console.log(`✅ Granted copy-paste permission to user ${e} for ${s} days (expires: ${r.toDateString()})`)}catch(s){throw console.error(`Error granting copy-paste permission to user ${e}:`,s),s}}static async removeCopyPastePermission(e){try{let s=(0,i.H9)(t.db,a.users,e);await (0,i.mZ)(s,{[o.quickTranslationAdvantageExpiry]:null}),console.log(`✅ Removed copy-paste permission from user ${e}`)}catch(s){throw console.error(`Error removing copy-paste permission from user ${e}:`,s),s}}static async reduceCopyPasteDays(e){try{let s=await (0,i.x7)((0,i.H9)(t.db,a.users,e));if(!s.exists())return{reduced:!1,daysRemaining:0,expired:!1};let r=s.data(),n=r[o.quickTranslationAdvantageExpiry],c=r[o.lastCopyPasteReduction];if(!n)return{reduced:!1,daysRemaining:0,expired:!1};let l=new Date().toDateString();if((c?c.toDate().toDateString():null)===l){let e=n.toDate(),s=new Date,r=Math.max(0,Math.ceil((e.getTime()-s.getTime())/864e5));return{reduced:!1,daysRemaining:r,expired:0===r}}let d=n.toDate(),p=new Date(d);p.setDate(p.getDate()-1);let u=(0,i.H9)(t.db,a.users,e);if(p<=new Date)return await (0,i.mZ)(u,{[o.quickTranslationAdvantageExpiry]:null,[o.lastCopyPasteReduction]:i.Dc.now()}),console.log(`📅 Copy-paste permission expired for user ${e}`),{reduced:!0,daysRemaining:0,expired:!0};{await (0,i.mZ)(u,{[o.quickTranslationAdvantageExpiry]:i.Dc.fromDate(p),[o.lastCopyPasteReduction]:i.Dc.now()});let s=Math.ceil((p.getTime()-new Date().getTime())/864e5);return console.log(`📅 Reduced copy-paste days for user ${e}: ${s} days remaining`),{reduced:!0,daysRemaining:s,expired:!1}}}catch(s){return console.error(`Error reducing copy-paste days for user ${e}:`,s),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,i.getDocs)((0,i.collection)(t.db,a.users)),s=0,r=0,o=0,c=0;for(let t of e.docs)try{s++;let e=await n.reduceCopyPasteDays(t.id);e.reduced&&(r++,e.expired&&o++)}catch(e){c++,console.error(`Error processing copy-paste reduction for user ${t.id}:`,e)}return console.log(`✅ Daily copy-paste reduction complete:`),console.log(`   - Processed: ${s} users`),console.log(`   - Reduced: ${r} users`),console.log(`   - Expired: ${o} users`),console.log(`   - Errors: ${c} users`),{processed:s,reduced:r,expired:o,errors:c}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let s=await n.checkCopyPastePermission(e);return{hasPermission:s.hasPermission,daysRemaining:s.daysRemaining,expiryDate:s.expiryDate?s.expiryDate.toDateString():null}}catch(s){return console.error(`Error getting copy-paste status for user ${e}:`,s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let c=n.checkCopyPastePermission,l=n.grantCopyPastePermission,d=n.removeCopyPastePermission,p=n.reduceCopyPasteDays,u=n.processAllUsersCopyPasteReduction;n.getCopyPasteStatus},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},44130:(e,s,r)=>{Promise.resolve().then(r.bind(r,97955))},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67028:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>l});var t=r(65239),i=r(48088),o=r(88170),a=r.n(o),n=r(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);r.d(s,c);let l={children:["",{children:["test-copy-paste-admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-admin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-admin\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-copy-paste-admin/page",pathname:"/test-copy-paste-admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},80393:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-copy-paste-admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-admin\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97955:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(60687),i=r(43210),o=r(87979),a=r(3582),n=r(27878);function c(){let{user:e,loading:s}=(0,o.Nu)(),[r,c]=(0,i.useState)(""),[l,d]=(0,i.useState)(1),[p,u]=(0,i.useState)(null),[m,x]=(0,i.useState)(!1),[y,h]=(0,i.useState)(null),g=async()=>{if(!r.trim())return void alert("Please enter a user ID");x(!0),u(null);try{console.log(`🔧 Granting copy-paste permission to user ${r} for ${l} days`),await (0,a.w1)(r.trim(),l,e?.email||"admin",30);let s=await (0,n.checkCopyPastePermission)(r.trim());h(s),u({success:!0,message:`Copy-paste permission granted for ${l} days`,action:"granted"})}catch(e){u({success:!1,message:`Error: ${e?.message||"Unknown error"}`,action:"grant_failed"})}finally{x(!1)}},P=async()=>{if(!r.trim())return void alert("Please enter a user ID");x(!0),u(null);try{console.log(`🔧 Removing copy-paste permission from user ${r}`),await (0,a.wT)(r.trim(),e?.email||"admin");let s=await (0,n.checkCopyPastePermission)(r.trim());h(s),u({success:!0,message:"Copy-paste permission removed",action:"removed"})}catch(e){u({success:!1,message:`Error: ${e?.message||"Unknown error"}`,action:"remove_failed"})}finally{x(!1)}},b=async()=>{if(!r.trim())return void alert("Please enter a user ID");x(!0);try{let e=await (0,n.checkCopyPastePermission)(r.trim());h(e),u({success:!0,message:"Status checked successfully",action:"status_checked"})}catch(e){u({success:!1,message:`Error: ${e?.message||"Unknown error"}`,action:"status_failed"})}finally{x(!1)}};return s?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"spinner mb-4"}),(0,t.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,t.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,t.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,t.jsxs)("div",{className:"glass-card p-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Copy-Paste Admin Functions"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white mb-2",children:"User ID:"}),(0,t.jsx)("input",{type:"text",value:r,onChange:e=>c(e.target.value),className:"w-full p-3 rounded-lg bg-white/10 text-white border border-white/20",placeholder:"Enter user ID"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white mb-2",children:"Days to Grant:"}),(0,t.jsx)("input",{type:"number",value:l,onChange:e=>d(parseInt(e.target.value)||1),min:"1",max:"365",className:"w-full p-3 rounded-lg bg-white/10 text-white border border-white/20"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)("button",{onClick:g,disabled:m,className:"flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:m?"Processing...":"Grant Permission"}),(0,t.jsx)("button",{onClick:P,disabled:m,className:"flex-1 bg-red-600 hover:bg-red-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:m?"Processing...":"Remove Permission"}),(0,t.jsx)("button",{onClick:b,disabled:m,className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:m?"Checking...":"Check Status"})]}),y&&(0,t.jsxs)("div",{className:"p-4 bg-white/10 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-white font-bold mb-2",children:"Current Permission Status:"}),(0,t.jsxs)("div",{className:"text-white space-y-1",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Has Permission:"})," ",y.hasPermission?"Yes":"No"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Days Remaining:"})," ",y.daysRemaining]}),y.expiryDate&&(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Expires:"})," ",new Date(y.expiryDate).toLocaleDateString()]})]})]}),p&&(0,t.jsxs)("div",{className:`p-4 rounded-lg ${p.success?"bg-green-500/20 border border-green-500/30":"bg-red-500/20 border border-red-500/30"}`,children:[(0,t.jsx)("h3",{className:`font-bold ${p.success?"text-green-400":"text-red-400"}`,children:p.success?"Success!":"Failed"}),(0,t.jsx)("p",{className:"text-white mt-2",children:p.message}),(0,t.jsxs)("p",{className:"text-white/70 text-sm mt-1",children:["Action: ",p.action]})]})]}),(0,t.jsxs)("div",{className:"mt-8 p-4 bg-white/5 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,t.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,t.jsx)("li",{children:"• Enter a valid user ID"}),(0,t.jsx)("li",{children:'• Use "Grant Permission" to enable copy-paste'}),(0,t.jsx)("li",{children:'• Use "Remove Permission" to disable copy-paste'}),(0,t.jsx)("li",{children:'• Use "Check Status" to see current permission state'}),(0,t.jsx)("li",{children:"• Check browser console for detailed logs"})]})]})]})})})}}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4573,6803,3582],()=>r(67028));module.exports=t})();