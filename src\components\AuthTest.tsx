'use client'

import { useState } from 'react'
import { useRequireAuth } from '@/hooks/useAuth'
import { 
  checkAuthenticationStatus,
  getUserDashboardData,
  getUserWorkData 
} from '@/lib/firebaseFunctions'

export default function AuthTest() {
  const { user, loading } = useRequireAuth()
  const [testResults, setTestResults] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const addResult = (testName: string, success: boolean, data: any, error?: any) => {
    setTestResults(prev => [...prev, {
      testName,
      success,
      data,
      error: error?.message || error?.code || error,
      timestamp: new Date().toISOString()
    }])
  }

  const runAuthTest = async () => {
    setIsLoading(true)
    setTestResults([])

    try {
      // Test 1: Check authentication status
      console.log('🧪 Testing authentication status...')
      const authStatus = await checkAuthenticationStatus()
      addResult('Authentication Status Check', true, authStatus)

      // Test 2: Test getUserDashboardData
      console.log('🧪 Testing getUserDashboardData...')
      try {
        const dashboardData = await getUserDashboardData()
        addResult('Get User Dashboard Data', true, dashboardData)
      } catch (error) {
        addResult('Get User Dashboard Data', false, null, error)
      }

      // Test 3: Test getUserWorkData
      console.log('🧪 Testing getUserWorkData...')
      try {
        const workData = await getUserWorkData()
        addResult('Get User Work Data', true, workData)
      } catch (error) {
        addResult('Get User Work Data', false, null, error)
      }

    } catch (error) {
      console.error('❌ Auth test failed:', error)
      addResult('Authentication Test', false, null, error)
    } finally {
      setIsLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading authentication...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <h3 className="text-red-800 font-semibold mb-2">Authentication Required</h3>
        <p className="text-red-600">Please log in to test authentication.</p>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">Authentication Test</h2>
        
        {/* User Info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="text-blue-800 font-semibold mb-2">Current User</h3>
          <p className="text-blue-600">
            <strong>UID:</strong> {user.uid}<br/>
            <strong>Email:</strong> {user.email}<br/>
            <strong>Display Name:</strong> {user.displayName || 'Not set'}
          </p>
        </div>

        {/* Test Button */}
        <div className="mb-6">
          <button
            onClick={runAuthTest}
            disabled={isLoading}
            className="bg-purple-600 hover:bg-purple-700 disabled:bg-purple-300 text-white font-semibold py-2 px-6 rounded-lg transition-colors"
          >
            {isLoading ? (
              <span className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Running Tests...
              </span>
            ) : (
              'Run Authentication Tests'
            )}
          </button>
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800">Test Results</h3>
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`border rounded-lg p-4 ${
                  result.success 
                    ? 'bg-green-50 border-green-200' 
                    : 'bg-red-50 border-red-200'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className={`font-semibold ${
                    result.success ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {result.success ? '✅' : '❌'} {result.testName}
                  </h4>
                  <span className="text-sm text-gray-500">
                    {new Date(result.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                
                {result.error && (
                  <div className="bg-red-100 border border-red-300 rounded p-2 mb-2">
                    <p className="text-red-700 text-sm font-mono">{result.error}</p>
                  </div>
                )}
                
                {result.data && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                      View Data
                    </summary>
                    <pre className="mt-2 bg-gray-100 border rounded p-2 text-xs overflow-auto max-h-40">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
