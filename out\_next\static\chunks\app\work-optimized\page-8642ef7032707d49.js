(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9282],{2644:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var s=a(5155),r=a(2115),i=a(6874),n=a.n(i),o=a(6681),l=a(2649),c=a(3663),d=a(4752),h=a.n(d);function u(){let{user:e,loading:t}=(0,o.Nu)(),[a,i]=(0,r.useState)(null),[d,u]=(0,r.useState)(!0),[m,g]=(0,r.useState)([]),[x,p]=(0,r.useState)(null),[f,b]=(0,r.useState)(0),[w,v]=(0,r.useState)(!1),[y,j]=(0,r.useState)(!1),[N,F]=(0,r.useState)(""),[E,k]=(0,r.useState)(""),[D,T]=(0,r.useState)(!1),[C,U]=(0,r.useState)(!1),[S,z]=(0,r.useState)(!1),O=async()=>{try{u(!0),console.log("\uD83D\uDE80 Loading optimized work data...");let e=await (0,l.Ov)();i(e),l.Ou.incrementFunctionUsage(),l.Ou.addReadsAvoided(4),console.log("✅ Optimized work data loaded:",e),e.canWork&&!e.planExpired&&await P()}catch(e){console.error("❌ Error loading optimized work data:",e),h().fire({icon:"error",title:"Error",text:"Failed to load work data. Please try again."})}finally{u(!1)}},P=async()=>{try{let t=await (0,c.bj)(e.uid);t.translations&&(g(t.translations),W())}catch(e){console.error("Error loading translations:",e)}},W=()=>{if(0===m.length)return;let e=Math.floor(Math.random()*m.length),t=m[e],a=(0,c.jQ)(),s=c.cb.find(e=>e.code===a);p({id:"step_".concat(Date.now(),"_").concat(Math.random()),englishText:t.english,targetLanguage:a,targetLanguageName:(null==s?void 0:s.name)||"Unknown",targetTranslation:t[a]||"Translation not available",userTypedText:"",selectedLanguage:"",isTypingComplete:!1,isLanguageSelected:!1,isConverted:!1,isSubmitted:!1}),F(""),k(""),U(!1),z(!1),T(!1)},A=e=>{k(e);let t=e===(null==x?void 0:x.targetLanguageName);if(z(t),t){let e=f+1;b(e),e>=50?v(!0):setTimeout(()=>{W()},1e3)}},R=async()=>{if(a&&!(f<50))try{j(!0),console.log("\uD83D\uDE80 Submitting optimized translation batch...");let e=await (0,l.PY)(50);l.Ou.incrementFunctionUsage(),l.Ou.addWritesOptimized(2),console.log("✅ Optimized batch submitted:",e),i(t=>t?{...t,wallet:e.newWalletBalance,totalTranslations:e.newTotalTranslations,todayTranslations:e.newTodayTranslations,canWork:e.newTodayTranslations<50}:null),b(0),v(!1),p(null),h().fire({icon:"success",title:"Batch Completed!",text:"Congratulations! You've earned ₹".concat(e.earningAmount," for completing 50 translations."),timer:3e3,showConfirmButton:!1});let t=l.Ou.getStats();console.log("\uD83D\uDCB0 Cost Optimization Stats:",t)}catch(e){console.error("❌ Error submitting optimized batch:",e),h().fire({icon:"error",title:"Submission Failed",text:e.message||"Failed to submit batch. Please try again."})}finally{j(!1)}};return((0,r.useEffect)(()=>{e&&O()},[e]),t||d)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner mb-4"}),(0,s.jsx)("p",{className:"text-white",children:t?"Loading...":"Loading optimized work data..."}),(0,s.jsx)("p",{className:"text-white/60 text-sm mt-2",children:"Using Firebase Functions for optimal performance"})]})}):a&&(a.planExpired||!a.canWork)?(0,s.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,s.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,s.jsxs)("div",{className:"glass-card p-8 text-center",children:[(0,s.jsx)("i",{className:"fas fa-clock text-yellow-400 text-6xl mb-6"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:a.planExpired?"Plan Expired":"Daily Limit Reached"}),(0,s.jsx)("p",{className:"text-white/80 mb-6",children:a.planExpired?"Your plan has expired. Please upgrade to continue working.":"You have completed your daily translation limit. Come back tomorrow!"}),(0,s.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,s.jsxs)(n(),{href:"/dashboard",className:"btn-primary",children:[(0,s.jsx)("i",{className:"fas fa-home mr-2"}),"Dashboard"]}),a.planExpired&&(0,s.jsxs)(n(),{href:"/plans",className:"btn-secondary",children:[(0,s.jsx)("i",{className:"fas fa-arrow-up mr-2"}),"Upgrade Plan"]})]})]})})}):(0,s.jsxs)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:[(0,s.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(n(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Dashboard"]}),(0,s.jsx)("h1",{className:"text-xl font-bold text-white",children:"Optimized Work Page"}),(0,s.jsxs)("div",{className:"text-white text-sm",children:[(0,s.jsxs)("div",{children:["Wallet: ₹",(null==a?void 0:a.wallet)||0]}),(0,s.jsx)("div",{className:"text-xs text-green-400",children:"Firebase Functions Enabled"})]})]})}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-white",children:"Today's Progress"}),(0,s.jsxs)("span",{className:"text-green-400 font-bold",children:[((null==a?void 0:a.todayTranslations)||0)+f,"/50"]})]}),(0,s.jsx)("div",{className:"w-full bg-white/20 rounded-full h-3 mb-4",children:(0,s.jsx)("div",{className:"bg-gradient-to-r from-green-400 to-blue-500 h-3 rounded-full transition-all duration-300",style:{width:"".concat(Math.min((((null==a?void 0:a.todayTranslations)||0)+f)/50*100,100),"%")}})}),(0,s.jsxs)("div",{className:"text-white/70 text-sm",children:["Local Progress: ",f," | Earning: ₹",(null==a?void 0:a.earningPerBatch)||0," per batch"]})]}),x&&(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white mb-4",children:["Translation Step ",f+1]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"English Text:"}),(0,s.jsx)("div",{className:"p-4 bg-white/10 rounded-lg text-white",children:x.englishText})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Type the text above:"}),(0,s.jsx)("textarea",{value:N,onChange:e=>F(e.target.value),className:"form-input h-24",placeholder:"Start typing the English text...",disabled:C}),!C&&(0,s.jsx)("button",{onClick:()=>{x&&(U(!0),T(!0))},disabled:N.trim().length<10,className:"btn-primary mt-2",children:"Complete Typing"})]}),D&&(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("label",{className:"block text-white font-medium mb-2",children:["Translation (",x.targetLanguageName,"):"]}),(0,s.jsx)("div",{className:"p-4 bg-green-500/20 rounded-lg text-white",children:x.targetTranslation})]}),C&&(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Select the language of the translation:"}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:c.cb.slice(0,6).map(e=>(0,s.jsx)("button",{onClick:()=>A(e.name),disabled:""!==E,className:"p-3 rounded-lg font-medium transition-all ".concat(E===e.name?S?"bg-green-500 text-white":"bg-red-500 text-white":"bg-white/10 text-white hover:bg-white/20"),children:e.name},e.code))})]})]}),w&&(0,s.jsx)("div",{className:"glass-card p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("i",{className:"fas fa-trophy text-yellow-400 text-4xl mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-white mb-4",children:"Batch Complete!"}),(0,s.jsxs)("p",{className:"text-white/80 mb-6",children:["You've completed 50 translations. Submit to earn ₹",(null==a?void 0:a.earningPerBatch)||0,"!"]}),(0,s.jsx)("button",{onClick:R,disabled:y,className:"btn-primary text-lg px-8 py-3",children:y?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Submitting..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-check mr-2"}),"Submit Batch (Optimized)"]})})]})}),(0,s.jsx)("div",{className:"glass-card p-4 mt-6",children:(0,s.jsxs)("div",{className:"text-center text-white/60 text-sm",children:[(0,s.jsx)("i",{className:"fas fa-bolt text-yellow-400 mr-2"}),"Powered by Firebase Functions for optimal performance and cost efficiency"]})})]})}},2649:(e,t,a)=>{"use strict";a.d(t,{Ou:()=>m,Ov:()=>y,PY:()=>j,ck:()=>b,e5:()=>w,iM:()=>x,lA:()=>f,rB:()=>g,tv:()=>v,wh:()=>p});var s=a(2144),r=a(6104);async function i(){let{auth:e}=await Promise.resolve().then(a.bind(a,6104)),t=e.currentUser;if(!t)throw Error("User not authenticated");try{await t.getIdToken(!0),console.log("✅ User token refreshed and validated")}catch(e){throw console.error("❌ Error refreshing user token:",e),Error("Authentication token expired. Please refresh the page.")}}let n=(0,s.Qg)(r.Cn,"getUserWorkData"),o=(0,s.Qg)(r.Cn,"submitTranslationBatch"),l=(0,s.Qg)(r.Cn,"getUserDashboardData"),c=((0,s.Qg)(r.Cn,"getAdminDashboardData"),(0,s.Qg)(r.Cn,"getUserTransactions")),d=(0,s.Qg)(r.Cn,"processWithdrawalRequest"),h=(0,s.Qg)(r.Cn,"processDailyActiveDays"),u=((0,s.Qg)(r.Cn,"processDailyCopyPasteReduction"),(0,s.Qg)(r.Cn,"grantCopyPastePermission"),(0,s.Qg)(r.Cn,"updateUserPlan"),(0,s.Qg)(r.Cn,"getPlatformStats")),m={functionsUsed:0,firestoreReadsAvoided:0,firestoreWritesOptimized:0,incrementFunctionUsage(){this.functionsUsed++,console.log("\uD83D\uDE80 Firebase Functions used: ".concat(this.functionsUsed))},addReadsAvoided(e){this.firestoreReadsAvoided+=e,console.log("\uD83D\uDCB0 Firestore reads avoided: ".concat(e," (Total: ").concat(this.firestoreReadsAvoided,")"))},addWritesOptimized(e){this.firestoreWritesOptimized+=e,console.log("⚡ Firestore writes optimized: ".concat(e," (Total: ").concat(this.firestoreWritesOptimized,")"))},getStats(){return{functionsUsed:this.functionsUsed,firestoreReadsAvoided:this.firestoreReadsAvoided,firestoreWritesOptimized:this.firestoreWritesOptimized,estimatedCostSavings:(36e-5*this.firestoreReadsAvoided+.00108*this.firestoreWritesOptimized).toFixed(4)}}};async function g(){try{console.log("\uD83D\uDE80 Fetching user work data via Firebase Function..."),await i();let e=(await n()).data;return m.incrementFunctionUsage(),m.addReadsAvoided(4),console.log("✅ User work data fetched successfully:",e),e}catch(t){var e;if(console.error("❌ Error fetching user work data:",t),"unauthenticated"===t.code||(null==(e=t.message)?void 0:e.includes("User must be authenticated")))throw Error("Authentication required. Please refresh the page and try again.");throw Error("Failed to fetch user work data")}}async function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;try{console.log("\uD83D\uDE80 Submitting translation batch via Firebase Function: ".concat(e," translations")),await i();let t=(await o({batchSize:e})).data;return m.incrementFunctionUsage(),m.addWritesOptimized(3),console.log("✅ Translation batch submitted successfully:",t),t}catch(e){var t;if(console.error("❌ Error submitting translation batch:",e),"unauthenticated"===e.code||(null==(t=e.message)?void 0:t.includes("User must be authenticated")))throw Error("Authentication required. Please refresh the page and try again.");if("permission-denied"===e.code)throw Error("Permission denied. Please check your account status.");throw Error("Failed to submit translation batch")}}async function p(){try{console.log("\uD83D\uDE80 Fetching user dashboard data via Firebase Function..."),await i();let e=(await l()).data;return m.incrementFunctionUsage(),m.addReadsAvoided(3),console.log("✅ User dashboard data fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching user dashboard data:",e),Error("Failed to fetch user dashboard data")}}async function f(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20,t=arguments.length>1?arguments[1]:void 0;try{console.log("\uD83D\uDE80 Fetching user transactions via Firebase Function: limit=".concat(e));let a=(await c({limit:e,startAfter:t})).data;return m.incrementFunctionUsage(),m.addReadsAvoided(Math.max(0,100-e)),console.log("✅ User transactions fetched successfully:",a),a}catch(e){throw console.error("❌ Error fetching user transactions:",e),Error("Failed to fetch user transactions")}}async function b(e,t){try{console.log("\uD83D\uDE80 Processing withdrawal request via Firebase Function: ₹".concat(e));let a=(await d({amount:e,upiId:t})).data;return m.incrementFunctionUsage(),m.addWritesOptimized(2),console.log("✅ Withdrawal request processed successfully:",a),a}catch(e){throw console.error("❌ Error processing withdrawal request:",e),Error("Failed to process withdrawal request")}}async function w(){try{console.log("\uD83D\uDE80 Processing daily active days via Firebase Function...");let e=(await h()).data;return m.incrementFunctionUsage(),m.addWritesOptimized(e.updated||0),console.log("✅ Daily active days processed successfully:",e),e}catch(e){throw console.error("❌ Error processing daily active days:",e),Error("Failed to process daily active days")}}async function v(){try{console.log("\uD83D\uDE80 Fetching platform stats via Firebase Function...");let e=(await u()).data;return m.incrementFunctionUsage(),m.addReadsAvoided(10),console.log("✅ Platform stats fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching platform stats:",e),Error("Failed to fetch platform stats")}}async function y(){try{console.log("\uD83D\uDE80 Getting optimized user work data..."),await i();let e=await n();return console.log("✅ Optimized user work data retrieved"),e.data}catch(e){throw console.error("❌ Error getting optimized user work data:",e),e}}async function j(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;try{console.log("\uD83D\uDE80 Submitting optimized translation batch: ".concat(e," translations"));let t=await o({batchSize:e});return console.log("✅ Optimized translation batch submitted"),t.data}catch(e){throw console.error("❌ Error submitting optimized translation batch:",e),e}}},6367:(e,t,a)=>{Promise.resolve().then(a.bind(a,2644))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6874,6681,3663,8441,1684,7358],()=>t(6367)),_N_E=e.O()}]);