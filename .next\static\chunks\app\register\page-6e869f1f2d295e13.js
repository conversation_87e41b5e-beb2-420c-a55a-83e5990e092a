(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2439,2454],{1469:(e,a,t)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),!function(e,a){for(var t in a)Object.defineProperty(e,t,{enumerable:!0,get:a[t]})}(a,{default:function(){return c},getImageProps:function(){return l}});let r=t(8229),s=t(8883),o=t(3063),i=r._(t(1193));function l(e){let{props:a}=(0,s.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,t]of Object.entries(a))void 0===t&&delete a[e];return{props:a}}let c=o.Image},2439:(e,a,t)=>{"use strict";t.d(a,{ActiveDaysService:()=>l,S3:()=>m,i7:()=>c,isUserPlanExpired:()=>p,mH:()=>d,nd:()=>u,updateUserActiveDays:()=>n});var r=t(6104),s=t(5317);let o={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},i={users:"users"};class l{static async calculateActiveDays(e){try{var a,t;let o,c=await (0,s.x7)((0,s.H9)(r.db,i.users,e));if(!c.exists())return console.error("User ".concat(e," not found")),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let n=c.data(),d=(null==(a=n.joinedDate)?void 0:a.toDate())||new Date,u=null==(t=n.lastActiveDayUpdate)?void 0:t.toDate(),m=n.activeDays||0,p=n.plan||"Trial",y=new Date,f=y.toDateString(),g=u?u.toDateString():null;if(console.log("\uD83D\uDCC5 Calculating active days for user ".concat(e,":")),console.log("   - Joined: ".concat(d.toDateString())),console.log("   - Current active days: ".concat(m)),console.log("   - Last update: ".concat(g||"Never")),console.log("   - Today: ".concat(f)),console.log("   - Plan: ".concat(p)),console.log("   - Is new day: ".concat(g!==f)),g===f)return console.log("✅ Already updated today for user ".concat(e)),{activeDays:m,shouldUpdate:!1,isNewDay:!1};if("Admin"===p)return console.log("⏭️ Skipping active days increment for admin user ".concat(e)),await l.updateLastActiveDayUpdate(e),{activeDays:m,shouldUpdate:!1,isNewDay:!0};if(await l.isUserOnLeaveToday(e))return console.log("\uD83C\uDFD6️ User ".concat(e," is on leave today, not incrementing active days")),await l.updateLastActiveDayUpdate(e),{activeDays:m,shouldUpdate:!1,isNewDay:!0};return o="Trial"===p?Math.floor((y.getTime()-d.getTime())/864e5)+1:m+1,console.log("\uD83D\uDCC8 New active days calculated: ".concat(m," → ").concat(o)),{activeDays:o,shouldUpdate:o!==m,isNewDay:!0}}catch(a){return console.error("Error calculating active days for user ".concat(e,":"),a),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(e){try{let a=await l.calculateActiveDays(e);if(a.shouldUpdate){let t=(0,s.H9)(r.db,i.users,e);await (0,s.mZ)(t,{[o.activeDays]:a.activeDays,[o.lastActiveDayUpdate]:s.Dc.now()}),console.log("✅ Updated active days for user ".concat(e,": ").concat(a.activeDays))}else a.isNewDay&&await l.updateLastActiveDayUpdate(e);return a.activeDays}catch(a){throw console.error("Error updating active days for user ".concat(e,":"),a),a}}static async updateLastActiveDayUpdate(e){try{let a=(0,s.H9)(r.db,i.users,e);await (0,s.mZ)(a,{[o.lastActiveDayUpdate]:s.Dc.now()})}catch(a){console.error("Error updating last active day timestamp for user ".concat(e,":"),a)}}static async isUserOnLeaveToday(e){try{let{isUserOnLeave:a}=await t.e(9567).then(t.bind(t,9567));return await a(e,new Date)}catch(a){return console.error("Error checking leave status for user ".concat(e,":"),a),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let e=await (0,s.getDocs)((0,s.collection)(r.db,i.users)),a=0,t=0,o=0;for(let r of e.docs)try{a++;let e=await l.calculateActiveDays(r.id);(e.shouldUpdate||e.isNewDay)&&(await l.updateUserActiveDays(r.id),e.shouldUpdate&&t++)}catch(e){o++,console.error("Error processing active days for user ".concat(r.id,":"),e)}return console.log("✅ Daily active days processing complete:"),console.log("   - Processed: ".concat(a," users")),console.log("   - Updated: ".concat(t," users")),console.log("   - Errors: ".concat(o," users")),{processed:a,updated:t,errors:o}}catch(e){throw console.error("Error in daily active days processing:",e),e}}static async getUserActiveDays(e){try{let a=await (0,s.x7)((0,s.H9)(r.db,i.users,e));if(!a.exists())return 0;return a.data().activeDays||0}catch(a){return console.error("Error getting active days for user ".concat(e,":"),a),0}}static async initializeActiveDaysForNewUser(e){try{let a=(0,s.H9)(r.db,i.users,e);await (0,s.mZ)(a,{[o.activeDays]:1,[o.lastActiveDayUpdate]:s.Dc.now()}),console.log("✅ Initialized active days for new user ".concat(e,": Day 1"))}catch(a){throw console.error("Error initializing active days for user ".concat(e,":"),a),a}}static async getActiveDaysDisplay(e){try{let a,t=await (0,s.x7)((0,s.H9)(r.db,i.users,e));if(!t.exists())return{current:0,total:2,displayText:"0/2"};let o=t.data(),l=o.plan||"Trial",c=o.activeDays||0;return a="Trial"===l?2:30,{current:c,total:a,displayText:"".concat(c,"/").concat(a)}}catch(a){return console.error("Error getting active days display for user ".concat(e,":"),a),{current:0,total:2,displayText:"0/2"}}}static async isUserPlanExpired(e){try{let a=await (0,s.x7)((0,s.H9)(r.db,i.users,e));if(!a.exists())return{expired:!0,reason:"User not found"};let t=a.data(),o=t.plan||"Trial",l=t.activeDays||0,c=t.planExpiry;if(console.log("\uD83D\uDCC5 Checking plan expiry for user ".concat(e,":"),{plan:o,activeDays:l,hasPlanExpiry:!!c,planExpiryDate:c?c.toDate():null}),"Admin"===o){let a={expired:!1,activeDays:l};return console.log("\uD83D\uDCC5 Plan expiry result for admin user ".concat(e,":"),a),a}if("Trial"===o){let a=Math.max(0,2-l),t={expired:a<=0,reason:a<=0?"Trial period expired":void 0,daysLeft:a,activeDays:l};return console.log("\uD83D\uDCC5 Plan expiry result for trial user ".concat(e,":"),t),t}if(c){let a=new Date,t=c.toDate(),r=a>t,s=r?0:Math.ceil((t.getTime()-a.getTime())/864e5),o={expired:r,reason:r?"Plan subscription expired":void 0,daysLeft:s,activeDays:l};return console.log("\uD83D\uDCC5 Plan expiry result for user ".concat(e," (using planExpiry field):"),o),o}let n=Math.max(0,30-l),d=l>30,u={expired:d,reason:d?"Plan expired - You have used ".concat(l," days out of 30 allowed days"):void 0,daysLeft:n,activeDays:l};return console.log("\uD83D\uDCC5 Plan expiry result for user ".concat(e,":"),u),u}catch(a){return console.error("Error checking plan expiry for user ".concat(e,":"),a),{expired:!0,reason:"Error checking plan status"}}}static async setPlanExpiryForUser(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30;try{var t;let o=(0,s.H9)(r.db,i.users,e),l=await (0,s.x7)(o);if(!l.exists())return console.error("User ".concat(e," not found")),!1;let c=l.data(),n=c.plan||"Trial";if("Trial"===n||"Admin"===n)return console.log("Skipping plan expiry setup for ".concat(n," user: ").concat(e)),!1;if(c.planExpiry)return console.log("User ".concat(e," already has plan expiry set: ").concat(c.planExpiry.toDate())),!1;let d=(null==(t=c.joinedDate)?void 0:t.toDate())||new Date,u=new Date(d);return u.setDate(u.getDate()+a),await (0,s.mZ)(o,{planExpiry:s.Dc.fromDate(u),planExpirySetDate:s.Dc.now()}),console.log("✅ Set plan expiry for user ".concat(e,": ").concat(u)),!0}catch(a){return console.error("Error setting plan expiry for user ".concat(e,":"),a),!1}}static async forceUpdateActiveDays(e,a,t){try{let l=(0,s.H9)(r.db,i.users,e);await (0,s.mZ)(l,{[o.activeDays]:a,[o.lastActiveDayUpdate]:s.Dc.now()}),console.log("\uD83D\uDD27 Admin ".concat(t," force updated active days for user ").concat(e,": ").concat(a))}catch(a){throw console.error("Error force updating active days for user ".concat(e,":"),a),a}}static async getActiveDaysStatistics(){try{let a=await (0,s.getDocs)((0,s.collection)(r.db,i.users)),t=0,o=0,l=0,c=0,n=0,d=0,u=new Date().toDateString();for(let r of a.docs){var e;let a=r.data(),s=a.plan||"Trial",i=a.activeDays||0,m=null==(e=a.lastActiveDayUpdate)?void 0:e.toDate();t++,n+=i,"Trial"===s?o++:"Admin"===s?c++:l++,m&&m.toDateString()===u&&d++}return{totalUsers:t,trialUsers:o,paidUsers:l,adminUsers:c,averageActiveDays:t>0?Math.round(n/t*100)/100:0,usersUpdatedToday:d}}catch(e){throw console.error("Error getting active days statistics:",e),e}}}let c=l.calculateActiveDays,n=l.updateUserActiveDays,d=l.processAllUsersActiveDays,u=l.getUserActiveDays,m=l.initializeActiveDaysForNewUser;l.getActiveDaysDisplay;let p=l.isUserPlanExpired;l.forceUpdateActiveDays,l.getActiveDaysStatistics},3587:(e,a,t)=>{Promise.resolve().then(t.bind(t,6616))},6616:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>g});var r=t(5155),s=t(2115),o=t(6874),i=t.n(o),l=t(6766),c=t(3004),n=t(5317),d=t(6104),u=t(6681),m=t(3592),p=t(2439),y=t(4752),f=t.n(y);function g(){let{user:e,loading:a}=(0,u.hD)(),[t,o]=(0,s.useState)({name:"",email:"",mobile:"",password:"",confirmPassword:"",referralCode:""}),[y,g]=(0,s.useState)(!1),[h,v]=(0,s.useState)(!1),[D,w]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e=new URLSearchParams(window.location.search).get("ref");e&&o(a=>({...a,referralCode:e}))},[]);let x=e=>{let{name:a,value:t}=e.target;o(e=>({...e,[a]:t}))},b=()=>{let{name:e,email:a,mobile:r,password:s,confirmPassword:o}=t;if(!e||!a||!r||!s||!o)throw Error("Please fill in all required fields");if(e.length<2)throw Error("Name must be at least 2 characters long");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a))throw Error("Please enter a valid email address");if(!/^[6-9]\d{9}$/.test(r))throw Error("Please enter a valid 10-digit mobile number");if(s.length<6)throw Error("Password must be at least 6 characters long");if(s!==o)throw Error("Passwords do not match")},N=async e=>{e.preventDefault();try{b(),g(!0),console.log("Creating user with email and password...");let e=(await (0,c.eJ)(d.auth,t.email,t.password)).user;console.log("Firebase Auth user created successfully:",e.uid),console.log("Generating referral code...");let a=Date.now().toString().slice(-4),r=Math.random().toString(36).substring(2,4).toUpperCase(),s="TN".concat(a).concat(r);console.log("Generated referral code:",s);let o={[m.Yr.name]:t.name.trim(),[m.Yr.email]:t.email.toLowerCase(),[m.Yr.mobile]:t.mobile,[m.Yr.referralCode]:s,[m.Yr.referredBy]:t.referralCode||"",[m.Yr.referralBonusCredited]:!1,[m.Yr.plan]:"Trial",[m.Yr.planExpiry]:null,[m.Yr.activeDays]:1,[m.Yr.joinedDate]:n.Dc.now(),[m.Yr.wallet]:0,[m.Yr.totalTranslations]:0,[m.Yr.todayTranslations]:0,[m.Yr.lastTranslationDate]:null,status:"active"};console.log("Creating user document with data:",o),console.log("User UID:",e.uid),console.log("Collection:",m.COLLECTIONS.users),console.log("Document path:","".concat(m.COLLECTIONS.users,"/").concat(e.uid)),console.log("Creating user document in Firestore...");let i=(0,n.H9)(d.db,m.COLLECTIONS.users,e.uid);console.log("Document reference created:",i.path),console.log("About to create document with data:",JSON.stringify(o,null,2));try{console.log("Attempting to create document..."),console.log("User UID:",e.uid),console.log("Document path:",i.path),console.log("Auth user email:",e.email),console.log("Auth user verified:",e.emailVerified),await (0,n.BN)(i,o),console.log("✅ User document created successfully"),await (0,p.S3)(e.uid),console.log("✅ Active days initialized for new user");let a=await (0,n.x7)(i);if(a.exists())console.log("✅ Document verification successful:",a.data()),console.log("✅ Registration completed successfully - both Auth and Firestore created");else throw console.error("❌ Document was not created properly"),Error("User document was not created properly")}catch(e){throw console.error("❌ Firestore setDoc failed:",e),console.error("❌ Firestore error code:",e.code),console.error("❌ Firestore error message:",e.message),console.error("❌ Full error object:",JSON.stringify(e,null,2)),console.error("❌ CRITICAL: Firebase Auth succeeded but Firestore document creation failed"),console.error("❌ User account exists but profile is incomplete"),Error("Failed to create user profile: ".concat(e.message,". Your account was created but profile setup failed. Please contact support."))}console.log("User registered successfully. Referral bonus will be processed when upgraded to paid plan."),f().fire({icon:"success",title:"Registration Successful!",text:"Your account and profile have been created successfully. Welcome to Instra Global!",timer:2e3,showConfirmButton:!1}).then(()=>{console.log("✅ Complete registration successful - redirecting to dashboard..."),window.location.href="/dashboard"})}catch(a){console.error("Registration error:",a),console.error("Error code:",a.code),console.error("Error message:",a.message),console.error("Full error object:",JSON.stringify(a,null,2));let e="An error occurred during registration";if(a.message.includes("fill in all"))e=a.message;else if(a.message.includes("Name must be"))e=a.message;else if(a.message.includes("valid email"))e=a.message;else if(a.message.includes("valid 10-digit"))e=a.message;else if(a.message.includes("Password must be"))e=a.message;else if(a.message.includes("Passwords do not match"))e=a.message;else if(a.message.includes("email address is already registered"))e=a.message;else if(a.message.includes("mobile number is already registered"))e=a.message;else switch(a.code){case"auth/email-already-in-use":e="An account with this email already exists";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/weak-password":e="Password is too weak";break;default:e=a.message||"Registration failed"}f().fire({icon:"error",title:"Registration Failed",text:e})}finally{g(!1)}};return a?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"spinner"})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4 py-8 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,r.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,r.jsx)(l.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:50,height:50,className:"mr-3"}),(0,r.jsx)("span",{className:"text-2xl font-bold text-white",children:"Instra Global"})]}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Create Account"}),(0,r.jsx)("p",{className:"text-white/80",children:"Join Instra Global and start earning today"})]}),(0,r.jsxs)("form",{onSubmit:N,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-white font-medium mb-2",children:"Full Name *"}),(0,r.jsx)("input",{type:"text",id:"name",name:"name",value:t.name,onChange:x,className:"form-input",placeholder:"Enter your full name",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address *"}),(0,r.jsx)("input",{type:"email",id:"email",name:"email",value:t.email,onChange:x,className:"form-input",placeholder:"Enter your email",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"mobile",className:"block text-white font-medium mb-2",children:"Mobile Number *"}),(0,r.jsx)("input",{type:"tel",id:"mobile",name:"mobile",value:t.mobile,onChange:x,className:"form-input",placeholder:"Enter 10-digit mobile number",maxLength:10,required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:"Password *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:h?"text":"password",id:"password",name:"password",value:t.password,onChange:x,className:"form-input pr-12",placeholder:"Enter password (min 6 characters)",required:!0}),(0,r.jsx)("button",{type:"button",onClick:()=>v(!h),className:"password-toggle-btn","aria-label":h?"Hide password":"Show password",children:(0,r.jsx)("i",{className:"fas ".concat(h?"fa-eye-slash":"fa-eye")})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"confirmPassword",className:"block text-white font-medium mb-2",children:"Confirm Password *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:D?"text":"password",id:"confirmPassword",name:"confirmPassword",value:t.confirmPassword,onChange:x,className:"form-input pr-12",placeholder:"Confirm your password",required:!0}),(0,r.jsx)("button",{type:"button",onClick:()=>w(!D),className:"password-toggle-btn","aria-label":D?"Hide confirm password":"Show confirm password",children:(0,r.jsx)("i",{className:"fas ".concat(D?"fa-eye-slash":"fa-eye")})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"referralCode",className:"block text-white font-medium mb-2",children:"Referral Code (Optional)"}),(0,r.jsx)("input",{type:"text",id:"referralCode",name:"referralCode",value:t.referralCode,onChange:x,className:"form-input",placeholder:"Enter referral code if you have one"})]}),(0,r.jsx)("button",{type:"submit",disabled:y,className:"w-full btn-primary flex items-center justify-center mt-6",children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Creating Account..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-user-plus mr-2"}),"Create Account"]})})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsxs)("div",{className:"text-white/60",children:["Already have an account?"," ",(0,r.jsx)(i(),{href:"/login",className:"text-white font-semibold hover:underline",children:"Sign in here"})]})}),(0,r.jsx)("div",{className:"mt-8 text-center",children:(0,r.jsxs)(i(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}},6766:(e,a,t)=>{"use strict";t.d(a,{default:()=>s.a});var r=t(1469),s=t.n(r)}},e=>{var a=a=>e(e.s=a);e.O(0,[2992,7416,8320,8818,6874,3063,6681,3592,8441,1684,7358],()=>a(3587)),_N_E=e.O()}]);