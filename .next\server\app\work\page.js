(()=>{var e={};e.id=4246,e.ids=[1705,4246,7878],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},6473:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var a=s(60687),r=s(43210),i=s(85814),o=s.n(i),n=s(87979),l=s(744),c=s(55986),d=s(3582),u=s(95871),p=s(51705);s(87087);var m=s(28879),h=s(27878),x=s(70038),g=s(98873),y=s(77567);function f({onProgressRestored:e,onRecoveryComplete:t}){let{user:s,loading:i}=(0,n.hD)(),[o,l]=(0,r.useState)(!1),[c,d]=(0,r.useState)(null),[u,p]=(0,r.useState)(!1);return o?(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsx)("div",{className:"bg-white rounded-lg p-6 max-w-md mx-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Checking for Previous Session"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Looking for any unsaved work..."})]})})}):u&&c?(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsx)("div",{className:"bg-white rounded-lg p-6 max-w-md mx-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4",children:(0,a.jsx)("i",{className:"fas fa-history text-yellow-600 text-xl"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Previous Session Found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"We found unsaved work from your previous session. Would you like to restore it?"}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg mb-6",children:(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:[(0,a.jsxs)("p",{className:"mb-2",children:[(0,a.jsx)("strong",{children:"Progress:"})," ",c.completedTranslations,"/50 translations"]}),(0,a.jsxs)("p",{className:"mb-2",children:[(0,a.jsx)("strong",{children:"Last saved:"})," ",new Date(c.lastSaved).toLocaleString()]}),c.userTypedText&&(0,a.jsxs)("p",{className:"mb-2",children:[(0,a.jsx)("strong",{children:"Current text:"}),' "',c.userTypedText.substring(0,50),'..."']})]})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{onClick:()=>{try{if(s?.uid){x.i.clearWorkProgress();let e=`work_progress_backup_${s.uid}`;localStorage.removeItem(e)}y.A.fire({icon:"info",title:"Starting Fresh",text:"Previous session cleared. Starting a new work session.",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error clearing session:",e)}finally{p(!1),t?.()}},className:"flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:"Start Fresh"}),(0,a.jsx)("button",{onClick:()=>{if(c)try{x.i.saveWorkProgress(c),e?.(c),y.A.fire({icon:"success",title:"Session Restored!",html:`
          <div class="text-center">
            <p class="mb-3">Your previous work session has been successfully restored.</p>
            <div class="bg-green-50 p-3 rounded-lg">
              <p class="text-sm text-green-700">
                <strong>Progress:</strong> ${c.completedTranslations}/50 translations completed
              </p>
              <p class="text-sm text-green-700">
                <strong>Last saved:</strong> ${new Date(c.lastSaved).toLocaleString()}
              </p>
            </div>
          </div>
        `,confirmButtonText:"Continue Working",confirmButtonColor:"#10b981"})}catch(e){console.error("Error restoring session:",e),y.A.fire({icon:"error",title:"Restoration Failed",text:"Failed to restore your session. Starting fresh."})}finally{p(!1),t?.()}},className:"flex-1 px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 transition-colors",children:"Restore Session"})]})]})})}):null}var b=s(33784),w=s(75535);function v(){let{user:e,loading:t}=(0,n.Nu)(),{markDataLoaded:s}=function(e){let[t,s]=(0,r.useState)(!1),[a]=(0,r.useState)(Date.now());return{markDataLoaded:()=>{if(!t){s(!0);let t=Date.now()-a;console.log(`⚡ ${e} data loaded in ${t}ms`)}},isDataLoaded:t}}("WorkPage"),{hasBlockingNotifications:i,isChecking:x,markAllAsRead:v}=(0,l.J)(e?.uid||null),{isBlocked:j,leaveStatus:N}=(0,c.l)({userId:e?.uid||null,checkInterval:12e4,enabled:!!e});console.log("WorkPage render:",{user:e?.uid,loading:t,hasBlockingNotifications:i,isChecking:x,isLeaveBlocked:j});let[D,P]=(0,r.useState)(null),[C,T]=(0,r.useState)(0),[k,S]=(0,r.useState)(0),[A,E]=(0,r.useState)(0),[$,U]=(0,r.useState)(!1),[F,R]=(0,r.useState)(!1),[q,L]=(0,r.useState)([]),[B,O]=(0,r.useState)(!0),[M,_]=(0,r.useState)(""),[z,W]=(0,r.useState)(""),[Y,I]=(0,r.useState)([]),[H,Q]=(0,r.useState)(!1),[G,J]=(0,r.useState)(!1),[Z,K]=(0,r.useState)(0),[V,X]=(0,r.useState)(""),[ee,et]=(0,r.useState)(!1),[es,ea]=(0,r.useState)(!1),[er,ei]=(0,r.useState)(0),[eo,en]=(0,r.useState)(!1),[el,ec]=(0,r.useState)(0),[ed,eu]=(0,r.useState)(!1),[ep,em]=(0,r.useState)(""),[eh,ex]=(0,r.useState)(null),[eg,ey]=(0,r.useState)(!1),[ef,eb]=(0,r.useState)(!1),[ew,ev]=(0,r.useState)(!1),[ej,eN]=(0,r.useState)(!1),[eD,eP]=(0,r.useState)(!1),[eC,eT]=(0,r.useState)({earningPerBatch:25,plan:"Trial"}),[ek,eS]=(0,r.useState)(null),[eA,eE]=(0,r.useState)(0),[e$,eU]=(0,r.useState)(0),[eF,eR]=(0,r.useState)(!1),eq=async()=>{try{eR(!0),console.log("\uD83D\uDD04 Refreshing user data from Firestore...");let t=(0,w.H9)(b.db,"users",e.uid),s=await (0,w.x7)(t);if(s.exists()){let e=s.data();console.log("\uD83D\uDCCA Fresh user data loaded:",{activeDays:e?.activeDays,copyPasteDaysRemaining:e?.copyPasteDaysRemaining,plan:e?.plan,planExpiry:e?.planExpiry}),eS(e),eU(e?.activeDays||0),K(e?.copyPasteDaysRemaining||0);let t=(e?.copyPasteDaysRemaining||0)>0;return J(t),console.log("✅ User data refreshed successfully"),e}}catch(e){console.error("❌ Error refreshing user data:",e)}finally{eR(!1)}return null},eL=async()=>{try{console.log("\uD83D\uDCCA Loading translation data for user:",e.uid);let t=await (0,d.getVideoCountData)(e.uid);console.log("\uD83D\uDCCA Translation data loaded:",t),T(t.todayTranslations),S(t.totalTranslations)}catch(e){console.error("Error loading translation data:",e)}},eB=async()=>{try{let t=await (0,d.getUserVideoSettings)(e.uid);eT({earningPerBatch:t.earningPerBatch,plan:t.plan});let s=await (0,h.checkCopyPastePermission)(e.uid);J(s.hasPermission),K(s.daysRemaining),console.log("Copy-paste permission status:",{hasPermission:s.hasPermission,daysRemaining:s.daysRemaining,expiryDate:s.expiryDate})}catch(e){console.error("Error loading translation settings:",e)}},eO=async()=>{try{console.log("\uD83D\uDD04 Manual refresh triggered by user"),y.A.fire({title:"Refreshing Data...",text:"Checking for latest updates",allowOutsideClick:!1,didOpen:()=>{y.A.showLoading()}}),await eq(),await eL(),await eB();let t=await p.ActiveDaysService.isUserPlanExpired(e.uid);if(t.expired)return void y.A.fire({icon:"warning",title:"Plan Expired",text:t.reason,confirmButtonText:"Go to Plans"}).then(()=>{window.location.href="/plans"});if((await (0,d.getVideoCountData)(e.uid)).todayTranslations>=50)return void y.A.fire({icon:"info",title:"Daily Session Completed",text:"You have completed your daily 50 translations!",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});y.A.fire({icon:"success",title:"Data Refreshed!",text:"All information has been updated",timer:2e3,showConfirmButton:!1})}catch(e){console.error("❌ Error in manual refresh:",e),y.A.fire({icon:"error",title:"Refresh Failed",text:"Could not refresh data. Please try again.",confirmButtonText:"OK"})}},eM=()=>{I([]),eu(!1),em("")},e_=(e=q)=>{if(0===e.length)return;let t=Math.floor(Math.random()*e.length),s=e[t],a=(0,m.jQ)(),r=m.cb.find(e=>e.code===a);P({id:`step_${Date.now()}_${Math.random()}`,englishText:s.english,targetLanguage:a,targetLanguageName:r?.name||"Unknown",targetTranslation:s[a]||"Translation not available",userTypedText:"",selectedLanguage:"",isTypingComplete:!1,isLanguageSelected:!1,isConverted:!1,isSubmitted:!1}),_(""),W(""),I([]),Q(!1),X(""),et(!1),ea(!1),en(!1),eu(!1),em("")},ez=(0,r.useCallback)(e=>{if(!D||H)return;let t=e.target.value,s=Date.now();0===er&&(ei(s),ec(0));let a=eQ(t,D.englishText);if(a.length>0&&!G){let s=a[0];if(t.length>s+1){let a=t.substring(0,s+1);e.target.value=a,_(a),I(eQ(a,D.englishText)),ed||(eu(!0),em(`Typing error at position ${s+1}`));return}}if(0===a.length&&!G&&!ed&&eW(t,s)){_(z),e.target.value=z,en(!0);let t=ep.includes("speed")?"Fast Typing Detected!":"Paste Not Allowed!",s=ep.includes("speed")?`${ep}. Please type at a moderate pace and continue.`:`${ep}. Please continue typing manually.`;y.A.fire({icon:"warning",title:t,text:s,timer:2e3,showConfirmButton:!1,toast:!0,position:"top-end"}),setTimeout(()=>{en(!1)},1e3);return}0===a.length&&W(t),_(t),I(a),a.length>0?ed||(eu(!0),em("Typing error detected")):ed&&(eu(!1),em("")),t===D.englishText&&0===a.length&&(Q(!0),y.A.fire({icon:"success",title:"Perfect!",text:"Text typed correctly. Now select the target language.",timer:2e3,showConfirmButton:!1})),ei(s),ec(t.length)},[D,H,eo,ed,G,M,z,ep,er]),eW=(e,t)=>{let s=t-er,a=e.length-M.length;if(0===er||e.length<4||ed||Y.length>0||1>=Math.abs(a))return!1;if(a>5)return console.log("\uD83D\uDEAB Paste detected: More than 5 characters at once"),em("More than 5 characters added at once"),!0;if(a>3&&s<50)return console.log("\uD83D\uDEAB Paste detected: Unrealistic typing speed (>3 chars in <50ms)"),em("Typing speed too fast (possible paste)"),!0;if(a>15)return console.log("\uD83D\uDEAB Paste detected: Large text block"),em("Large text block added instantly"),!0;if(e.length>30&&e===D?.englishText.substring(0,e.length)){let s=t-(0===er?t:er),a=e.length/(s/1e3);if(a>10&&s>1e3)return console.log("\uD83D\uDEAB Paste detected: Perfect match with high speed",{charsPerSecond:a,totalTime:s}),em("Perfect text match with unrealistic speed"),!0}if(a>3){let t=e.substring(M.length);if(t.trim().split(/\s+/).length>=2&&t.includes(" "))return console.log("\uD83D\uDEAB Paste detected: Multiple words added at once"),em("Multiple words added simultaneously"),!0}return!1},eY=(0,r.useCallback)(e=>{!G&&((e.ctrlKey||e.metaKey)&&"v"===e.key&&(e.preventDefault(),y.A.fire({icon:"warning",title:"Paste Not Allowed!",text:"Keyboard paste shortcuts are disabled. Please continue typing manually.",timer:2e3,toast:!0,position:"top-end",showConfirmButton:!1})),e.repeat&&(console.log("\uD83D\uDEAB Long press detected"),"Backspace"!==e.key&&"Delete"!==e.key&&e.preventDefault())),Y.length>0&&setTimeout(()=>{0===eQ(e.target.value,D?.englishText||"").length&&eM()},10)},[G,Y,D]),eI=(0,r.useCallback)(e=>{G||(e.preventDefault(),y.A.fire({icon:"warning",title:"Drag & Drop Not Allowed!",text:"Please continue typing the text manually.",timer:2e3,toast:!0,position:"top-end",showConfirmButton:!1}))},[G]),eH=(0,r.useCallback)(e=>{G||(e.preventDefault(),y.A.fire({icon:"warning",title:"Context Menu Disabled",text:"Right-click menu is disabled to prevent paste operations.",timer:1500}))},[G]),eQ=(e,t)=>{let s=[];for(let a=0;a<e.length;a++)(a>=t.length||e[a]!==t[a])&&s.push(a);return s},eG=e=>{D&&H&&(X(e),e===D.targetLanguage?(et(!0),y.A.fire({icon:"success",title:"Correct Language!",text:"You selected the correct language. Click Convert to see the translation.",timer:2e3,showConfirmButton:!1})):(et(!1),y.A.fire({icon:"error",title:"Wrong Language!",text:`Please select ${D.targetLanguageName} language.`,timer:2e3,showConfirmButton:!1})))},eJ=async()=>{if($&&!F&&!(A<50)){if(j)return void y.A.fire({icon:"warning",title:"Submission Not Available",text:N.reason||"Translation submission is not available due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});try{if(R(!0),console.log("\uD83D\uDE80 Submitting translation batch via Firebase Function..."),!e?.uid)throw Error("User not authenticated");await new Promise(e=>setTimeout(e,100));let t=await (0,u.iM)(50);u.Ou.incrementFunctionUsage(),console.log("\uD83D\uDCB0 Cost optimization stats:",u.Ou.getStats()),console.log("✅ Translation batch submitted successfully:",t),T(t.newTodayTranslations),S(t.newTotalTranslations);let s=new Date().toDateString(),a=`translation_session_${e.uid}_${s}`;localStorage.removeItem(a),E(0),U(!1),eP(!1),eN(!0),y.A.fire({icon:"success",title:"\uD83C\uDF89 Daily Session Completed!",html:`
          <div class="text-center">
            <p class="text-lg font-bold text-green-600 mb-2">₹${t.earningAmount} Earned!</p>
            <p class="mb-2">50 translations completed and submitted</p>
            <p class="text-sm text-gray-600 mb-3">Earnings have been added to your wallet</p>
            <p class="text-sm text-blue-600 font-semibold">
              🎉 Your daily session is complete! Come back tomorrow for your next session.
            </p>
            <p class="text-xs text-gray-500 mt-2">
              💰 New wallet balance: ₹${t.newWallet}
            </p>
          </div>
        `,confirmButtonText:"Go to Dashboard",timer:6e3,showConfirmButton:!0}).then(()=>{window.location.href="/dashboard"})}catch(s){console.error("Error submitting translations:",s);let e="There was an error submitting your translations. Please try again.",t=!1;s.message?.includes("Authentication required")?(e="Authentication expired. Please refresh the page and try again.",t=!0):s.message?.includes("Permission denied")?e="Permission denied. Please check your account status or contact support.":s.message?.includes("User not authenticated")&&(e="Session expired. Please refresh the page and log in again.",t=!0),y.A.fire({icon:"error",title:"Submission Failed",text:e,confirmButtonText:t?"Refresh Page":"Try Again",showCancelButton:t,cancelButtonText:t?"Cancel":void 0}).then(e=>{e.isConfirmed&&t&&window.location.reload()})}finally{R(!1)}}};return t||B||x&&!e?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:t?"Loading...":x?"Checking notifications...":"Loading work data..."}),(0,a.jsx)("p",{className:"text-white/60 text-sm mt-2",children:"\uD83D\uDE80 Using Firebase Functions for optimal performance"})]})}):t?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Loading work page..."})]})}):e?i&&e?(0,a.jsx)(g.A,{userId:e.uid,onAllRead:v}):ej&&e?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full bg-white/10 backdrop-blur-lg rounded-2xl p-8 text-center shadow-2xl",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("i",{className:"fas fa-check-circle text-6xl text-green-400 mb-4"}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:"Daily Work Completed! \uD83C\uDF89"}),(0,a.jsx)("p",{className:"text-white/80 text-lg",children:"You've successfully completed your 50 translations for today."})]}),(0,a.jsxs)("div",{className:"bg-white/5 rounded-xl p-6 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("span",{className:"text-white/70",children:"Today's Earnings:"}),(0,a.jsxs)("span",{className:"text-green-400 font-bold text-xl",children:["₹",eC.earningPerBatch]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("span",{className:"text-white/70",children:"Translations Completed:"}),(0,a.jsx)("span",{className:"text-white font-semibold",children:"50/50"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-white/70",children:"Next Session:"}),(0,a.jsx)("span",{className:"text-yellow-400 font-semibold",children:"Tomorrow"})]})]}),(0,a.jsxs)("div",{className:"text-white/60 text-sm mb-6",children:[(0,a.jsxs)("p",{className:"mb-2",children:[(0,a.jsx)("i",{className:"fas fa-clock mr-2"}),"Your work session is locked until tomorrow"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("i",{className:"fas fa-calendar-alt mr-2"}),"Come back tomorrow for your next 50 translations"]})]}),(0,a.jsxs)("button",{onClick:()=>window.location.href="/dashboard",className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105",children:[(0,a.jsx)("i",{className:"fas fa-home mr-2"}),"Go to Dashboard"]})]})}):(0,a.jsxs)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:[ef&&(0,a.jsx)(f,{onProgressRestored:e=>{P(e.currentStep),_(e.userTypedText),W(e.userTypedText),X(e.selectedLanguage),Q(e.isTypingComplete),E(e.completedTranslations),ex(new Date(e.lastSaved))},onRecoveryComplete:()=>{ev(!0),eb(!1)}}),(0,a.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)(o(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"Translate Text & Earn"}),(0,a.jsx)("button",{onClick:eO,disabled:eF,className:`glass-button px-3 py-2 text-white hover:bg-white/20 transition-colors ${eF?"opacity-50 cursor-not-allowed":""}`,title:"Refresh data to check for updates",children:(0,a.jsx)("i",{className:`fas fa-sync-alt ${eF?"animate-spin":""}`})})]}),(0,a.jsxs)("div",{className:"text-white text-right",children:[(0,a.jsxs)("p",{className:"text-sm",children:["Plan: ",eC.plan]}),(0,a.jsxs)("p",{className:"text-sm",children:["₹",eC.earningPerBatch,"/batch (50 translations)"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-5 gap-2 text-center",children:[(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,a.jsx)("p",{className:"text-lg font-bold text-blue-400",children:C}),(0,a.jsx)("p",{className:"text-white/80 text-xs",children:"Today TL"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,a.jsx)("p",{className:"text-lg font-bold text-green-400",children:k}),(0,a.jsx)("p",{className:"text-white/80 text-xs",children:"Total TL"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,a.jsx)("p",{className:"text-lg font-bold text-purple-400",children:Math.max(0,50-A)}),(0,a.jsx)("p",{className:"text-white/80 text-xs",children:"TL Left"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,a.jsxs)("p",{className:"text-lg font-bold text-orange-400",children:[e$,"/","Trial"===eC.plan?"2":"30"]}),(0,a.jsx)("p",{className:"text-white/80 text-xs",children:"Active Days"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,a.jsx)("p",{className:`text-lg font-bold ${G?"text-green-400":"text-gray-400"}`,children:G?Z:"0"}),(0,a.jsx)("p",{className:"text-white/80 text-xs",children:"Copy Days"})]})]}),(0,a.jsx)("div",{className:"flex items-center justify-center mt-3",children:eg?(0,a.jsxs)("span",{className:"text-yellow-400 text-sm",children:[(0,a.jsx)("i",{className:"fas fa-spinner fa-spin mr-1"}),"Saving progress..."]}):eh?(0,a.jsxs)("span",{className:"text-green-400 text-sm",children:[(0,a.jsx)("i",{className:"fas fa-check mr-1"}),"Last saved: ",eh.toLocaleTimeString()]}):(0,a.jsxs)("span",{className:"text-blue-400 text-sm",children:[(0,a.jsx)("i",{className:"fas fa-shield-alt mr-1"}),"Auto-save protection enabled"]})})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,a.jsx)("i",{className:"fas fa-language mr-2"}),"Translate Text & Earn"]}),(0,a.jsxs)("button",{onClick:()=>{_(""),W(""),I([]),Q(!1),X(""),et(!1),ea(!1),en(!1),eu(!1),em(""),ei(0),ec(0),y.A.fire({icon:"info",title:"Reset Complete!",text:"You can now start typing again. Please type carefully.",timer:2e3,showConfirmButton:!1})},className:"glass-button px-3 py-1 text-white text-sm",title:"Clear typed text and reset",children:[(0,a.jsx)("i",{className:"fas fa-eraser mr-1"}),"Reset"]})]}),!D&&!B&&(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-8",children:[(0,a.jsx)("i",{className:"fas fa-exclamation-triangle text-4xl text-yellow-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-4",children:"No Translation Available"}),(0,a.jsx)("p",{className:"text-white/80 mb-6",children:"Unable to load translation data. This could be due to:"}),(0,a.jsxs)("ul",{className:"text-white/70 text-left max-w-md mx-auto mb-6",children:[(0,a.jsx)("li",{className:"mb-2",children:"• Translation data file not found"}),(0,a.jsx)("li",{className:"mb-2",children:"• Network connectivity issues"}),(0,a.jsx)("li",{className:"mb-2",children:"• Server maintenance"})]}),(0,a.jsxs)("button",{onClick:()=>window.location.reload(),className:"btn-primary px-6 py-3 rounded-lg font-semibold",children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Retry Loading"]})]})}),D&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold",children:[(0,a.jsx)("i",{className:"fas fa-keyboard mr-2"}),"Step 1: Type the English text below"]}),G?(0,a.jsxs)("button",{onClick:()=>{navigator.clipboard.writeText(D.englishText),y.A.fire({icon:"success",title:"Copied!",text:"English text copied to clipboard",timer:1500,showConfirmButton:!1})},className:"group relative bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 animate-pulse",title:"Copy English text",children:[(0,a.jsx)("i",{className:"fas fa-copy mr-2"}),(0,a.jsx)("span",{className:"text-sm",children:"Copy"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-white/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"})]}):(0,a.jsxs)("div",{className:"text-white/60 text-xs bg-white/10 px-3 py-2 rounded-lg",children:[(0,a.jsx)("i",{className:"fas fa-lock mr-1"}),"Copy disabled - Type manually"]})]}),(0,a.jsxs)("div",{className:"bg-white/5 p-3 rounded border-l-4 border-blue-400 mb-3",children:[(0,a.jsx)("div",{className:"max-h-24 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent",children:(0,a.jsx)("p",{className:"text-white text-base md:text-lg font-mono leading-relaxed",children:Y.length>0&&!G?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-green-400",children:D.englishText.substring(0,Y[0])}),(0,a.jsx)("span",{className:"bg-red-500 text-white px-1 rounded animate-pulse",children:D.englishText[Y[0]]}),(0,a.jsx)("span",{className:"text-white/60",children:D.englishText.substring(Y[0]+1)})]}):D.englishText})}),(0,a.jsxs)("div",{className:"text-xs text-white/60 mt-2",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-1"}),Y.length>0&&!G?`Error at highlighted character (position ${Y[0]+1})`:"Scroll to see full text if needed"]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("textarea",{value:M,onChange:ez,onKeyDown:eY,onDrop:eI,onContextMenu:eH,disabled:H,placeholder:Y.length>0?"Fix the highlighted error and continue typing...":G?"Type or paste the English text here...":"Type the English text here (copy-paste not allowed). Fast typists: please type at moderate speed to avoid triggering anti-paste protection.",className:`w-full h-24 md:h-32 p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200 placeholder-white/60 resize-none font-mono text-sm md:text-base leading-relaxed ${Y.length>0?"border-red-500":""} ${H?"border-green-500 bg-green-500/10":""}`,onPaste:e=>{G||(e.preventDefault(),y.A.fire({icon:"warning",title:"Paste Not Allowed!",text:"Please continue typing the text manually.",timer:2e3,toast:!0,position:"top-end",showConfirmButton:!1}))},onDragOver:e=>{G||e.preventDefault()},spellCheck:!1,autoComplete:"off",autoCorrect:"off",autoCapitalize:"off"}),G&&(0,a.jsxs)("button",{onClick:async()=>{try{let e=await navigator.clipboard.readText();_(e),ez({target:{value:e}}),y.A.fire({icon:"success",title:"Pasted!",text:"Text pasted from clipboard",timer:1500,showConfirmButton:!1})}catch(e){y.A.fire({icon:"error",title:"Paste Failed",text:"Could not access clipboard",timer:1500,showConfirmButton:!1})}},className:"group absolute top-3 right-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold py-2 px-3 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-200 animate-bounce disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:animate-none",title:"Paste from clipboard",disabled:H,children:[(0,a.jsx)("i",{className:"fas fa-paste mr-1"}),(0,a.jsx)("span",{className:"text-xs",children:"Paste"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-white/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"})]})]}),Y.length>0&&(0,a.jsxs)("div",{className:"mt-2 text-red-400 text-sm bg-red-500/10 border border-red-500/30 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)("i",{className:"fas fa-exclamation-triangle mr-2"}),(0,a.jsx)("strong",{children:"Typing Error Detected"})]}),(0,a.jsxs)("div",{className:"text-red-300 text-xs mb-2",children:["Error at position ",Y[0]+1,': Expected "',D.englishText[Y[0]],'" but got "',M[Y[0]]||"nothing",'"']}),(0,a.jsxs)("div",{className:"text-red-200 text-xs",children:[(0,a.jsx)("i",{className:"fas fa-edit mr-1"}),"Edit the text box to correct the mistake, then continue typing."]})]}),H&&(0,a.jsxs)("div",{className:"mt-2 text-green-400 text-sm",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-1"}),"Perfect! Text typed correctly."]}),eo&&!G&&(0,a.jsxs)("div",{className:"mt-2 p-3 bg-yellow-500/20 border border-yellow-500/30 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-yellow-400 text-sm",children:[(0,a.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),(0,a.jsx)("strong",{children:"Paste Attempt Detected"})]}),(0,a.jsx)("div",{className:"text-yellow-300 text-xs mt-1",children:ep.includes("speed")?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-1"}),"Fast typing detected. Please type at a moderate pace. You can continue typing normally."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-clipboard mr-1"}),"Paste operation blocked. Please continue typing manually from where you left off."]})}),(0,a.jsxs)("div",{className:"text-yellow-200 text-xs mt-2",children:[(0,a.jsx)("i",{className:"fas fa-arrow-right mr-1"}),"This message will disappear automatically. Continue typing normally."]})]}),Y.length>0&&!G&&(0,a.jsx)("div",{className:"mt-3 text-center",children:(0,a.jsxs)("div",{className:"bg-blue-500/10 border border-blue-500/30 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"text-blue-400 text-sm font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-lightbulb mr-2"}),"How to Fix the Error:"]}),(0,a.jsxs)("div",{className:"text-blue-300 text-xs space-y-1",children:[(0,a.jsx)("div",{children:"1. Click in the text box and edit the incorrect character"}),(0,a.jsxs)("div",{children:['2. Change it to the correct character: "',D.englishText[Y[0]],'"']}),(0,a.jsx)("div",{children:"3. Continue typing the rest of the text"})]})]})}),M&&!H&&0===Y.length&&(0,a.jsx)("div",{className:"mt-3 text-center",children:(0,a.jsxs)("button",{onClick:()=>{ez({target:{value:M}})},className:"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold py-2 px-6 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200",title:"Check if typed text is correct",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-2"}),"Check Text"]})})]}),H&&(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-globe mr-2"}),"Step 2: Select the target language - ",D.targetLanguageName]}),(0,a.jsxs)("select",{value:V,onChange:e=>eG(e.target.value),className:"w-full p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200",children:[(0,a.jsx)("option",{value:"",className:"bg-gray-800 text-white",children:"Select target language..."}),m.cb.map(e=>(0,a.jsxs)("option",{value:e.code,className:"bg-gray-800 text-white",children:[e.flag," ",e.name]},e.code))]}),V&&!ee&&(0,a.jsxs)("div",{className:"mt-2 text-red-400 text-sm",children:[(0,a.jsx)("i",{className:"fas fa-times-circle mr-1"}),"Wrong language! Please select ",D.targetLanguageName,"."]}),ee&&(0,a.jsxs)("div",{className:"mt-2 text-green-400 text-sm",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-1"}),"Correct language selected!"]})]}),ee&&(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("button",{onClick:()=>{D&&ee&&(ea(!0),P(e=>e?{...e,isConverted:!0}:null))},disabled:es,className:`px-8 py-3 rounded-lg font-semibold transition-all duration-300 ${es?"btn-disabled cursor-not-allowed opacity-50":"btn-primary hover:scale-105"}`,children:[(0,a.jsx)("i",{className:"fas fa-exchange-alt mr-2"}),"Convert to ",D.targetLanguageName]})}),es&&(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-language mr-2"}),D.targetLanguageName," Translation:"]}),(0,a.jsx)("div",{className:"bg-white/5 p-3 rounded border-l-4 border-green-400",children:(0,a.jsx)("p",{className:"text-white text-lg",children:D.targetTranslation})}),(0,a.jsx)("div",{className:"text-center mt-4",children:(0,a.jsxs)("button",{onClick:()=>{if(!D||!es)return;if(A>=50)return void y.A.fire({icon:"warning",title:"Daily Limit Reached!",text:"You have already completed 50 translations for today. Please submit your batch to earn rewards.",timer:3e3,showConfirmButton:!1});P(e=>e?{...e,isSubmitted:!0}:null);let t=A+1;E(t);let s=new Date().toDateString(),a=`translation_session_${e.uid}_${s}`;localStorage.setItem(a,t.toString()),t<50?y.A.fire({icon:"success",title:"Translation Submitted!",text:`Progress: ${t}/50 translations completed.`,timer:2e3,showConfirmButton:!1}).then(()=>{e_()}):(U(!0),eP(!0),y.A.fire({icon:"success",title:"\uD83C\uDF89 All Translations Completed!",text:'You have completed all 50 translations! Click "Submit & Earn" to get your rewards.',timer:3e3,showConfirmButton:!1}),P(null),ea(!1),Q(!1),_(""),X(""),et(!1))},disabled:A>=50,className:`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${A>=50?"btn-disabled cursor-not-allowed opacity-50":"btn-success hover:scale-105"}`,children:[(0,a.jsx)("i",{className:"fas fa-check mr-2"}),A>=50?"Daily Limit Reached":"Submit Translation"]})})]}),eD&&!ej&&(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl p-6 mb-4 shadow-lg",children:[(0,a.jsx)("h3",{className:"text-white font-bold text-xl mb-2",children:"\uD83C\uDF89 Congratulations! You've completed 50 translations!"}),(0,a.jsx)("p",{className:"text-white/90 mb-4",children:"Click the button below to submit your daily batch and receive your earnings."}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"⚠️ After submission, you won't be able to work until tomorrow."})]}),(0,a.jsx)("button",{onClick:eJ,disabled:F,className:"bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold py-4 px-12 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none animate-pulse",children:F?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),"Submitting Final Batch..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-trophy mr-2"}),"Submit Final Batch & Earn ₹",eC.earningPerBatch]})})]}),$&&!eD&&(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("button",{onClick:eJ,disabled:F,className:"btn-success px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 hover:scale-105",children:[(0,a.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Submit All 50 Translations & Earn ₹",eC.earningPerBatch]})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:"text-white/80",children:["Progress: ",A,"/50 translations completed"]}),(0,a.jsx)("div",{className:"w-full bg-white/20 rounded-full h-2 mt-2",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-purple-400 h-2 rounded-full transition-all duration-300",style:{width:`${A/50*100}%`}})})]})]})]})]}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Authenticating..."})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19645:(e,t,s)=>{Promise.resolve().then(s.bind(s,30766))},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27878:(e,t,s)=>{"use strict";s.d(t,{Mk:()=>p,checkCopyPastePermission:()=>l,grantCopyPastePermission:()=>c,i7:()=>u,removeCopyPastePermission:()=>d});var a=s(33784),r=s(75535);let i={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},o={users:"users"};class n{static async checkCopyPastePermission(e){try{let t=await (0,r.x7)((0,r.H9)(a.db,o.users,e));if(!t.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let s=t.data()[i.quickTranslationAdvantageExpiry];if(!s)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let n=s.toDate(),l=new Date,c=n>l,d=c?Math.ceil((n.getTime()-l.getTime())/864e5):0;return{hasPermission:c,daysRemaining:d,expiryDate:n}}catch(t){return console.error(`Error checking copy-paste permission for user ${e}:`,t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,t){try{let s=new Date;s.setDate(s.getDate()+t);let n=(0,r.H9)(a.db,o.users,e);await (0,r.mZ)(n,{[i.quickTranslationAdvantageExpiry]:r.Dc.fromDate(s),[i.lastCopyPasteReduction]:r.Dc.now()}),console.log(`✅ Granted copy-paste permission to user ${e} for ${t} days (expires: ${s.toDateString()})`)}catch(t){throw console.error(`Error granting copy-paste permission to user ${e}:`,t),t}}static async removeCopyPastePermission(e){try{let t=(0,r.H9)(a.db,o.users,e);await (0,r.mZ)(t,{[i.quickTranslationAdvantageExpiry]:null}),console.log(`✅ Removed copy-paste permission from user ${e}`)}catch(t){throw console.error(`Error removing copy-paste permission from user ${e}:`,t),t}}static async reduceCopyPasteDays(e){try{let t=await (0,r.x7)((0,r.H9)(a.db,o.users,e));if(!t.exists())return{reduced:!1,daysRemaining:0,expired:!1};let s=t.data(),n=s[i.quickTranslationAdvantageExpiry],l=s[i.lastCopyPasteReduction];if(!n)return{reduced:!1,daysRemaining:0,expired:!1};let c=new Date().toDateString();if((l?l.toDate().toDateString():null)===c){let e=n.toDate(),t=new Date,s=Math.max(0,Math.ceil((e.getTime()-t.getTime())/864e5));return{reduced:!1,daysRemaining:s,expired:0===s}}let d=n.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let p=(0,r.H9)(a.db,o.users,e);if(u<=new Date)return await (0,r.mZ)(p,{[i.quickTranslationAdvantageExpiry]:null,[i.lastCopyPasteReduction]:r.Dc.now()}),console.log(`📅 Copy-paste permission expired for user ${e}`),{reduced:!0,daysRemaining:0,expired:!0};{await (0,r.mZ)(p,{[i.quickTranslationAdvantageExpiry]:r.Dc.fromDate(u),[i.lastCopyPasteReduction]:r.Dc.now()});let t=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log(`📅 Reduced copy-paste days for user ${e}: ${t} days remaining`),{reduced:!0,daysRemaining:t,expired:!1}}}catch(t){return console.error(`Error reducing copy-paste days for user ${e}:`,t),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,r.getDocs)((0,r.collection)(a.db,o.users)),t=0,s=0,i=0,l=0;for(let a of e.docs)try{t++;let e=await n.reduceCopyPasteDays(a.id);e.reduced&&(s++,e.expired&&i++)}catch(e){l++,console.error(`Error processing copy-paste reduction for user ${a.id}:`,e)}return console.log(`✅ Daily copy-paste reduction complete:`),console.log(`   - Processed: ${t} users`),console.log(`   - Reduced: ${s} users`),console.log(`   - Expired: ${i} users`),console.log(`   - Errors: ${l} users`),{processed:t,reduced:s,expired:i,errors:l}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let t=await n.checkCopyPastePermission(e);return{hasPermission:t.hasPermission,daysRemaining:t.daysRemaining,expiryDate:t.expiryDate?t.expiryDate.toDateString():null}}catch(t){return console.error(`Error getting copy-paste status for user ${e}:`,t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let l=n.checkCopyPastePermission,c=n.grantCopyPastePermission,d=n.removeCopyPastePermission,u=n.reduceCopyPasteDays,p=n.processAllUsersCopyPasteReduction;n.getCopyPasteStatus},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30766:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx","default")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},48060:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=s(65239),r=s(48088),i=s(88170),o=s.n(i),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c={children:["",{children:["work",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,30766)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/work/page",pathname:"/work",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},51705:(e,t,s)=>{"use strict";s.d(t,{ActiveDaysService:()=>n,S3:()=>p,i7:()=>l,isUserPlanExpired:()=>m,mH:()=>d,nd:()=>u,updateUserActiveDays:()=>c});var a=s(33784),r=s(75535);let i={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},o={users:"users"};class n{static async calculateActiveDays(e){try{let t,s=await (0,r.x7)((0,r.H9)(a.db,o.users,e));if(!s.exists())return console.error(`User ${e} not found`),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let i=s.data(),l=i.joinedDate?.toDate()||new Date,c=i.lastActiveDayUpdate?.toDate(),d=i.activeDays||0,u=i.plan||"Trial",p=new Date,m=p.toDateString(),h=c?c.toDateString():null;if(console.log(`📅 Calculating active days for user ${e}:`),console.log(`   - Joined: ${l.toDateString()}`),console.log(`   - Current active days: ${d}`),console.log(`   - Last update: ${h||"Never"}`),console.log(`   - Today: ${m}`),console.log(`   - Plan: ${u}`),console.log(`   - Is new day: ${h!==m}`),h===m)return console.log(`✅ Already updated today for user ${e}`),{activeDays:d,shouldUpdate:!1,isNewDay:!1};if("Admin"===u)return console.log(`⏭️ Skipping active days increment for admin user ${e}`),await n.updateLastActiveDayUpdate(e),{activeDays:d,shouldUpdate:!1,isNewDay:!0};if(await n.isUserOnLeaveToday(e))return console.log(`🏖️ User ${e} is on leave today, not incrementing active days`),await n.updateLastActiveDayUpdate(e),{activeDays:d,shouldUpdate:!1,isNewDay:!0};return t="Trial"===u?Math.floor((p.getTime()-l.getTime())/864e5)+1:d+1,console.log(`📈 New active days calculated: ${d} → ${t}`),{activeDays:t,shouldUpdate:t!==d,isNewDay:!0}}catch(t){return console.error(`Error calculating active days for user ${e}:`,t),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(e){try{let t=await n.calculateActiveDays(e);if(t.shouldUpdate){let s=(0,r.H9)(a.db,o.users,e);await (0,r.mZ)(s,{[i.activeDays]:t.activeDays,[i.lastActiveDayUpdate]:r.Dc.now()}),console.log(`✅ Updated active days for user ${e}: ${t.activeDays}`)}else t.isNewDay&&await n.updateLastActiveDayUpdate(e);return t.activeDays}catch(t){throw console.error(`Error updating active days for user ${e}:`,t),t}}static async updateLastActiveDayUpdate(e){try{let t=(0,r.H9)(a.db,o.users,e);await (0,r.mZ)(t,{[i.lastActiveDayUpdate]:r.Dc.now()})}catch(t){console.error(`Error updating last active day timestamp for user ${e}:`,t)}}static async isUserOnLeaveToday(e){try{let{isUserOnLeave:t}=await s.e(7087).then(s.bind(s,87087));return await t(e,new Date)}catch(t){return console.error(`Error checking leave status for user ${e}:`,t),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let e=await (0,r.getDocs)((0,r.collection)(a.db,o.users)),t=0,s=0,i=0;for(let a of e.docs)try{t++;let e=await n.calculateActiveDays(a.id);(e.shouldUpdate||e.isNewDay)&&(await n.updateUserActiveDays(a.id),e.shouldUpdate&&s++)}catch(e){i++,console.error(`Error processing active days for user ${a.id}:`,e)}return console.log(`✅ Daily active days processing complete:`),console.log(`   - Processed: ${t} users`),console.log(`   - Updated: ${s} users`),console.log(`   - Errors: ${i} users`),{processed:t,updated:s,errors:i}}catch(e){throw console.error("Error in daily active days processing:",e),e}}static async getUserActiveDays(e){try{let t=await (0,r.x7)((0,r.H9)(a.db,o.users,e));if(!t.exists())return 0;return t.data().activeDays||0}catch(t){return console.error(`Error getting active days for user ${e}:`,t),0}}static async initializeActiveDaysForNewUser(e){try{let t=(0,r.H9)(a.db,o.users,e);await (0,r.mZ)(t,{[i.activeDays]:1,[i.lastActiveDayUpdate]:r.Dc.now()}),console.log(`✅ Initialized active days for new user ${e}: Day 1`)}catch(t){throw console.error(`Error initializing active days for user ${e}:`,t),t}}static async getActiveDaysDisplay(e){try{let t,s=await (0,r.x7)((0,r.H9)(a.db,o.users,e));if(!s.exists())return{current:0,total:2,displayText:"0/2"};let i=s.data(),n=i.plan||"Trial",l=i.activeDays||0;return t="Trial"===n?2:30,{current:l,total:t,displayText:`${l}/${t}`}}catch(t){return console.error(`Error getting active days display for user ${e}:`,t),{current:0,total:2,displayText:"0/2"}}}static async isUserPlanExpired(e){try{let t=await (0,r.x7)((0,r.H9)(a.db,o.users,e));if(!t.exists())return{expired:!0,reason:"User not found"};let s=t.data(),i=s.plan||"Trial",n=s.activeDays||0,l=s.planExpiry;if(console.log(`📅 Checking plan expiry for user ${e}:`,{plan:i,activeDays:n,hasPlanExpiry:!!l,planExpiryDate:l?l.toDate():null}),"Admin"===i){let t={expired:!1,activeDays:n};return console.log(`📅 Plan expiry result for admin user ${e}:`,t),t}if("Trial"===i){let t=Math.max(0,2-n),s={expired:t<=0,reason:t<=0?"Trial period expired":void 0,daysLeft:t,activeDays:n};return console.log(`📅 Plan expiry result for trial user ${e}:`,s),s}if(l){let t=new Date,s=l.toDate(),a=t>s,r=a?0:Math.ceil((s.getTime()-t.getTime())/864e5),i={expired:a,reason:a?"Plan subscription expired":void 0,daysLeft:r,activeDays:n};return console.log(`📅 Plan expiry result for user ${e} (using planExpiry field):`,i),i}let c=Math.max(0,30-n),d=n>30,u={expired:d,reason:d?`Plan expired - You have used ${n} days out of 30 allowed days`:void 0,daysLeft:c,activeDays:n};return console.log(`📅 Plan expiry result for user ${e}:`,u),u}catch(t){return console.error(`Error checking plan expiry for user ${e}:`,t),{expired:!0,reason:"Error checking plan status"}}}static async setPlanExpiryForUser(e,t=30){try{let s=(0,r.H9)(a.db,o.users,e),i=await (0,r.x7)(s);if(!i.exists())return console.error(`User ${e} not found`),!1;let n=i.data(),l=n.plan||"Trial";if("Trial"===l||"Admin"===l)return console.log(`Skipping plan expiry setup for ${l} user: ${e}`),!1;if(n.planExpiry)return console.log(`User ${e} already has plan expiry set: ${n.planExpiry.toDate()}`),!1;let c=n.joinedDate?.toDate()||new Date,d=new Date(c);return d.setDate(d.getDate()+t),await (0,r.mZ)(s,{planExpiry:r.Dc.fromDate(d),planExpirySetDate:r.Dc.now()}),console.log(`✅ Set plan expiry for user ${e}: ${d}`),!0}catch(t){return console.error(`Error setting plan expiry for user ${e}:`,t),!1}}static async forceUpdateActiveDays(e,t,s){try{let n=(0,r.H9)(a.db,o.users,e);await (0,r.mZ)(n,{[i.activeDays]:t,[i.lastActiveDayUpdate]:r.Dc.now()}),console.log(`🔧 Admin ${s} force updated active days for user ${e}: ${t}`)}catch(t){throw console.error(`Error force updating active days for user ${e}:`,t),t}}static async getActiveDaysStatistics(){try{let e=await (0,r.getDocs)((0,r.collection)(a.db,o.users)),t=0,s=0,i=0,n=0,l=0,c=0,d=new Date().toDateString();for(let a of e.docs){let e=a.data(),r=e.plan||"Trial",o=e.activeDays||0,u=e.lastActiveDayUpdate?.toDate();t++,l+=o,"Trial"===r?s++:"Admin"===r?n++:i++,u&&u.toDateString()===d&&c++}return{totalUsers:t,trialUsers:s,paidUsers:i,adminUsers:n,averageActiveDays:t>0?Math.round(l/t*100)/100:0,usersUpdatedToday:c}}catch(e){throw console.error("Error getting active days statistics:",e),e}}}let l=n.calculateActiveDays,c=n.updateUserActiveDays,d=n.processAllUsersActiveDays,u=n.getUserActiveDays,p=n.initializeActiveDaysForNewUser;n.getActiveDaysDisplay;let m=n.isUserPlanExpired;n.forceUpdateActiveDays,n.getActiveDaysStatistics},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54557:(e,t,s)=>{Promise.resolve().then(s.bind(s,6473))},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95871:(e,t,s)=>{"use strict";s.d(t,{Ou:()=>m,Ov:()=>v,PY:()=>j,ck:()=>f,e5:()=>b,iM:()=>x,lA:()=>y,rB:()=>h,tv:()=>w,wh:()=>g});var a=s(24791),r=s(33784);async function i(){let{auth:e}=await Promise.resolve().then(s.bind(s,33784)),t=e.currentUser;if(!t)throw Error("User not authenticated");try{await t.getIdToken(!0),console.log("✅ User token refreshed and validated")}catch(e){throw console.error("❌ Error refreshing user token:",e),Error("Authentication token expired. Please refresh the page.")}}let o=(0,a.Qg)(r.Cn,"getUserWorkData"),n=(0,a.Qg)(r.Cn,"submitTranslationBatch"),l=(0,a.Qg)(r.Cn,"getUserDashboardData"),c=((0,a.Qg)(r.Cn,"getAdminDashboardData"),(0,a.Qg)(r.Cn,"getUserTransactions")),d=(0,a.Qg)(r.Cn,"processWithdrawalRequest"),u=(0,a.Qg)(r.Cn,"processDailyActiveDays"),p=((0,a.Qg)(r.Cn,"processDailyCopyPasteReduction"),(0,a.Qg)(r.Cn,"grantCopyPastePermission"),(0,a.Qg)(r.Cn,"updateUserPlan"),(0,a.Qg)(r.Cn,"getPlatformStats")),m={functionsUsed:0,firestoreReadsAvoided:0,firestoreWritesOptimized:0,incrementFunctionUsage(){this.functionsUsed++,console.log(`🚀 Firebase Functions used: ${this.functionsUsed}`)},addReadsAvoided(e){this.firestoreReadsAvoided+=e,console.log(`💰 Firestore reads avoided: ${e} (Total: ${this.firestoreReadsAvoided})`)},addWritesOptimized(e){this.firestoreWritesOptimized+=e,console.log(`⚡ Firestore writes optimized: ${e} (Total: ${this.firestoreWritesOptimized})`)},getStats(){return{functionsUsed:this.functionsUsed,firestoreReadsAvoided:this.firestoreReadsAvoided,firestoreWritesOptimized:this.firestoreWritesOptimized,estimatedCostSavings:(36e-5*this.firestoreReadsAvoided+.00108*this.firestoreWritesOptimized).toFixed(4)}}};async function h(){try{console.log("\uD83D\uDE80 Fetching user work data via Firebase Function..."),await i();let e=(await o()).data;return m.incrementFunctionUsage(),m.addReadsAvoided(4),console.log("✅ User work data fetched successfully:",e),e}catch(e){if(console.error("❌ Error fetching user work data:",e),"unauthenticated"===e.code||e.message?.includes("User must be authenticated"))throw Error("Authentication required. Please refresh the page and try again.");throw Error("Failed to fetch user work data")}}async function x(e=50){try{console.log(`🚀 Submitting translation batch via Firebase Function: ${e} translations`),await i();let t=(await n({batchSize:e})).data;return m.incrementFunctionUsage(),m.addWritesOptimized(3),console.log("✅ Translation batch submitted successfully:",t),t}catch(e){if(console.error("❌ Error submitting translation batch:",e),"unauthenticated"===e.code||e.message?.includes("User must be authenticated"))throw Error("Authentication required. Please refresh the page and try again.");if("permission-denied"===e.code)throw Error("Permission denied. Please check your account status.");throw Error("Failed to submit translation batch")}}async function g(){try{console.log("\uD83D\uDE80 Fetching user dashboard data via Firebase Function..."),await i();let e=(await l()).data;return m.incrementFunctionUsage(),m.addReadsAvoided(3),console.log("✅ User dashboard data fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching user dashboard data:",e),Error("Failed to fetch user dashboard data")}}async function y(e=20,t){try{console.log(`🚀 Fetching user transactions via Firebase Function: limit=${e}`);let s=(await c({limit:e,startAfter:t})).data;return m.incrementFunctionUsage(),m.addReadsAvoided(Math.max(0,100-e)),console.log("✅ User transactions fetched successfully:",s),s}catch(e){throw console.error("❌ Error fetching user transactions:",e),Error("Failed to fetch user transactions")}}async function f(e,t){try{console.log(`🚀 Processing withdrawal request via Firebase Function: ₹${e}`);let s=(await d({amount:e,upiId:t})).data;return m.incrementFunctionUsage(),m.addWritesOptimized(2),console.log("✅ Withdrawal request processed successfully:",s),s}catch(e){throw console.error("❌ Error processing withdrawal request:",e),Error("Failed to process withdrawal request")}}async function b(){try{console.log("\uD83D\uDE80 Processing daily active days via Firebase Function...");let e=(await u()).data;return m.incrementFunctionUsage(),m.addWritesOptimized(e.updated||0),console.log("✅ Daily active days processed successfully:",e),e}catch(e){throw console.error("❌ Error processing daily active days:",e),Error("Failed to process daily active days")}}async function w(){try{console.log("\uD83D\uDE80 Fetching platform stats via Firebase Function...");let e=(await p()).data;return m.incrementFunctionUsage(),m.addReadsAvoided(10),console.log("✅ Platform stats fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching platform stats:",e),Error("Failed to fetch platform stats")}}async function v(){try{console.log("\uD83D\uDE80 Getting optimized user work data..."),await i();let e=await o();return console.log("✅ Optimized user work data retrieved"),e.data}catch(e){throw console.error("❌ Error getting optimized user work data:",e),e}}async function j(e=50){try{console.log(`🚀 Submitting optimized translation batch: ${e} translations`);let t=await n({batchSize:e});return console.log("✅ Optimized translation batch submitted"),t.data}catch(e){throw console.error("❌ Error submitting optimized translation batch:",e),e}}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4573,6803,3582,8879,6951],()=>s(48060));module.exports=a})();