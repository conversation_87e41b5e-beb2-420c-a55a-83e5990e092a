(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2439,4246,7718],{2439:(e,t,a)=>{"use strict";a.d(t,{ActiveDaysService:()=>i,S3:()=>h,i7:()=>l,isUserPlanExpired:()=>m,mH:()=>d,nd:()=>u,updateUserActiveDays:()=>c});var s=a(6104),r=a(5317);let o={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},n={users:"users"};class i{static async calculateActiveDays(e){try{var t,a;let o,l=await (0,r.x7)((0,r.H9)(s.db,n.users,e));if(!l.exists())return console.error("User ".concat(e," not found")),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let c=l.data(),d=(null==(t=c.joinedDate)?void 0:t.toDate())||new Date,u=null==(a=c.lastActiveDayUpdate)?void 0:a.toDate(),h=c.activeDays||0,m=c.plan||"Trial",g=new Date,p=g.toDateString(),x=u?u.toDateString():null;if(console.log("\uD83D\uDCC5 Calculating active days for user ".concat(e,":")),console.log("   - Joined: ".concat(d.toDateString())),console.log("   - Current active days: ".concat(h)),console.log("   - Last update: ".concat(x||"Never")),console.log("   - Today: ".concat(p)),console.log("   - Plan: ".concat(m)),console.log("   - Is new day: ".concat(x!==p)),x===p)return console.log("✅ Already updated today for user ".concat(e)),{activeDays:h,shouldUpdate:!1,isNewDay:!1};if("Admin"===m)return console.log("⏭️ Skipping active days increment for admin user ".concat(e)),await i.updateLastActiveDayUpdate(e),{activeDays:h,shouldUpdate:!1,isNewDay:!0};if(await i.isUserOnLeaveToday(e))return console.log("\uD83C\uDFD6️ User ".concat(e," is on leave today, not incrementing active days")),await i.updateLastActiveDayUpdate(e),{activeDays:h,shouldUpdate:!1,isNewDay:!0};return o="Trial"===m?Math.floor((g.getTime()-d.getTime())/864e5)+1:h+1,console.log("\uD83D\uDCC8 New active days calculated: ".concat(h," → ").concat(o)),{activeDays:o,shouldUpdate:o!==h,isNewDay:!0}}catch(t){return console.error("Error calculating active days for user ".concat(e,":"),t),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(e){try{let t=await i.calculateActiveDays(e);if(t.shouldUpdate){let a=(0,r.H9)(s.db,n.users,e);await (0,r.mZ)(a,{[o.activeDays]:t.activeDays,[o.lastActiveDayUpdate]:r.Dc.now()}),console.log("✅ Updated active days for user ".concat(e,": ").concat(t.activeDays))}else t.isNewDay&&await i.updateLastActiveDayUpdate(e);return t.activeDays}catch(t){throw console.error("Error updating active days for user ".concat(e,":"),t),t}}static async updateLastActiveDayUpdate(e){try{let t=(0,r.H9)(s.db,n.users,e);await (0,r.mZ)(t,{[o.lastActiveDayUpdate]:r.Dc.now()})}catch(t){console.error("Error updating last active day timestamp for user ".concat(e,":"),t)}}static async isUserOnLeaveToday(e){try{let{isUserOnLeave:t}=await a.e(9567).then(a.bind(a,9567));return await t(e,new Date)}catch(t){return console.error("Error checking leave status for user ".concat(e,":"),t),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let e=await (0,r.getDocs)((0,r.collection)(s.db,n.users)),t=0,a=0,o=0;for(let s of e.docs)try{t++;let e=await i.calculateActiveDays(s.id);(e.shouldUpdate||e.isNewDay)&&(await i.updateUserActiveDays(s.id),e.shouldUpdate&&a++)}catch(e){o++,console.error("Error processing active days for user ".concat(s.id,":"),e)}return console.log("✅ Daily active days processing complete:"),console.log("   - Processed: ".concat(t," users")),console.log("   - Updated: ".concat(a," users")),console.log("   - Errors: ".concat(o," users")),{processed:t,updated:a,errors:o}}catch(e){throw console.error("Error in daily active days processing:",e),e}}static async getUserActiveDays(e){try{let t=await (0,r.x7)((0,r.H9)(s.db,n.users,e));if(!t.exists())return 0;return t.data().activeDays||0}catch(t){return console.error("Error getting active days for user ".concat(e,":"),t),0}}static async initializeActiveDaysForNewUser(e){try{let t=(0,r.H9)(s.db,n.users,e);await (0,r.mZ)(t,{[o.activeDays]:1,[o.lastActiveDayUpdate]:r.Dc.now()}),console.log("✅ Initialized active days for new user ".concat(e,": Day 1"))}catch(t){throw console.error("Error initializing active days for user ".concat(e,":"),t),t}}static async getActiveDaysDisplay(e){try{let t,a=await (0,r.x7)((0,r.H9)(s.db,n.users,e));if(!a.exists())return{current:0,total:2,displayText:"0/2"};let o=a.data(),i=o.plan||"Trial",l=o.activeDays||0;return t="Trial"===i?2:30,{current:l,total:t,displayText:"".concat(l,"/").concat(t)}}catch(t){return console.error("Error getting active days display for user ".concat(e,":"),t),{current:0,total:2,displayText:"0/2"}}}static async isUserPlanExpired(e){try{let t=await (0,r.x7)((0,r.H9)(s.db,n.users,e));if(!t.exists())return{expired:!0,reason:"User not found"};let a=t.data(),o=a.plan||"Trial",i=a.activeDays||0,l=a.planExpiry;if(console.log("\uD83D\uDCC5 Checking plan expiry for user ".concat(e,":"),{plan:o,activeDays:i,hasPlanExpiry:!!l,planExpiryDate:l?l.toDate():null}),"Admin"===o){let t={expired:!1,activeDays:i};return console.log("\uD83D\uDCC5 Plan expiry result for admin user ".concat(e,":"),t),t}if("Trial"===o){let t=Math.max(0,2-i),a={expired:t<=0,reason:t<=0?"Trial period expired":void 0,daysLeft:t,activeDays:i};return console.log("\uD83D\uDCC5 Plan expiry result for trial user ".concat(e,":"),a),a}if(l){let t=new Date,a=l.toDate(),s=t>a,r=s?0:Math.ceil((a.getTime()-t.getTime())/864e5),o={expired:s,reason:s?"Plan subscription expired":void 0,daysLeft:r,activeDays:i};return console.log("\uD83D\uDCC5 Plan expiry result for user ".concat(e," (using planExpiry field):"),o),o}let c=Math.max(0,30-i),d=i>30,u={expired:d,reason:d?"Plan expired - You have used ".concat(i," days out of 30 allowed days"):void 0,daysLeft:c,activeDays:i};return console.log("\uD83D\uDCC5 Plan expiry result for user ".concat(e,":"),u),u}catch(t){return console.error("Error checking plan expiry for user ".concat(e,":"),t),{expired:!0,reason:"Error checking plan status"}}}static async setPlanExpiryForUser(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30;try{var a;let o=(0,r.H9)(s.db,n.users,e),i=await (0,r.x7)(o);if(!i.exists())return console.error("User ".concat(e," not found")),!1;let l=i.data(),c=l.plan||"Trial";if("Trial"===c||"Admin"===c)return console.log("Skipping plan expiry setup for ".concat(c," user: ").concat(e)),!1;if(l.planExpiry)return console.log("User ".concat(e," already has plan expiry set: ").concat(l.planExpiry.toDate())),!1;let d=(null==(a=l.joinedDate)?void 0:a.toDate())||new Date,u=new Date(d);return u.setDate(u.getDate()+t),await (0,r.mZ)(o,{planExpiry:r.Dc.fromDate(u),planExpirySetDate:r.Dc.now()}),console.log("✅ Set plan expiry for user ".concat(e,": ").concat(u)),!0}catch(t){return console.error("Error setting plan expiry for user ".concat(e,":"),t),!1}}static async forceUpdateActiveDays(e,t,a){try{let i=(0,r.H9)(s.db,n.users,e);await (0,r.mZ)(i,{[o.activeDays]:t,[o.lastActiveDayUpdate]:r.Dc.now()}),console.log("\uD83D\uDD27 Admin ".concat(a," force updated active days for user ").concat(e,": ").concat(t))}catch(t){throw console.error("Error force updating active days for user ".concat(e,":"),t),t}}static async getActiveDaysStatistics(){try{let t=await (0,r.getDocs)((0,r.collection)(s.db,n.users)),a=0,o=0,i=0,l=0,c=0,d=0,u=new Date().toDateString();for(let s of t.docs){var e;let t=s.data(),r=t.plan||"Trial",n=t.activeDays||0,h=null==(e=t.lastActiveDayUpdate)?void 0:e.toDate();a++,c+=n,"Trial"===r?o++:"Admin"===r?l++:i++,h&&h.toDateString()===u&&d++}return{totalUsers:a,trialUsers:o,paidUsers:i,adminUsers:l,averageActiveDays:a>0?Math.round(c/a*100)/100:0,usersUpdatedToday:d}}catch(e){throw console.error("Error getting active days statistics:",e),e}}}let l=i.calculateActiveDays,c=i.updateUserActiveDays,d=i.processAllUsersActiveDays,u=i.getUserActiveDays,h=i.initializeActiveDaysForNewUser;i.getActiveDaysDisplay;let m=i.isUserPlanExpired;i.forceUpdateActiveDays,i.getActiveDaysStatistics},2649:(e,t,a)=>{"use strict";a.d(t,{Ou:()=>m,Ov:()=>b,PY:()=>D,ck:()=>y,e5:()=>w,iM:()=>p,lA:()=>f,rB:()=>g,tv:()=>v,wh:()=>x});var s=a(2144),r=a(6104);async function o(){let{auth:e}=await Promise.resolve().then(a.bind(a,6104)),t=e.currentUser;if(!t)throw Error("User not authenticated");try{await t.getIdToken(!0),console.log("✅ User token refreshed and validated")}catch(e){throw console.error("❌ Error refreshing user token:",e),Error("Authentication token expired. Please refresh the page.")}}let n=(0,s.Qg)(r.Cn,"getUserWorkData"),i=(0,s.Qg)(r.Cn,"submitTranslationBatch"),l=(0,s.Qg)(r.Cn,"getUserDashboardData"),c=((0,s.Qg)(r.Cn,"getAdminDashboardData"),(0,s.Qg)(r.Cn,"getUserTransactions")),d=(0,s.Qg)(r.Cn,"processWithdrawalRequest"),u=(0,s.Qg)(r.Cn,"processDailyActiveDays"),h=((0,s.Qg)(r.Cn,"processDailyCopyPasteReduction"),(0,s.Qg)(r.Cn,"grantCopyPastePermission"),(0,s.Qg)(r.Cn,"updateUserPlan"),(0,s.Qg)(r.Cn,"getPlatformStats")),m={functionsUsed:0,firestoreReadsAvoided:0,firestoreWritesOptimized:0,incrementFunctionUsage(){this.functionsUsed++,console.log("\uD83D\uDE80 Firebase Functions used: ".concat(this.functionsUsed))},addReadsAvoided(e){this.firestoreReadsAvoided+=e,console.log("\uD83D\uDCB0 Firestore reads avoided: ".concat(e," (Total: ").concat(this.firestoreReadsAvoided,")"))},addWritesOptimized(e){this.firestoreWritesOptimized+=e,console.log("⚡ Firestore writes optimized: ".concat(e," (Total: ").concat(this.firestoreWritesOptimized,")"))},getStats(){return{functionsUsed:this.functionsUsed,firestoreReadsAvoided:this.firestoreReadsAvoided,firestoreWritesOptimized:this.firestoreWritesOptimized,estimatedCostSavings:(36e-5*this.firestoreReadsAvoided+.00108*this.firestoreWritesOptimized).toFixed(4)}}};async function g(){try{console.log("\uD83D\uDE80 Fetching user work data via Firebase Function..."),await o();let e=(await n()).data;return m.incrementFunctionUsage(),m.addReadsAvoided(4),console.log("✅ User work data fetched successfully:",e),e}catch(t){var e;if(console.error("❌ Error fetching user work data:",t),"unauthenticated"===t.code||(null==(e=t.message)?void 0:e.includes("User must be authenticated")))throw Error("Authentication required. Please refresh the page and try again.");throw Error("Failed to fetch user work data")}}async function p(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;try{console.log("\uD83D\uDE80 Submitting translation batch via Firebase Function: ".concat(e," translations")),await o();let t=(await i({batchSize:e})).data;return m.incrementFunctionUsage(),m.addWritesOptimized(3),console.log("✅ Translation batch submitted successfully:",t),t}catch(e){var t;if(console.error("❌ Error submitting translation batch:",e),"unauthenticated"===e.code||(null==(t=e.message)?void 0:t.includes("User must be authenticated")))throw Error("Authentication required. Please refresh the page and try again.");if("permission-denied"===e.code)throw Error("Permission denied. Please check your account status.");throw Error("Failed to submit translation batch")}}async function x(){try{console.log("\uD83D\uDE80 Fetching user dashboard data via Firebase Function..."),await o();let e=(await l()).data;return m.incrementFunctionUsage(),m.addReadsAvoided(3),console.log("✅ User dashboard data fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching user dashboard data:",e),Error("Failed to fetch user dashboard data")}}async function f(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20,t=arguments.length>1?arguments[1]:void 0;try{console.log("\uD83D\uDE80 Fetching user transactions via Firebase Function: limit=".concat(e));let a=(await c({limit:e,startAfter:t})).data;return m.incrementFunctionUsage(),m.addReadsAvoided(Math.max(0,100-e)),console.log("✅ User transactions fetched successfully:",a),a}catch(e){throw console.error("❌ Error fetching user transactions:",e),Error("Failed to fetch user transactions")}}async function y(e,t){try{console.log("\uD83D\uDE80 Processing withdrawal request via Firebase Function: ₹".concat(e));let a=(await d({amount:e,upiId:t})).data;return m.incrementFunctionUsage(),m.addWritesOptimized(2),console.log("✅ Withdrawal request processed successfully:",a),a}catch(e){throw console.error("❌ Error processing withdrawal request:",e),Error("Failed to process withdrawal request")}}async function w(){try{console.log("\uD83D\uDE80 Processing daily active days via Firebase Function...");let e=(await u()).data;return m.incrementFunctionUsage(),m.addWritesOptimized(e.updated||0),console.log("✅ Daily active days processed successfully:",e),e}catch(e){throw console.error("❌ Error processing daily active days:",e),Error("Failed to process daily active days")}}async function v(){try{console.log("\uD83D\uDE80 Fetching platform stats via Firebase Function...");let e=(await h()).data;return m.incrementFunctionUsage(),m.addReadsAvoided(10),console.log("✅ Platform stats fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching platform stats:",e),Error("Failed to fetch platform stats")}}async function b(){try{console.log("\uD83D\uDE80 Getting optimized user work data..."),await o();let e=await n();return console.log("✅ Optimized user work data retrieved"),e.data}catch(e){throw console.error("❌ Error getting optimized user work data:",e),e}}async function D(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;try{console.log("\uD83D\uDE80 Submitting optimized translation batch: ".concat(e," translations"));let t=await i({batchSize:e});return console.log("✅ Optimized translation batch submitted"),t.data}catch(e){throw console.error("❌ Error submitting optimized translation batch:",e),e}}},7381:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>j});var s=a(5155),r=a(2115),o=a(6874),n=a.n(o),i=a(6681),l=a(7460),c=a(6572),d=a(3592),u=a(2649),h=a(2439),m=a(9567),g=a(3663),p=a(7718),x=a(5738),f=a(8647),y=a(4752),w=a.n(y);function v(e){let{onProgressRestored:t,onRecoveryComplete:a}=e,{user:o,loading:n}=(0,i.hD)(),[l,c]=(0,r.useState)(!1),[d,u]=(0,r.useState)(null),[h,m]=(0,r.useState)(!1);(0,r.useEffect)(()=>{o&&!n&&g();let e=setTimeout(()=>{l&&(console.log("Session recovery timeout - proceeding without recovery"),c(!1),null==a||a())},1e4);return()=>clearTimeout(e)},[o,n,l,a]);let g=async()=>{try{c(!0);let e=x.i.getWorkProgress();if(e&&e.userId===(null==o?void 0:o.uid)){let t=new Date(e.lastSaved);if((new Date().getTime()-t.getTime())/36e5<24){let a=new Date().toDateString();if(t.toDateString()===a&&e.completedTranslations>0){u(e),m(!0);return}}}let t="work_progress_backup_".concat(null==o?void 0:o.uid),s=localStorage.getItem(t);if(s)try{let e=JSON.parse(s),t=new Date(e.lastSaved),a=new Date().toDateString();if(t.toDateString()===a&&e.completedTranslations>0){u(e),m(!0);return}}catch(e){console.error("Error parsing backup data:",e)}null==a||a()}catch(e){console.error("Error checking for recoverable session:",e),null==a||a()}finally{c(!1)}};return l?(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,s.jsx)("div",{className:"bg-white rounded-lg p-6 max-w-md mx-4",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Checking for Previous Session"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Looking for any unsaved work..."})]})})}):h&&d?(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,s.jsx)("div",{className:"bg-white rounded-lg p-6 max-w-md mx-4",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4",children:(0,s.jsx)("i",{className:"fas fa-history text-yellow-600 text-xl"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Previous Session Found"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"We found unsaved work from your previous session. Would you like to restore it?"}),(0,s.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg mb-6",children:(0,s.jsxs)("div",{className:"text-sm text-gray-700",children:[(0,s.jsxs)("p",{className:"mb-2",children:[(0,s.jsx)("strong",{children:"Progress:"})," ",d.completedTranslations,"/50 translations"]}),(0,s.jsxs)("p",{className:"mb-2",children:[(0,s.jsx)("strong",{children:"Last saved:"})," ",new Date(d.lastSaved).toLocaleString()]}),d.userTypedText&&(0,s.jsxs)("p",{className:"mb-2",children:[(0,s.jsx)("strong",{children:"Current text:"}),' "',d.userTypedText.substring(0,50),'..."']})]})}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsx)("button",{onClick:()=>{try{if(null==o?void 0:o.uid){x.i.clearWorkProgress();let e="work_progress_backup_".concat(o.uid);localStorage.removeItem(e)}w().fire({icon:"info",title:"Starting Fresh",text:"Previous session cleared. Starting a new work session.",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error clearing session:",e)}finally{m(!1),null==a||a()}},className:"flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:"Start Fresh"}),(0,s.jsx)("button",{onClick:()=>{if(d)try{x.i.saveWorkProgress(d),null==t||t(d),w().fire({icon:"success",title:"Session Restored!",html:'\n          <div class="text-center">\n            <p class="mb-3">Your previous work session has been successfully restored.</p>\n            <div class="bg-green-50 p-3 rounded-lg">\n              <p class="text-sm text-green-700">\n                <strong>Progress:</strong> '.concat(d.completedTranslations,'/50 translations completed\n              </p>\n              <p class="text-sm text-green-700">\n                <strong>Last saved:</strong> ').concat(new Date(d.lastSaved).toLocaleString(),"\n              </p>\n            </div>\n          </div>\n        "),confirmButtonText:"Continue Working",confirmButtonColor:"#10b981"})}catch(e){console.error("Error restoring session:",e),w().fire({icon:"error",title:"Restoration Failed",text:"Failed to restore your session. Starting fresh."})}finally{m(!1),null==a||a()}},className:"flex-1 px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 transition-colors",children:"Restore Session"})]})]})})}):null}var b=a(6104),D=a(5317);function j(){let{user:e,loading:t}=(0,i.Nu)(),{markDataLoaded:o}=function(e){let[t,a]=(0,r.useState)(!1),[s]=(0,r.useState)(Date.now());return{markDataLoaded:()=>{if(!t){a(!0);let t=Date.now()-s;console.log("⚡ ".concat(e," data loaded in ").concat(t,"ms"))}},isDataLoaded:t}}("WorkPage"),{hasBlockingNotifications:y,isChecking:j,markAllAsRead:N}=(0,l.J)((null==e?void 0:e.uid)||null),{isBlocked:P,leaveStatus:C}=(0,c.l)({userId:(null==e?void 0:e.uid)||null,checkInterval:12e4,enabled:!!e});console.log("WorkPage render:",{user:null==e?void 0:e.uid,loading:t,hasBlockingNotifications:y,isChecking:j,isLeaveBlocked:P});let[k,T]=(0,r.useState)(null),[E,S]=(0,r.useState)(0),[A,U]=(0,r.useState)(0),[F,R]=(0,r.useState)(0),[B,L]=(0,r.useState)(!1),[W,z]=(0,r.useState)(!1),[O,_]=(0,r.useState)([]),[M,I]=(0,r.useState)(!0),[q,H]=(0,r.useState)(""),[Y,Q]=(0,r.useState)(""),[G,Z]=(0,r.useState)([]),[V,K]=(0,r.useState)(!1),[J,X]=(0,r.useState)(!1),[$,ee]=(0,r.useState)(0),[et,ea]=(0,r.useState)(""),[es,er]=(0,r.useState)(!1),[eo,en]=(0,r.useState)(!1),[ei,el]=(0,r.useState)(0),[ec,ed]=(0,r.useState)(!1),[eu,eh]=(0,r.useState)(0),[em,eg]=(0,r.useState)(!1),[ep,ex]=(0,r.useState)(""),[ef,ey]=(0,r.useState)(null),[ew,ev]=(0,r.useState)(!1),[eb,eD]=(0,r.useState)(!1),[ej,eN]=(0,r.useState)(!1),[eP,eC]=(0,r.useState)(!1),[ek,eT]=(0,r.useState)(!1),[eE,eS]=(0,r.useState)({earningPerBatch:25,plan:"Trial"}),[eA,eU]=(0,r.useState)(null),[eF,eR]=(0,r.useState)(0),[eB,eL]=(0,r.useState)(0),[eW,ez]=(0,r.useState)(!1);(0,r.useEffect)(()=>{if(e&&!ej){eD(!0);let e=setTimeout(()=>{console.log("Session recovery timeout - proceeding without recovery"),eN(!0),eD(!1)},15e3);return()=>clearTimeout(e)}e&&ej&&e_()},[e,ej]),(0,r.useEffect)(()=>{L(F>=50),eT(F>=50&&!eP)},[F,eP]),(0,r.useEffect)(()=>{if(!e||!k)return;let t=setInterval(()=>{eG()},1e4);return()=>clearInterval(t)},[e,k,q,et,V,F]),(0,r.useEffect)(()=>{if(e&&k&&q){let e=setTimeout(()=>{eG()},2e3);return()=>clearTimeout(e)}},[q]),(0,r.useEffect)(()=>{let t=t=>{if(e&&k&&(q||F>0))return eG(),t.preventDefault(),t.returnValue="You have unsaved work progress. Are you sure you want to leave?",t.returnValue};return window.addEventListener("beforeunload",t),()=>window.removeEventListener("beforeunload",t)},[e,k,q,F]),(0,r.useEffect)(()=>{if(!(null==e?void 0:e.uid))return;let t=t=>{let{userId:a,hasPermission:s,timestamp:r,updatedBy:o}=t.detail;a===e.uid&&(console.log("\uD83D\uDCE1 Received copy-paste permission update: ".concat(s," (by ").concat(o,")")),X(s),w().fire({icon:s?"success":"warning",title:s?"Copy-Paste Enabled!":"Copy-Paste Disabled!",text:s?"Copy-paste permission has been enabled by admin. You can now copy and paste text.":"Copy-paste permission has been disabled by admin. Please type text manually.",timer:4e3,showConfirmButton:!0,confirmButtonText:"OK"}))},a=()=>{try{let t="copyPasteUpdate_".concat(e.uid),a=localStorage.getItem(t);if(a){let e=JSON.parse(a);Date.now()-e.timestamp<3e4&&(console.log("\uD83D\uDCE1 Found recent copy-paste update in localStorage: ".concat(e.hasPermission)),X(e.hasPermission),w().fire({icon:e.hasPermission?"success":"warning",title:e.hasPermission?"Copy-Paste Enabled!":"Copy-Paste Disabled!",text:e.hasPermission?"Copy-paste permission has been enabled by admin.":"Copy-paste permission has been disabled by admin.",timer:3e3,showConfirmButton:!1}),localStorage.removeItem(t))}}catch(e){console.error("Error checking for copy-paste updates:",e)}};window.addEventListener("copyPastePermissionChanged",t),a();let s=setInterval(a,5e3);return()=>{window.removeEventListener("copyPastePermissionChanged",t),clearInterval(s)}},[null==e?void 0:e.uid]),(0,r.useEffect)(()=>{if(!(null==e?void 0:e.uid))return;let t=async()=>{console.log("\uD83D\uDD04 Page gained focus - checking for data updates...");try{await eO();let t=await h.ActiveDaysService.isUserPlanExpired(e.uid);if(t.expired)return void w().fire({icon:"warning",title:"Plan Expired",text:t.reason,confirmButtonText:"Go to Plans"}).then(()=>{window.location.href="/plans"});(await (0,d.getVideoCountData)(e.uid)).todayTranslations>=50&&!eP&&(eC(!0),w().fire({icon:"info",title:"Daily Session Completed",text:"You have completed your daily 50 translations!",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"}))}catch(e){console.error("❌ Error refreshing data on focus:",e)}};return window.addEventListener("focus",t),()=>window.removeEventListener("focus",t)},[null==e?void 0:e.uid,eP]),(0,r.useEffect)(()=>{if(!(null==e?void 0:e.uid))return;let t=setInterval(async()=>{console.log("\uD83D\uDD04 Periodic data refresh...");try{if(await eO(),(await h.ActiveDaysService.isUserPlanExpired(e.uid)).expired){window.location.href="/plans";return}(await (0,d.getVideoCountData)(e.uid)).todayTranslations>=50&&!eP&&(eC(!0),window.location.href="/dashboard")}catch(e){console.error("❌ Error in periodic refresh:",e)}},3e5);return()=>clearInterval(t)},[null==e?void 0:e.uid,eP]);let eO=async()=>{try{ez(!0),console.log("\uD83D\uDD04 Refreshing user data from Firestore...");let t=(0,D.H9)(b.db,"users",e.uid),a=await (0,D.x7)(t);if(a.exists()){let e=a.data();console.log("\uD83D\uDCCA Fresh user data loaded:",{activeDays:null==e?void 0:e.activeDays,copyPasteDaysRemaining:null==e?void 0:e.copyPasteDaysRemaining,plan:null==e?void 0:e.plan,planExpiry:null==e?void 0:e.planExpiry}),eU(e),eL((null==e?void 0:e.activeDays)||0),ee((null==e?void 0:e.copyPasteDaysRemaining)||0);let t=((null==e?void 0:e.copyPasteDaysRemaining)||0)>0;return X(t),console.log("✅ User data refreshed successfully"),e}}catch(e){console.error("❌ Error refreshing user data:",e)}finally{ez(!1)}return null},e_=async()=>{try{console.log("\uD83D\uDD0D Checking work access for user:",e.uid),await eO();let t=await h.ActiveDaysService.isUserPlanExpired(e.uid);if(console.log("\uD83D\uDCC5 Plan status result:",t),t.expired){console.log("\uD83D\uDEAB Work access blocked - Plan expired:",t.reason),w().fire({icon:"error",title:"Plan Expired",html:'\n            <div class="text-center">\n              <p class="mb-3">'.concat(t.reason,'</p>\n              <p class="text-sm text-gray-600">\n                Active Days: ').concat(t.activeDays||0," | Days Left: ").concat(t.daysLeft||0,"\n              </p>\n            </div>\n          "),confirmButtonText:"Upgrade Plan",showCancelButton:!0,cancelButtonText:"Go to Dashboard"}).then(e=>{e.isConfirmed?window.location.href="/plans":window.location.href="/dashboard"});return}let a=await (0,d.getVideoCountData)(e.uid);if(console.log("\uD83D\uDCCA Translation data check:",a),a.todayTranslations>=50){console.log("\uD83D\uDEAB Work access blocked - Daily session completed"),eC(!0),w().fire({icon:"info",title:"Daily Session Completed",html:'\n            <div class="text-center">\n              <p class="mb-3">You have already completed your daily session of 50 translations!</p>\n              <p class="text-sm text-gray-600">\n                Translations completed today: '.concat(a.todayTranslations,'/50\n              </p>\n              <p class="text-sm text-green-600 mt-2">\n                Come back tomorrow for your next session.\n              </p>\n            </div>\n          '),confirmButtonText:"Go to Dashboard",allowOutsideClick:!1,allowEscapeKey:!1}).then(()=>{window.location.href="/dashboard"});return}let s=await (0,m.q8)(e.uid);if(console.log("\uD83D\uDCCA Work status result:",s),s.blocked){console.log("\uD83D\uDEAB Work access blocked:",s.reason),w().fire({icon:"warning",title:"Work Not Available",text:s.reason||"Work is currently blocked.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});return}console.log("✅ Work access allowed, proceeding with ultra-fast loading"),await eH()}catch(e){console.error("❌ Error checking work access (allowing work to proceed):",e),await eH()}},eM=async()=>{try{console.log("\uD83D\uDCCA Loading translation data for user:",e.uid);let t=await (0,d.getVideoCountData)(e.uid);console.log("\uD83D\uDCCA Translation data loaded:",t),S(t.todayTranslations),U(t.totalTranslations)}catch(e){console.error("Error loading translation data:",e)}},eI=async()=>{try{let t=await (0,d.getUserVideoSettings)(e.uid);eS({earningPerBatch:t.earningPerBatch,plan:t.plan});let a=await (0,p.checkCopyPastePermission)(e.uid);X(a.hasPermission),ee(a.daysRemaining),console.log("Copy-paste permission status:",{hasPermission:a.hasPermission,daysRemaining:a.daysRemaining,expiryDate:a.expiryDate})}catch(e){console.error("Error loading translation settings:",e)}},eq=async()=>{try{console.log("\uD83D\uDD04 Manual refresh triggered by user"),w().fire({title:"Refreshing Data...",text:"Checking for latest updates",allowOutsideClick:!1,didOpen:()=>{w().showLoading()}}),await eO(),await eM(),await eI();let t=await h.ActiveDaysService.isUserPlanExpired(e.uid);if(t.expired)return void w().fire({icon:"warning",title:"Plan Expired",text:t.reason,confirmButtonText:"Go to Plans"}).then(()=>{window.location.href="/plans"});if((await (0,d.getVideoCountData)(e.uid)).todayTranslations>=50)return void w().fire({icon:"info",title:"Daily Session Completed",text:"You have completed your daily 50 translations!",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});w().fire({icon:"success",title:"Data Refreshed!",text:"All information has been updated",timer:2e3,showConfirmButton:!1})}catch(e){console.error("❌ Error in manual refresh:",e),w().fire({icon:"error",title:"Refresh Failed",text:"Could not refresh data. Please try again.",confirmButtonText:"OK"})}},eH=async()=>{try{if(I(!0),console.log("\uD83D\uDE80 Ultra-fast loading work page data via Firebase Functions..."),!(null==e?void 0:e.uid))throw Error("User not authenticated");await new Promise(e=>setTimeout(e,100));let t=await (0,u.rB)();if(u.Ou.incrementFunctionUsage(),console.log("\uD83D\uDCB0 Work page cost optimization:",u.Ou.getStats()),!t.workAccess.canWork){console.log("\uD83D\uDEAB User cannot work today:",t.workAccess.reason),eC(!0);return}eS({earningPerBatch:t.planStatus.earningPerBatch,plan:t.userProfile.plan}),S(t.translationProgress.todayTranslations),U(t.translationProgress.totalTranslations),eL(t.userProfile.activeDays),eR(t.planStatus.daysLeft),eU({name:t.userProfile.name,email:t.userProfile.email,plan:t.userProfile.plan,activeDays:t.userProfile.activeDays});let a=(await (0,g.bj)(e.uid)).translations||[];_(a),eZ(),k||eJ(a),console.log("✅ Ultra-fast initialization complete!"),o()}catch(e){var t,a;if(console.error("❌ Error in ultra-fast initialization, falling back to fast method:",e),(null==(t=e.message)?void 0:t.includes("Authentication required"))||(null==(a=e.message)?void 0:a.includes("User not authenticated"))){console.log("\uD83D\uDD04 Authentication issue detected, retrying in 2 seconds..."),setTimeout(()=>{window.location.reload()},2e3);return}await eY()}finally{I(!1)}},eY=async()=>{try{I(!0),console.log("\uD83D\uDE80 Fast loading work page data...");let t=await (0,g.bj)(e.uid);if(!t.canWork){console.log("\uD83D\uDEAB User cannot work today"),eC(!0);return}eS({earningPerBatch:t.userSettings.earningPerBatch,plan:t.userSettings.plan}),X(t.userSettings.hasQuickAdvantage),ee(t.userSettings.copyPasteDaysRemaining),S(t.todayProgress.todayTranslations),U(t.todayProgress.totalTranslations),eL(t.todayProgress.activeDays||0);let a=t.translations.map(e=>({english:e.english,hindi:e.hindi,spanish:e.spanish,french:e.french,german:e.german,italian:e.italian,portuguese:e.portuguese,russian:e.russian,arabic:e.arabic,chinese:e.chinese,japanese:e.japanese,korean:e.korean,turkish:e.turkish,dutch:e.dutch,swedish:e.swedish,polish:e.polish,ukrainian:e.ukrainian,greek:e.greek,hebrew:e.hebrew,vietnamese:e.vietnamese,thai:e.thai}));_(a),eZ(),k||eJ(a),console.log("✅ Fast initialization complete!")}catch(e){console.error("❌ Error in fast initialization:",e),eM(),eI(),eQ(),eV()}finally{I(!1)}},eQ=async()=>{try{let t=await (0,d.getUserData)(e.uid);if(eU(t),t){let a=await h.ActiveDaysService.isUserPlanExpired(e.uid);eR(a.daysLeft||0),eL(a.activeDays||0),console.log("\uD83D\uDCCA Plan status loaded:",{plan:t.plan,expired:a.expired,daysLeft:a.daysLeft,activeDays:a.activeDays,reason:a.reason})}}catch(e){console.error("Error loading user data:",e)}},eG=async()=>{if(e&&k)try{ev(!0);let t={currentStep:k,userTypedText:q,selectedLanguage:et,isTypingComplete:V,completedTranslations:F,batchProgress:F/50*100,lastSaved:new Date,userId:e.uid};x.i.saveWorkProgress(t);let a="work_progress_backup_".concat(e.uid);localStorage.setItem(a,JSON.stringify(t)),ey(new Date),console.log("✅ Work progress saved successfully")}catch(e){console.error("❌ Error saving work progress:",e)}finally{ev(!1)}},eZ=()=>{let t=new Date().toDateString(),a="translation_session_".concat(e.uid,"_").concat(t),s=localStorage.getItem(a);s&&R(parseInt(s))},eV=async()=>{try{I(!0);let e=null!==k,{initializeTranslationSystem:t}=await Promise.resolve().then(a.bind(a,3663)),s=(await t()).map(e=>({english:e.english,hindi:e.hindi,spanish:e.spanish,french:e.french,german:e.german,italian:e.italian,portuguese:e.portuguese,russian:e.russian,arabic:e.arabic,chinese:e.chinese,japanese:e.japanese,korean:e.korean,turkish:e.turkish,dutch:e.dutch,swedish:e.swedish,polish:e.polish,ukrainian:e.ukrainian,greek:e.greek,hebrew:e.hebrew,vietnamese:e.vietnamese,thai:e.thai}));_(s),e?console.log("\uD83D\uDD04 Using restored translation step"):eJ(s)}catch(e){console.error("Error loading translations:",e),w().fire({icon:"error",title:"Loading Error",text:"Failed to load translation data. Please refresh the page."})}finally{I(!1)}},eK=()=>{Z([]),eg(!1),ex("")},eJ=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:O;if(0===e.length)return;let t=Math.floor(Math.random()*e.length),a=e[t],s=(0,g.jQ)(),r=g.cb.find(e=>e.code===s);T({id:"step_".concat(Date.now(),"_").concat(Math.random()),englishText:a.english,targetLanguage:s,targetLanguageName:(null==r?void 0:r.name)||"Unknown",targetTranslation:a[s]||"Translation not available",userTypedText:"",selectedLanguage:"",isTypingComplete:!1,isLanguageSelected:!1,isConverted:!1,isSubmitted:!1}),H(""),Q(""),Z([]),K(!1),ea(""),er(!1),en(!1),ed(!1),eg(!1),ex("")},eX=(0,r.useCallback)(e=>{if(!k||V)return;let t=e.target.value,a=Date.now();0===ei&&(el(a),eh(0));let s=e2(t,k.englishText);if(s.length>0&&!J){let a=s[0];if(t.length>a+1){let s=t.substring(0,a+1);e.target.value=s,H(s),Z(e2(s,k.englishText)),em||(eg(!0),ex("Typing error at position ".concat(a+1)));return}}if(0===s.length&&!J&&!em&&e$(t,a)){H(Y),e.target.value=Y,ed(!0);let t=ep.includes("speed")?"Fast Typing Detected!":"Paste Not Allowed!",a=ep.includes("speed")?"".concat(ep,". Please type at a moderate pace and continue."):"".concat(ep,". Please continue typing manually.");w().fire({icon:"warning",title:t,text:a,timer:2e3,showConfirmButton:!1,toast:!0,position:"top-end"}),setTimeout(()=>{ed(!1)},1e3);return}0===s.length&&Q(t),H(t),Z(s),s.length>0?em||(eg(!0),ex("Typing error detected")):em&&(eg(!1),ex("")),t===k.englishText&&0===s.length&&(K(!0),w().fire({icon:"success",title:"Perfect!",text:"Text typed correctly. Now select the target language.",timer:2e3,showConfirmButton:!1})),el(a),eh(t.length)},[k,V,ec,em,J,q,Y,ep,ei]),e$=(e,t)=>{let a=t-ei,s=e.length-q.length;if(0===ei||e.length<4||em||G.length>0||1>=Math.abs(s))return!1;if(s>5)return console.log("\uD83D\uDEAB Paste detected: More than 5 characters at once"),ex("More than 5 characters added at once"),!0;if(s>3&&a<50)return console.log("\uD83D\uDEAB Paste detected: Unrealistic typing speed (>3 chars in <50ms)"),ex("Typing speed too fast (possible paste)"),!0;if(s>15)return console.log("\uD83D\uDEAB Paste detected: Large text block"),ex("Large text block added instantly"),!0;if(e.length>30&&e===(null==k?void 0:k.englishText.substring(0,e.length))){let a=t-(0===ei?t:ei),s=e.length/(a/1e3);if(s>10&&a>1e3)return console.log("\uD83D\uDEAB Paste detected: Perfect match with high speed",{charsPerSecond:s,totalTime:a}),ex("Perfect text match with unrealistic speed"),!0}if(s>3){let t=e.substring(q.length);if(t.trim().split(/\s+/).length>=2&&t.includes(" "))return console.log("\uD83D\uDEAB Paste detected: Multiple words added at once"),ex("Multiple words added simultaneously"),!0}return!1},e0=(0,r.useCallback)(e=>{!J&&((e.ctrlKey||e.metaKey)&&"v"===e.key&&(e.preventDefault(),w().fire({icon:"warning",title:"Paste Not Allowed!",text:"Keyboard paste shortcuts are disabled. Please continue typing manually.",timer:2e3,toast:!0,position:"top-end",showConfirmButton:!1})),e.repeat&&(console.log("\uD83D\uDEAB Long press detected"),"Backspace"!==e.key&&"Delete"!==e.key&&e.preventDefault())),G.length>0&&setTimeout(()=>{0===e2(e.target.value,(null==k?void 0:k.englishText)||"").length&&eK()},10)},[J,G,k]),e1=(0,r.useCallback)(e=>{J||(e.preventDefault(),w().fire({icon:"warning",title:"Drag & Drop Not Allowed!",text:"Please continue typing the text manually.",timer:2e3,toast:!0,position:"top-end",showConfirmButton:!1}))},[J]),e3=(0,r.useCallback)(e=>{J||(e.preventDefault(),w().fire({icon:"warning",title:"Context Menu Disabled",text:"Right-click menu is disabled to prevent paste operations.",timer:1500}))},[J]),e2=(e,t)=>{let a=[];for(let s=0;s<e.length;s++)(s>=t.length||e[s]!==t[s])&&a.push(s);return a},e5=e=>{k&&V&&(ea(e),e===k.targetLanguage?(er(!0),w().fire({icon:"success",title:"Correct Language!",text:"You selected the correct language. Click Convert to see the translation.",timer:2e3,showConfirmButton:!1})):(er(!1),w().fire({icon:"error",title:"Wrong Language!",text:"Please select ".concat(k.targetLanguageName," language."),timer:2e3,showConfirmButton:!1})))},e4=async()=>{if(B&&!W&&!(F<50)){if(P)return void w().fire({icon:"warning",title:"Submission Not Available",text:C.reason||"Translation submission is not available due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});try{if(z(!0),console.log("\uD83D\uDE80 Submitting translation batch via Firebase Function..."),!(null==e?void 0:e.uid))throw Error("User not authenticated");await new Promise(e=>setTimeout(e,100));let t=await (0,u.iM)(50);u.Ou.incrementFunctionUsage(),console.log("\uD83D\uDCB0 Cost optimization stats:",u.Ou.getStats()),console.log("✅ Translation batch submitted successfully:",t),S(t.newTodayTranslations),U(t.newTotalTranslations);let a=new Date().toDateString(),s="translation_session_".concat(e.uid,"_").concat(a);localStorage.removeItem(s),R(0),L(!1),eT(!1),eC(!0),w().fire({icon:"success",title:"\uD83C\uDF89 Daily Session Completed!",html:'\n          <div class="text-center">\n            <p class="text-lg font-bold text-green-600 mb-2">₹'.concat(t.earningAmount,' Earned!</p>\n            <p class="mb-2">50 translations completed and submitted</p>\n            <p class="text-sm text-gray-600 mb-3">Earnings have been added to your wallet</p>\n            <p class="text-sm text-blue-600 font-semibold">\n              \uD83C\uDF89 Your daily session is complete! Come back tomorrow for your next session.\n            </p>\n            <p class="text-xs text-gray-500 mt-2">\n              \uD83D\uDCB0 New wallet balance: ₹').concat(t.newWallet,"\n            </p>\n          </div>\n        "),confirmButtonText:"Go to Dashboard",timer:6e3,showConfirmButton:!0}).then(()=>{window.location.href="/dashboard"})}catch(o){var t,a,s;console.error("Error submitting translations:",o);let e="There was an error submitting your translations. Please try again.",r=!1;(null==(t=o.message)?void 0:t.includes("Authentication required"))?(e="Authentication expired. Please refresh the page and try again.",r=!0):(null==(a=o.message)?void 0:a.includes("Permission denied"))?e="Permission denied. Please check your account status or contact support.":(null==(s=o.message)?void 0:s.includes("User not authenticated"))&&(e="Session expired. Please refresh the page and log in again.",r=!0),w().fire({icon:"error",title:"Submission Failed",text:e,confirmButtonText:r?"Refresh Page":"Try Again",showCancelButton:r,cancelButtonText:r?"Cancel":void 0}).then(e=>{e.isConfirmed&&r&&window.location.reload()})}finally{z(!1)}}};return t||M||j&&!e?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner mb-4"}),(0,s.jsx)("p",{className:"text-white",children:t?"Loading...":j?"Checking notifications...":"Loading work data..."}),(0,s.jsx)("p",{className:"text-white/60 text-sm mt-2",children:"\uD83D\uDE80 Using Firebase Functions for optimal performance"})]})}):t?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-white",children:"Loading work page..."})]})}):e?y&&e?(0,s.jsx)(f.A,{userId:e.uid,onAllRead:N}):eP&&e?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"max-w-md w-full bg-white/10 backdrop-blur-lg rounded-2xl p-8 text-center shadow-2xl",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("i",{className:"fas fa-check-circle text-6xl text-green-400 mb-4"}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:"Daily Work Completed! \uD83C\uDF89"}),(0,s.jsx)("p",{className:"text-white/80 text-lg",children:"You've successfully completed your 50 translations for today."})]}),(0,s.jsxs)("div",{className:"bg-white/5 rounded-xl p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsx)("span",{className:"text-white/70",children:"Today's Earnings:"}),(0,s.jsxs)("span",{className:"text-green-400 font-bold text-xl",children:["₹",eE.earningPerBatch]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsx)("span",{className:"text-white/70",children:"Translations Completed:"}),(0,s.jsx)("span",{className:"text-white font-semibold",children:"50/50"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-white/70",children:"Next Session:"}),(0,s.jsx)("span",{className:"text-yellow-400 font-semibold",children:"Tomorrow"})]})]}),(0,s.jsxs)("div",{className:"text-white/60 text-sm mb-6",children:[(0,s.jsxs)("p",{className:"mb-2",children:[(0,s.jsx)("i",{className:"fas fa-clock mr-2"}),"Your work session is locked until tomorrow"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("i",{className:"fas fa-calendar-alt mr-2"}),"Come back tomorrow for your next 50 translations"]})]}),(0,s.jsxs)("button",{onClick:()=>window.location.href="/dashboard",className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105",children:[(0,s.jsx)("i",{className:"fas fa-home mr-2"}),"Go to Dashboard"]})]})}):(0,s.jsxs)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:[eb&&(0,s.jsx)(v,{onProgressRestored:e=>{T(e.currentStep),H(e.userTypedText),Q(e.userTypedText),ea(e.selectedLanguage),K(e.isTypingComplete),R(e.completedTranslations),ey(new Date(e.lastSaved))},onRecoveryComplete:()=>{eN(!0),eD(!1)}}),(0,s.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)(n(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("h1",{className:"text-xl font-bold text-white",children:"Translate Text & Earn"}),(0,s.jsx)("button",{onClick:eq,disabled:eW,className:"glass-button px-3 py-2 text-white hover:bg-white/20 transition-colors ".concat(eW?"opacity-50 cursor-not-allowed":""),title:"Refresh data to check for updates",children:(0,s.jsx)("i",{className:"fas fa-sync-alt ".concat(eW?"animate-spin":"")})})]}),(0,s.jsxs)("div",{className:"text-white text-right",children:[(0,s.jsxs)("p",{className:"text-sm",children:["Plan: ",eE.plan]}),(0,s.jsxs)("p",{className:"text-sm",children:["₹",eE.earningPerBatch,"/batch (50 translations)"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-5 gap-2 text-center",children:[(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-blue-400",children:E}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"Today TL"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-green-400",children:A}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"Total TL"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-purple-400",children:Math.max(0,50-F)}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"TL Left"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsxs)("p",{className:"text-lg font-bold text-orange-400",children:[eB,"/","Trial"===eE.plan?"2":"30"]}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"Active Days"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsx)("p",{className:"text-lg font-bold ".concat(J?"text-green-400":"text-gray-400"),children:J?$:"0"}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"Copy Days"})]})]}),(0,s.jsx)("div",{className:"flex items-center justify-center mt-3",children:ew?(0,s.jsxs)("span",{className:"text-yellow-400 text-sm",children:[(0,s.jsx)("i",{className:"fas fa-spinner fa-spin mr-1"}),"Saving progress..."]}):ef?(0,s.jsxs)("span",{className:"text-green-400 text-sm",children:[(0,s.jsx)("i",{className:"fas fa-check mr-1"}),"Last saved: ",ef.toLocaleTimeString()]}):(0,s.jsxs)("span",{className:"text-blue-400 text-sm",children:[(0,s.jsx)("i",{className:"fas fa-shield-alt mr-1"}),"Auto-save protection enabled"]})})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,s.jsx)("i",{className:"fas fa-language mr-2"}),"Translate Text & Earn"]}),(0,s.jsxs)("button",{onClick:()=>{H(""),Q(""),Z([]),K(!1),ea(""),er(!1),en(!1),ed(!1),eg(!1),ex(""),el(0),eh(0),w().fire({icon:"info",title:"Reset Complete!",text:"You can now start typing again. Please type carefully.",timer:2e3,showConfirmButton:!1})},className:"glass-button px-3 py-1 text-white text-sm",title:"Clear typed text and reset",children:[(0,s.jsx)("i",{className:"fas fa-eraser mr-1"}),"Reset"]})]}),!k&&!M&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-8",children:[(0,s.jsx)("i",{className:"fas fa-exclamation-triangle text-4xl text-yellow-400 mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-white mb-4",children:"No Translation Available"}),(0,s.jsx)("p",{className:"text-white/80 mb-6",children:"Unable to load translation data. This could be due to:"}),(0,s.jsxs)("ul",{className:"text-white/70 text-left max-w-md mx-auto mb-6",children:[(0,s.jsx)("li",{className:"mb-2",children:"• Translation data file not found"}),(0,s.jsx)("li",{className:"mb-2",children:"• Network connectivity issues"}),(0,s.jsx)("li",{className:"mb-2",children:"• Server maintenance"})]}),(0,s.jsxs)("button",{onClick:()=>window.location.reload(),className:"btn-primary px-6 py-3 rounded-lg font-semibold",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Retry Loading"]})]})}),k&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("h3",{className:"text-white font-semibold",children:[(0,s.jsx)("i",{className:"fas fa-keyboard mr-2"}),"Step 1: Type the English text below"]}),J?(0,s.jsxs)("button",{onClick:()=>{navigator.clipboard.writeText(k.englishText),w().fire({icon:"success",title:"Copied!",text:"English text copied to clipboard",timer:1500,showConfirmButton:!1})},className:"group relative bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 animate-pulse",title:"Copy English text",children:[(0,s.jsx)("i",{className:"fas fa-copy mr-2"}),(0,s.jsx)("span",{className:"text-sm",children:"Copy"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-white/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"})]}):(0,s.jsxs)("div",{className:"text-white/60 text-xs bg-white/10 px-3 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-lock mr-1"}),"Copy disabled - Type manually"]})]}),(0,s.jsxs)("div",{className:"bg-white/5 p-3 rounded border-l-4 border-blue-400 mb-3",children:[(0,s.jsx)("div",{className:"max-h-24 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent",children:(0,s.jsx)("p",{className:"text-white text-base md:text-lg font-mono leading-relaxed",children:G.length>0&&!J?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{className:"text-green-400",children:k.englishText.substring(0,G[0])}),(0,s.jsx)("span",{className:"bg-red-500 text-white px-1 rounded animate-pulse",children:k.englishText[G[0]]}),(0,s.jsx)("span",{className:"text-white/60",children:k.englishText.substring(G[0]+1)})]}):k.englishText})}),(0,s.jsxs)("div",{className:"text-xs text-white/60 mt-2",children:[(0,s.jsx)("i",{className:"fas fa-info-circle mr-1"}),G.length>0&&!J?"Error at highlighted character (position ".concat(G[0]+1,")"):"Scroll to see full text if needed"]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("textarea",{value:q,onChange:eX,onKeyDown:e0,onDrop:e1,onContextMenu:e3,disabled:V,placeholder:G.length>0?"Fix the highlighted error and continue typing...":J?"Type or paste the English text here...":"Type the English text here (copy-paste not allowed). Fast typists: please type at moderate speed to avoid triggering anti-paste protection.",className:"w-full h-24 md:h-32 p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200 placeholder-white/60 resize-none font-mono text-sm md:text-base leading-relaxed ".concat(G.length>0?"border-red-500":""," ").concat(V?"border-green-500 bg-green-500/10":""),onPaste:e=>{J||(e.preventDefault(),w().fire({icon:"warning",title:"Paste Not Allowed!",text:"Please continue typing the text manually.",timer:2e3,toast:!0,position:"top-end",showConfirmButton:!1}))},onDragOver:e=>{J||e.preventDefault()},spellCheck:!1,autoComplete:"off",autoCorrect:"off",autoCapitalize:"off"}),J&&(0,s.jsxs)("button",{onClick:async()=>{try{let e=await navigator.clipboard.readText();H(e),eX({target:{value:e}}),w().fire({icon:"success",title:"Pasted!",text:"Text pasted from clipboard",timer:1500,showConfirmButton:!1})}catch(e){w().fire({icon:"error",title:"Paste Failed",text:"Could not access clipboard",timer:1500,showConfirmButton:!1})}},className:"group absolute top-3 right-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold py-2 px-3 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-200 animate-bounce disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:animate-none",title:"Paste from clipboard",disabled:V,children:[(0,s.jsx)("i",{className:"fas fa-paste mr-1"}),(0,s.jsx)("span",{className:"text-xs",children:"Paste"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-white/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"})]})]}),G.length>0&&(0,s.jsxs)("div",{className:"mt-2 text-red-400 text-sm bg-red-500/10 border border-red-500/30 rounded-lg p-3",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(0,s.jsx)("i",{className:"fas fa-exclamation-triangle mr-2"}),(0,s.jsx)("strong",{children:"Typing Error Detected"})]}),(0,s.jsxs)("div",{className:"text-red-300 text-xs mb-2",children:["Error at position ",G[0]+1,': Expected "',k.englishText[G[0]],'" but got "',q[G[0]]||"nothing",'"']}),(0,s.jsxs)("div",{className:"text-red-200 text-xs",children:[(0,s.jsx)("i",{className:"fas fa-edit mr-1"}),"Edit the text box to correct the mistake, then continue typing."]})]}),V&&(0,s.jsxs)("div",{className:"mt-2 text-green-400 text-sm",children:[(0,s.jsx)("i",{className:"fas fa-check-circle mr-1"}),"Perfect! Text typed correctly."]}),ec&&!J&&(0,s.jsxs)("div",{className:"mt-2 p-3 bg-yellow-500/20 border border-yellow-500/30 rounded-lg",children:[(0,s.jsxs)("div",{className:"text-yellow-400 text-sm",children:[(0,s.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),(0,s.jsx)("strong",{children:"Paste Attempt Detected"})]}),(0,s.jsx)("div",{className:"text-yellow-300 text-xs mt-1",children:ep.includes("speed")?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-info-circle mr-1"}),"Fast typing detected. Please type at a moderate pace. You can continue typing normally."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-clipboard mr-1"}),"Paste operation blocked. Please continue typing manually from where you left off."]})}),(0,s.jsxs)("div",{className:"text-yellow-200 text-xs mt-2",children:[(0,s.jsx)("i",{className:"fas fa-arrow-right mr-1"}),"This message will disappear automatically. Continue typing normally."]})]}),G.length>0&&!J&&(0,s.jsx)("div",{className:"mt-3 text-center",children:(0,s.jsxs)("div",{className:"bg-blue-500/10 border border-blue-500/30 rounded-lg p-3",children:[(0,s.jsxs)("div",{className:"text-blue-400 text-sm font-semibold mb-2",children:[(0,s.jsx)("i",{className:"fas fa-lightbulb mr-2"}),"How to Fix the Error:"]}),(0,s.jsxs)("div",{className:"text-blue-300 text-xs space-y-1",children:[(0,s.jsx)("div",{children:"1. Click in the text box and edit the incorrect character"}),(0,s.jsxs)("div",{children:['2. Change it to the correct character: "',k.englishText[G[0]],'"']}),(0,s.jsx)("div",{children:"3. Continue typing the rest of the text"})]})]})}),q&&!V&&0===G.length&&(0,s.jsx)("div",{className:"mt-3 text-center",children:(0,s.jsxs)("button",{onClick:()=>{eX({target:{value:q}})},className:"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold py-2 px-6 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200",title:"Check if typed text is correct",children:[(0,s.jsx)("i",{className:"fas fa-check-circle mr-2"}),"Check Text"]})})]}),V&&(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,s.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,s.jsx)("i",{className:"fas fa-globe mr-2"}),"Step 2: Select the target language - ",k.targetLanguageName]}),(0,s.jsxs)("select",{value:et,onChange:e=>e5(e.target.value),className:"w-full p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200",children:[(0,s.jsx)("option",{value:"",className:"bg-gray-800 text-white",children:"Select target language..."}),g.cb.map(e=>(0,s.jsxs)("option",{value:e.code,className:"bg-gray-800 text-white",children:[e.flag," ",e.name]},e.code))]}),et&&!es&&(0,s.jsxs)("div",{className:"mt-2 text-red-400 text-sm",children:[(0,s.jsx)("i",{className:"fas fa-times-circle mr-1"}),"Wrong language! Please select ",k.targetLanguageName,"."]}),es&&(0,s.jsxs)("div",{className:"mt-2 text-green-400 text-sm",children:[(0,s.jsx)("i",{className:"fas fa-check-circle mr-1"}),"Correct language selected!"]})]}),es&&(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("button",{onClick:()=>{k&&es&&(en(!0),T(e=>e?{...e,isConverted:!0}:null))},disabled:eo,className:"px-8 py-3 rounded-lg font-semibold transition-all duration-300 ".concat(eo?"btn-disabled cursor-not-allowed opacity-50":"btn-primary hover:scale-105"),children:[(0,s.jsx)("i",{className:"fas fa-exchange-alt mr-2"}),"Convert to ",k.targetLanguageName]})}),eo&&(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,s.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,s.jsx)("i",{className:"fas fa-language mr-2"}),k.targetLanguageName," Translation:"]}),(0,s.jsx)("div",{className:"bg-white/5 p-3 rounded border-l-4 border-green-400",children:(0,s.jsx)("p",{className:"text-white text-lg",children:k.targetTranslation})}),(0,s.jsx)("div",{className:"text-center mt-4",children:(0,s.jsxs)("button",{onClick:()=>{if(!k||!eo)return;if(F>=50)return void w().fire({icon:"warning",title:"Daily Limit Reached!",text:"You have already completed 50 translations for today. Please submit your batch to earn rewards.",timer:3e3,showConfirmButton:!1});T(e=>e?{...e,isSubmitted:!0}:null);let t=F+1;R(t);let a=new Date().toDateString(),s="translation_session_".concat(e.uid,"_").concat(a);localStorage.setItem(s,t.toString()),t<50?w().fire({icon:"success",title:"Translation Submitted!",text:"Progress: ".concat(t,"/50 translations completed."),timer:2e3,showConfirmButton:!1}).then(()=>{eJ()}):(L(!0),eT(!0),w().fire({icon:"success",title:"\uD83C\uDF89 All Translations Completed!",text:'You have completed all 50 translations! Click "Submit & Earn" to get your rewards.',timer:3e3,showConfirmButton:!1}),T(null),en(!1),K(!1),H(""),ea(""),er(!1))},disabled:F>=50,className:"px-6 py-3 rounded-lg font-semibold transition-all duration-300 ".concat(F>=50?"btn-disabled cursor-not-allowed opacity-50":"btn-success hover:scale-105"),children:[(0,s.jsx)("i",{className:"fas fa-check mr-2"}),F>=50?"Daily Limit Reached":"Submit Translation"]})})]}),ek&&!eP&&(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl p-6 mb-4 shadow-lg",children:[(0,s.jsx)("h3",{className:"text-white font-bold text-xl mb-2",children:"\uD83C\uDF89 Congratulations! You've completed 50 translations!"}),(0,s.jsx)("p",{className:"text-white/90 mb-4",children:"Click the button below to submit your daily batch and receive your earnings."}),(0,s.jsx)("p",{className:"text-white/80 text-sm",children:"⚠️ After submission, you won't be able to work until tomorrow."})]}),(0,s.jsx)("button",{onClick:e4,disabled:W,className:"bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold py-4 px-12 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none animate-pulse",children:W?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),"Submitting Final Batch..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-trophy mr-2"}),"Submit Final Batch & Earn ₹",eE.earningPerBatch]})})]}),B&&!ek&&(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("button",{onClick:e4,disabled:W,className:"btn-success px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 hover:scale-105",children:[(0,s.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Submit All 50 Translations & Earn ₹",eE.earningPerBatch]})}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("p",{className:"text-white/80",children:["Progress: ",F,"/50 translations completed"]}),(0,s.jsx)("div",{className:"w-full bg-white/20 rounded-full h-2 mt-2",children:(0,s.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-purple-400 h-2 rounded-full transition-all duration-300",style:{width:"".concat(F/50*100,"%")}})})]})]})]})]}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-white",children:"Authenticating..."})]})})}},7718:(e,t,a)=>{"use strict";a.d(t,{Mk:()=>h,checkCopyPastePermission:()=>l,grantCopyPastePermission:()=>c,i7:()=>u,removeCopyPastePermission:()=>d});var s=a(6104),r=a(5317);let o={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},n={users:"users"};class i{static async checkCopyPastePermission(e){try{let t=await (0,r.x7)((0,r.H9)(s.db,n.users,e));if(!t.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let a=t.data()[o.quickTranslationAdvantageExpiry];if(!a)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let i=a.toDate(),l=new Date,c=i>l,d=c?Math.ceil((i.getTime()-l.getTime())/864e5):0;return{hasPermission:c,daysRemaining:d,expiryDate:i}}catch(t){return console.error("Error checking copy-paste permission for user ".concat(e,":"),t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,t){try{let a=new Date;a.setDate(a.getDate()+t);let i=(0,r.H9)(s.db,n.users,e);await (0,r.mZ)(i,{[o.quickTranslationAdvantageExpiry]:r.Dc.fromDate(a),[o.lastCopyPasteReduction]:r.Dc.now()}),console.log("✅ Granted copy-paste permission to user ".concat(e," for ").concat(t," days (expires: ").concat(a.toDateString(),")"))}catch(t){throw console.error("Error granting copy-paste permission to user ".concat(e,":"),t),t}}static async removeCopyPastePermission(e){try{let t=(0,r.H9)(s.db,n.users,e);await (0,r.mZ)(t,{[o.quickTranslationAdvantageExpiry]:null}),console.log("✅ Removed copy-paste permission from user ".concat(e))}catch(t){throw console.error("Error removing copy-paste permission from user ".concat(e,":"),t),t}}static async reduceCopyPasteDays(e){try{let t=await (0,r.x7)((0,r.H9)(s.db,n.users,e));if(!t.exists())return{reduced:!1,daysRemaining:0,expired:!1};let a=t.data(),i=a[o.quickTranslationAdvantageExpiry],l=a[o.lastCopyPasteReduction];if(!i)return{reduced:!1,daysRemaining:0,expired:!1};let c=new Date().toDateString();if((l?l.toDate().toDateString():null)===c){let e=i.toDate(),t=new Date,a=Math.max(0,Math.ceil((e.getTime()-t.getTime())/864e5));return{reduced:!1,daysRemaining:a,expired:0===a}}let d=i.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let h=(0,r.H9)(s.db,n.users,e);if(u<=new Date)return await (0,r.mZ)(h,{[o.quickTranslationAdvantageExpiry]:null,[o.lastCopyPasteReduction]:r.Dc.now()}),console.log("\uD83D\uDCC5 Copy-paste permission expired for user ".concat(e)),{reduced:!0,daysRemaining:0,expired:!0};{await (0,r.mZ)(h,{[o.quickTranslationAdvantageExpiry]:r.Dc.fromDate(u),[o.lastCopyPasteReduction]:r.Dc.now()});let t=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log("\uD83D\uDCC5 Reduced copy-paste days for user ".concat(e,": ").concat(t," days remaining")),{reduced:!0,daysRemaining:t,expired:!1}}}catch(t){return console.error("Error reducing copy-paste days for user ".concat(e,":"),t),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,r.getDocs)((0,r.collection)(s.db,n.users)),t=0,a=0,o=0,l=0;for(let s of e.docs)try{t++;let e=await i.reduceCopyPasteDays(s.id);e.reduced&&(a++,e.expired&&o++)}catch(e){l++,console.error("Error processing copy-paste reduction for user ".concat(s.id,":"),e)}return console.log("✅ Daily copy-paste reduction complete:"),console.log("   - Processed: ".concat(t," users")),console.log("   - Reduced: ".concat(a," users")),console.log("   - Expired: ".concat(o," users")),console.log("   - Errors: ".concat(l," users")),{processed:t,reduced:a,expired:o,errors:l}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let t=await i.checkCopyPastePermission(e);return{hasPermission:t.hasPermission,daysRemaining:t.daysRemaining,expiryDate:t.expiryDate?t.expiryDate.toDateString():null}}catch(t){return console.error("Error getting copy-paste status for user ".concat(e,":"),t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let l=i.checkCopyPastePermission,c=i.grantCopyPastePermission,d=i.removeCopyPastePermission,u=i.reduceCopyPasteDays,h=i.processAllUsersCopyPasteReduction;i.getCopyPasteStatus},8763:(e,t,a)=>{Promise.resolve().then(a.bind(a,7381))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6874,6681,3592,3663,3499,8441,1684,7358],()=>t(8763)),_N_E=e.O()}]);