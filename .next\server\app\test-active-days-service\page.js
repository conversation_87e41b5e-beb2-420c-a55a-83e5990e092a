(()=>{var e={};e.id=7410,e.ids=[1705,7410],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},17537:(e,t,s)=>{Promise.resolve().then(s.bind(s,47538))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},45252:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(60687),a=s(43210),i=s(87979),o=s(51705);function l(){let{user:e,loading:t}=(0,i.Nu)(),[s,l]=(0,a.useState)(""),[c,n]=(0,a.useState)(null),[d,u]=(0,a.useState)(!1),p=async()=>{if(!s.trim())return void alert("Please enter a user ID");u(!0),n(null);try{console.log(`🧪 Testing active days service for user: ${s}`),console.log("1. Testing getUserActiveDays...");let e=await (0,o.nd)(s.trim());console.log("✅ getUserActiveDays result:",e),console.log("2. Testing calculateActiveDays...");let t=await (0,o.i7)(s.trim());console.log("✅ calculateActiveDays result:",t),console.log("3. Testing updateUserActiveDays...");let r=await (0,o.updateUserActiveDays)(s.trim());console.log("✅ updateUserActiveDays result:",r),n({success:!0,currentActiveDays:e,calculation:t,updatedActiveDays:r,message:"Single user test completed successfully"})}catch(e){console.error("❌ Error in single user test:",e),n({success:!1,error:e.message,stack:e.stack,message:"Single user test failed"})}finally{u(!1)}},y=async()=>{u(!0),n(null);try{console.log("\uD83E\uDDEA Testing processAllUsersActiveDays...");let e=await (0,o.mH)();console.log("✅ processAllUsersActiveDays result:",e),n({success:!0,allUsersResult:e,message:"All users test completed successfully"})}catch(e){console.error("❌ Error in all users test:",e),n({success:!1,error:e.message,stack:e.stack,message:"All users test failed"})}finally{u(!1)}};return t?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,r.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,r.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,r.jsxs)("div",{className:"glass-card p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Active Days Service"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white mb-2",children:"User ID (for single user test):"}),(0,r.jsx)("input",{type:"text",value:s,onChange:e=>l(e.target.value),className:"w-full p-3 rounded-lg bg-white/10 text-white border border-white/20",placeholder:"Enter user ID"})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{onClick:p,disabled:d,className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:d?"Testing...":"Test Single User"}),(0,r.jsx)("button",{onClick:y,disabled:d,className:"flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:d?"Testing...":"Test All Users"})]}),c&&(0,r.jsxs)("div",{className:`p-4 rounded-lg ${c.success?"bg-green-500/20 border border-green-500/30":"bg-red-500/20 border border-red-500/30"}`,children:[(0,r.jsx)("h3",{className:`font-bold ${c.success?"text-green-400":"text-red-400"}`,children:c.success?"Success!":"Failed"}),(0,r.jsx)("p",{className:"text-white mt-2",children:c.message}),void 0!==c.currentActiveDays&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Current Active Days:"}),(0,r.jsx)("p",{className:"text-white/80 text-sm mt-1",children:c.currentActiveDays})]}),c.calculation&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Calculation Result:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(c.calculation,null,2)})]}),void 0!==c.updatedActiveDays&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Updated Active Days:"}),(0,r.jsx)("p",{className:"text-white/80 text-sm mt-1",children:c.updatedActiveDays})]}),c.allUsersResult&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"All Users Result:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(c.allUsersResult,null,2)})]}),c.error&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-red-500/20 rounded",children:[(0,r.jsx)("h4",{className:"text-red-400 font-semibold",children:"Error Details:"}),(0,r.jsx)("p",{className:"text-white/80 text-sm mt-1",children:c.error}),c.stack&&(0,r.jsx)("pre",{className:"text-white/60 text-xs mt-2 overflow-auto",children:c.stack})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 p-4 bg-white/5 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,r.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• Enter a user ID to test single user functions"}),(0,r.jsx)("li",{children:'• Click "Test Single User" to test calculation and update functions'}),(0,r.jsx)("li",{children:'• Click "Test All Users" to test the batch processing function'}),(0,r.jsx)("li",{children:"• Check browser console for detailed logs"})]})]})]})})})}},47538:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-active-days-service\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-active-days-service\\page.tsx","default")},51705:(e,t,s)=>{"use strict";s.d(t,{ActiveDaysService:()=>l,S3:()=>p,i7:()=>c,isUserPlanExpired:()=>y,mH:()=>d,nd:()=>u,updateUserActiveDays:()=>n});var r=s(33784),a=s(75535);let i={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},o={users:"users"};class l{static async calculateActiveDays(e){try{let t,s=await (0,a.x7)((0,a.H9)(r.db,o.users,e));if(!s.exists())return console.error(`User ${e} not found`),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let i=s.data(),c=i.joinedDate?.toDate()||new Date,n=i.lastActiveDayUpdate?.toDate(),d=i.activeDays||0,u=i.plan||"Trial",p=new Date,y=p.toDateString(),x=n?n.toDateString():null;if(console.log(`📅 Calculating active days for user ${e}:`),console.log(`   - Joined: ${c.toDateString()}`),console.log(`   - Current active days: ${d}`),console.log(`   - Last update: ${x||"Never"}`),console.log(`   - Today: ${y}`),console.log(`   - Plan: ${u}`),console.log(`   - Is new day: ${x!==y}`),x===y)return console.log(`✅ Already updated today for user ${e}`),{activeDays:d,shouldUpdate:!1,isNewDay:!1};if("Admin"===u)return console.log(`⏭️ Skipping active days increment for admin user ${e}`),await l.updateLastActiveDayUpdate(e),{activeDays:d,shouldUpdate:!1,isNewDay:!0};if(await l.isUserOnLeaveToday(e))return console.log(`🏖️ User ${e} is on leave today, not incrementing active days`),await l.updateLastActiveDayUpdate(e),{activeDays:d,shouldUpdate:!1,isNewDay:!0};return t="Trial"===u?Math.floor((p.getTime()-c.getTime())/864e5)+1:d+1,console.log(`📈 New active days calculated: ${d} → ${t}`),{activeDays:t,shouldUpdate:t!==d,isNewDay:!0}}catch(t){return console.error(`Error calculating active days for user ${e}:`,t),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(e){try{let t=await l.calculateActiveDays(e);if(t.shouldUpdate){let s=(0,a.H9)(r.db,o.users,e);await (0,a.mZ)(s,{[i.activeDays]:t.activeDays,[i.lastActiveDayUpdate]:a.Dc.now()}),console.log(`✅ Updated active days for user ${e}: ${t.activeDays}`)}else t.isNewDay&&await l.updateLastActiveDayUpdate(e);return t.activeDays}catch(t){throw console.error(`Error updating active days for user ${e}:`,t),t}}static async updateLastActiveDayUpdate(e){try{let t=(0,a.H9)(r.db,o.users,e);await (0,a.mZ)(t,{[i.lastActiveDayUpdate]:a.Dc.now()})}catch(t){console.error(`Error updating last active day timestamp for user ${e}:`,t)}}static async isUserOnLeaveToday(e){try{let{isUserOnLeave:t}=await s.e(7087).then(s.bind(s,87087));return await t(e,new Date)}catch(t){return console.error(`Error checking leave status for user ${e}:`,t),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let e=await (0,a.getDocs)((0,a.collection)(r.db,o.users)),t=0,s=0,i=0;for(let r of e.docs)try{t++;let e=await l.calculateActiveDays(r.id);(e.shouldUpdate||e.isNewDay)&&(await l.updateUserActiveDays(r.id),e.shouldUpdate&&s++)}catch(e){i++,console.error(`Error processing active days for user ${r.id}:`,e)}return console.log(`✅ Daily active days processing complete:`),console.log(`   - Processed: ${t} users`),console.log(`   - Updated: ${s} users`),console.log(`   - Errors: ${i} users`),{processed:t,updated:s,errors:i}}catch(e){throw console.error("Error in daily active days processing:",e),e}}static async getUserActiveDays(e){try{let t=await (0,a.x7)((0,a.H9)(r.db,o.users,e));if(!t.exists())return 0;return t.data().activeDays||0}catch(t){return console.error(`Error getting active days for user ${e}:`,t),0}}static async initializeActiveDaysForNewUser(e){try{let t=(0,a.H9)(r.db,o.users,e);await (0,a.mZ)(t,{[i.activeDays]:1,[i.lastActiveDayUpdate]:a.Dc.now()}),console.log(`✅ Initialized active days for new user ${e}: Day 1`)}catch(t){throw console.error(`Error initializing active days for user ${e}:`,t),t}}static async getActiveDaysDisplay(e){try{let t,s=await (0,a.x7)((0,a.H9)(r.db,o.users,e));if(!s.exists())return{current:0,total:2,displayText:"0/2"};let i=s.data(),l=i.plan||"Trial",c=i.activeDays||0;return t="Trial"===l?2:30,{current:c,total:t,displayText:`${c}/${t}`}}catch(t){return console.error(`Error getting active days display for user ${e}:`,t),{current:0,total:2,displayText:"0/2"}}}static async isUserPlanExpired(e){try{let t=await (0,a.x7)((0,a.H9)(r.db,o.users,e));if(!t.exists())return{expired:!0,reason:"User not found"};let s=t.data(),i=s.plan||"Trial",l=s.activeDays||0,c=s.planExpiry;if(console.log(`📅 Checking plan expiry for user ${e}:`,{plan:i,activeDays:l,hasPlanExpiry:!!c,planExpiryDate:c?c.toDate():null}),"Admin"===i){let t={expired:!1,activeDays:l};return console.log(`📅 Plan expiry result for admin user ${e}:`,t),t}if("Trial"===i){let t=Math.max(0,2-l),s={expired:t<=0,reason:t<=0?"Trial period expired":void 0,daysLeft:t,activeDays:l};return console.log(`📅 Plan expiry result for trial user ${e}:`,s),s}if(c){let t=new Date,s=c.toDate(),r=t>s,a=r?0:Math.ceil((s.getTime()-t.getTime())/864e5),i={expired:r,reason:r?"Plan subscription expired":void 0,daysLeft:a,activeDays:l};return console.log(`📅 Plan expiry result for user ${e} (using planExpiry field):`,i),i}let n=Math.max(0,30-l),d=l>30,u={expired:d,reason:d?`Plan expired - You have used ${l} days out of 30 allowed days`:void 0,daysLeft:n,activeDays:l};return console.log(`📅 Plan expiry result for user ${e}:`,u),u}catch(t){return console.error(`Error checking plan expiry for user ${e}:`,t),{expired:!0,reason:"Error checking plan status"}}}static async setPlanExpiryForUser(e,t=30){try{let s=(0,a.H9)(r.db,o.users,e),i=await (0,a.x7)(s);if(!i.exists())return console.error(`User ${e} not found`),!1;let l=i.data(),c=l.plan||"Trial";if("Trial"===c||"Admin"===c)return console.log(`Skipping plan expiry setup for ${c} user: ${e}`),!1;if(l.planExpiry)return console.log(`User ${e} already has plan expiry set: ${l.planExpiry.toDate()}`),!1;let n=l.joinedDate?.toDate()||new Date,d=new Date(n);return d.setDate(d.getDate()+t),await (0,a.mZ)(s,{planExpiry:a.Dc.fromDate(d),planExpirySetDate:a.Dc.now()}),console.log(`✅ Set plan expiry for user ${e}: ${d}`),!0}catch(t){return console.error(`Error setting plan expiry for user ${e}:`,t),!1}}static async forceUpdateActiveDays(e,t,s){try{let l=(0,a.H9)(r.db,o.users,e);await (0,a.mZ)(l,{[i.activeDays]:t,[i.lastActiveDayUpdate]:a.Dc.now()}),console.log(`🔧 Admin ${s} force updated active days for user ${e}: ${t}`)}catch(t){throw console.error(`Error force updating active days for user ${e}:`,t),t}}static async getActiveDaysStatistics(){try{let e=await (0,a.getDocs)((0,a.collection)(r.db,o.users)),t=0,s=0,i=0,l=0,c=0,n=0,d=new Date().toDateString();for(let r of e.docs){let e=r.data(),a=e.plan||"Trial",o=e.activeDays||0,u=e.lastActiveDayUpdate?.toDate();t++,c+=o,"Trial"===a?s++:"Admin"===a?l++:i++,u&&u.toDateString()===d&&n++}return{totalUsers:t,trialUsers:s,paidUsers:i,adminUsers:l,averageActiveDays:t>0?Math.round(c/t*100)/100:0,usersUpdatedToday:n}}catch(e){throw console.error("Error getting active days statistics:",e),e}}}let c=l.calculateActiveDays,n=l.updateUserActiveDays,d=l.processAllUsersActiveDays,u=l.getUserActiveDays,p=l.initializeActiveDaysForNewUser;l.getActiveDaysDisplay;let y=l.isUserPlanExpired;l.forceUpdateActiveDays,l.getActiveDaysStatistics},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},53985:(e,t,s)=>{Promise.resolve().then(s.bind(s,45252))},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},80868:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>n});var r=s(65239),a=s(48088),i=s(88170),o=s.n(i),l=s(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let n={children:["",{children:["test-active-days-service",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,47538)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-active-days-service\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-active-days-service\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-active-days-service/page",pathname:"/test-active-days-service",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4573,6803],()=>s(80868));module.exports=r})();