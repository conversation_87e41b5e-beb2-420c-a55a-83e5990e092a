(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2475],{1187:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>j});var t=s(5155),r=s(2115),l=s(6874),n=s.n(l),i=s(8999),o=s(6681),c=s(3004),d=s(5317),u=s(6104),m=s(3592);function h(e){let a=[];if((!e.name||e.name.trim().length<2)&&a.push("Name is required and must be at least 2 characters"),e.email&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)||a.push("Valid email address is required"),e.mobile&&/^[6-9]\d{9}$/.test(e.mobile)||a.push("Valid 10-digit mobile number is required"),(!e.password||e.password.length<6)&&a.push("Password is required and must be at least 6 characters"),e.plan&&!["Trial","Junior","Senior","Expert"].includes(e.plan)&&a.push("Plan must be one of: Trial, Junior, Senior, Expert"),e.activeDays&&(e.activeDays<0||e.activeDays>365)&&a.push("Active days must be between 0 and 365"),e.wallet&&e.wallet<0&&a.push("Wallet balance cannot be negative"),e.totalTranslations&&e.totalTranslations<0&&a.push("Total translations cannot be negative"),e.referralCode&&(/^TN\d{4}$/.test(e.referralCode)||a.push("Referral code must follow format TN0001, TN0002, etc. (TN followed by 4 digits)")),void 0!==e.quickTranslationAdvantage&&"boolean"!=typeof e.quickTranslationAdvantage&&a.push("Quick translation advantage must be true or false"),e.quickTranslationAdvantageDays&&(e.quickTranslationAdvantageDays<1||e.quickTranslationAdvantageDays>365)&&a.push("Quick translation advantage days must be between 1 and 365"),e.quickTranslationAdvantageSeconds&&(e.quickTranslationAdvantageSeconds<1||e.quickTranslationAdvantageSeconds>420)&&a.push("Quick translation advantage seconds must be between 1 and 420 (7 minutes)"),!0!==e.quickTranslationAdvantage||e.quickTranslationAdvantageDays||a.push("Quick translation advantage days must be provided when quick advantage is enabled"),!0===e.quickTranslationAdvantage&&e.quickTranslationAdvantageSeconds){let a=[1,10,30,60,120,180,240,300,360,420];a.includes(e.quickTranslationAdvantageSeconds)||console.warn("Quick translation advantage seconds ".concat(e.quickTranslationAdvantageSeconds," is not a common duration. Valid options: ").concat(a.join(", ")))}return a}async function p(e,a){try{let s=(0,d.P)((0,d.collection)(u.db,m.COLLECTIONS.users),(0,d._M)(m.Yr.email,"==",e));if(!(await (0,d.getDocs)(s)).empty)return console.log("Email ".concat(e," already exists in Firestore")),!0;let t=(0,d.P)((0,d.collection)(u.db,m.COLLECTIONS.users),(0,d._M)(m.Yr.mobile,"==",a));if(!(await (0,d.getDocs)(t)).empty)return console.log("Mobile ".concat(a," already exists in Firestore")),!0;return!1}catch(e){return console.error("Error checking user existence:",e),!1}}async function x(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;try{let s=h(e);if(s.length>0)return{success:!1,error:s.join(", ")};if(await p(e.email,e.mobile))return{success:!1,error:"User with this email or mobile already exists (duplicate)"};if(a>0){let s=2e3*Math.pow(2,a);console.log("⏳ Retrying user creation for ".concat(e.email," after ").concat(s,"ms delay (attempt ").concat(a+1,"/").concat(4,")")),await new Promise(e=>setTimeout(e,s))}let t=(await (0,c.eJ)(u.auth,e.email,e.password)).user,r=e.referralCode;if(r){let e=(0,d.P)((0,d.collection)(u.db,m.COLLECTIONS.users),(0,d._M)(m.Yr.referralCode,"==",r));if(!(await (0,d.getDocs)(e)).empty)throw Error("Referral code ".concat(r," already exists. Please use a unique referral code."))}else r=await (0,m.x4)();let l=null;if(e.quickTranslationAdvantage&&e.quickTranslationAdvantageDays){let a=new Date;l=d.Dc.fromDate(new Date(a.getTime()+24*e.quickTranslationAdvantageDays*36e5))}let n={[m.Yr.name]:e.name.trim(),[m.Yr.email]:e.email.toLowerCase(),[m.Yr.mobile]:e.mobile,[m.Yr.referralCode]:r,[m.Yr.referredBy]:e.referredBy||"",[m.Yr.referralBonusCredited]:!1,[m.Yr.plan]:e.plan||"Trial",[m.Yr.planExpiry]:null,[m.Yr.activeDays]:e.activeDays||1,[m.Yr.joinedDate]:e.joinedDate?new Date(e.joinedDate):d.Dc.now(),[m.Yr.wallet]:e.wallet||0,[m.Yr.totalTranslations]:e.totalTranslations||0,[m.Yr.todayTranslations]:0,[m.Yr.lastTranslationDate]:null,status:"active",[m.Yr.quickTranslationAdvantage]:e.quickTranslationAdvantage||!1,[m.Yr.quickTranslationAdvantageExpiry]:l,[m.Yr.quickTranslationAdvantageDays]:e.quickTranslationAdvantageDays||0,[m.Yr.quickTranslationAdvantageSeconds]:e.quickTranslationAdvantageSeconds||30,[m.Yr.quickTranslationAdvantageGrantedBy]:e.quickTranslationAdvantageGrantedBy||"",[m.Yr.quickTranslationAdvantageGrantedAt]:e.quickTranslationAdvantage?d.Dc.now():null};return await (0,d.BN)((0,d.H9)(u.db,m.COLLECTIONS.users,t.uid),n),{success:!0}}catch(t){if(console.error("Error creating user ".concat(e.email,":"),t),"auth/too-many-requests"===t.code&&a<3)return console.log("\uD83D\uDD04 Rate limited for ".concat(e.email,", retrying... (attempt ").concat(a+1,"/").concat(3,")")),x(e,a+1);let s="Unknown error occurred";return"auth/email-already-in-use"===t.code?s="Email address is already in use (duplicate)":"auth/invalid-email"===t.code?s="Invalid email address":"auth/weak-password"===t.code?s="Password is too weak":"auth/too-many-requests"===t.code?s="Rate limited by Firebase. Please wait a few minutes and try again. (Retried ".concat(a," times)"):t.message&&(s=t.message),{success:!1,error:s}}}async function g(e){let a={success:0,failed:0,errors:[],duplicates:0};try{let r=(await e.text()).split("\n").filter(e=>e.trim());if(r.length<2)throw Error("CSV file must have at least a header row and one data row");let l=r[0],n=l.includes("	")?"	":",",i=l.split(n).map(e=>e.trim().replace(/"/g,"")),o=[];for(let e=1;e<r.length;e++){let a=r[e].split(n).map(e=>e.trim().replace(/"/g,"")),s={};i.forEach((e,t)=>{s[e]=a[t]||""}),s.activeDays&&(s.activeDays=parseInt(s.activeDays)||0),s.wallet&&(s.wallet=parseFloat(s.wallet)||0),s.totalTranslations&&(s.totalTranslations=parseInt(s.totalTranslations)||0),s.quickTranslationAdvantage&&(s.quickTranslationAdvantage="true"===s.quickTranslationAdvantage.toLowerCase()),s.quickTranslationAdvantageDays&&(s.quickTranslationAdvantageDays=parseInt(s.quickTranslationAdvantageDays)||0),s.quickTranslationAdvantageSeconds&&(s.quickTranslationAdvantageSeconds=parseInt(s.quickTranslationAdvantageSeconds)||30),o.push(s)}console.log("\uD83D\uDE80 Starting bulk user creation: ".concat(o.length," users"));for(let e=0;e<o.length;e++){let r=o[e],l="Processing user ".concat(e+1," of ").concat(o.length,": ").concat(r.name||r.email),n=Math.round((e+1)/o.length*100);console.log("\uD83D\uDCCA ".concat(n,"% - ").concat(l)),window.updateUploadProgress&&window.updateUploadProgress("".concat(n,"% - ").concat(l));try{let l=await x(r);if(l.success)a.success++,console.log("✅ Created user: ".concat(r.email));else{var s,t;a.failed++,(null==(s=l.error)?void 0:s.includes("already exists"))||(null==(t=l.error)?void 0:t.includes("duplicate"))?(a.duplicates++,console.log("⚠️ Skipped duplicate: ".concat(r.email))):console.log("❌ Failed to create: ".concat(r.email," - ").concat(l.error)),a.errors.push("Row ".concat(e+2,": ").concat(l.error))}}catch(s){a.failed++,console.log("❌ Error creating: ".concat(r.email," - ").concat(s.message)),a.errors.push("Row ".concat(e+2,": ").concat(s.message||"Unknown error"))}e%10==0?(await new Promise(e=>setTimeout(e,3e3)),console.log("⏳ Taking a 3-second break after ".concat(e+1," users to avoid rate limiting..."))):e%5==0?(await new Promise(e=>setTimeout(e,1500)),console.log("⏳ Taking a 1.5-second break after ".concat(e+1," users..."))):await new Promise(e=>setTimeout(e,800))}return a}catch(e){throw console.error("Error uploading users from CSV:",e),Error("Failed to process CSV file: ".concat(e.message))}}async function f(e){let a={success:0,failed:0,errors:[],duplicates:0};try{let r=await e.text(),l=JSON.parse(r);if(!Array.isArray(l))throw Error("JSON file must contain an array of user objects");for(let e=0;e<l.length;e++){let r=l[e];try{let l=await x(r);if(l.success)a.success++;else{var s,t;a.failed++,((null==(s=l.error)?void 0:s.includes("already exists"))||(null==(t=l.error)?void 0:t.includes("duplicate")))&&a.duplicates++,a.errors.push("User ".concat(e+1," (").concat(r.email,"): ").concat(l.error))}}catch(s){a.failed++,a.errors.push("User ".concat(e+1," (").concat(r.email,"): ").concat(s.message||"Unknown error"))}e%10==0?(await new Promise(e=>setTimeout(e,3e3)),console.log("⏳ Taking a 3-second break after ".concat(e+1," users to avoid rate limiting..."))):e%5==0?(await new Promise(e=>setTimeout(e,1500)),console.log("⏳ Taking a 1.5-second break after ".concat(e+1," users..."))):await new Promise(e=>setTimeout(e,800))}return a}catch(e){throw console.error("Error uploading users from JSON:",e),Error("Failed to process JSON file: ".concat(e.message))}}var v=s(4752),w=s.n(v);function j(){let{user:e,loading:a,isAdmin:s}=(0,o.wC)();(0,i.useRouter)();let[l,c]=(0,r.useState)(!1),[d,u]=(0,r.useState)(null),[m,p]=(0,r.useState)(null),[x,v]=(0,r.useState)("csv"),[j,b]=(0,r.useState)([]),[y,N]=(0,r.useState)(!1),[T,k]=(0,r.useState)("");(0,r.useEffect)(()=>{let e=e=>{if(l)return e.preventDefault(),"Upload is in progress. Are you sure you want to leave?"};return window.addEventListener("beforeunload",e),()=>{window.removeEventListener("beforeunload",e)}},[l]);let A=async()=>{if(m)try{c(!0);let e=await m.text(),a=[];if("csv"===x){let s=e.split("\n").filter(e=>e.trim());if(s.length<2)throw Error("CSV file must have at least a header row and one data row");let t=s[0],r=t.includes("	")?"	":",",l=t.split(r).map(e=>e.trim().replace(/"/g,""));a=s.slice(1).map(e=>{let a=e.split(r).map(e=>e.trim().replace(/"/g,"")),s={};return l.forEach((e,t)=>{s[e]=a[t]||""}),s})}else if(a=JSON.parse(e),!Array.isArray(a))throw Error("JSON file must contain an array of user objects");let s=a.slice(0,5),t=[];s.forEach((e,a)=>{let s=h(e);s.length>0&&t.push("Row ".concat(a+1,": ").concat(s.join(", ")))}),b(s),N(!0),t.length>0&&w().fire({icon:"warning",title:"Validation Issues Found",html:'<div class="text-left"><p>Issues found in preview data:</p><ul>'.concat(t.map(e=>"<li>".concat(e,"</li>")).join(""),"</ul></div>"),confirmButtonText:"Continue Anyway",showCancelButton:!0,cancelButtonText:"Fix Data First"})}catch(e){console.error("Error previewing file:",e),w().fire({icon:"error",title:"Preview Failed",text:e.message||"Failed to preview file. Please check the format."})}finally{c(!1)}},q=async()=>{if(m&&(await w().fire({icon:"question",title:"Confirm User Upload",html:'\n        <div class="text-left">\n          <p><strong>Are you sure you want to upload users from this file?</strong></p>\n          <br>\n          <p>This will:</p>\n          <ul>\n            <li>Create Firebase Authentication accounts</li>\n            <li>Create user documents in Firestore</li>\n            <li>Use provided referral codes or assign new sequential ones</li>\n            <li>Set up wallet and transaction data</li>\n            <li>Apply quick translation advantage if specified</li>\n            <li>Validate referral code uniqueness</li>\n          </ul>\n          <br>\n          <p class="text-red-600"><strong>Warning:</strong> This action cannot be undone!</p>\n        </div>\n      ',showCancelButton:!0,confirmButtonText:"Yes, Upload Users",cancelButtonText:"Cancel",confirmButtonColor:"#dc2626"})).isConfirmed)try{let e;c(!0),u(null),k("Starting upload..."),w().fire({title:"Uploading Users",html:'\n          <div class="text-center">\n            <div class="spinner mx-auto mb-4"></div>\n            <p id="upload-progress">Starting upload...</p>\n            <p class="text-sm text-gray-600 mt-2">Please do not close this page or navigate away.</p>\n          </div>\n        ',allowOutsideClick:!1,allowEscapeKey:!1,showConfirmButton:!1,didOpen:()=>{window.updateUploadProgress=e=>{let a=document.getElementById("upload-progress");a&&(a.textContent=e)}}}),e="csv"===x?await g(m):await f(m),w().close(),u(e),e.success>0?w().fire({icon:e.failed>0?"warning":"success",title:"Upload Complete",html:'\n            <div class="text-left">\n              <p><strong>Upload Summary:</strong></p>\n              <ul>\n                <li class="text-green-600">✓ Successfully created: '.concat(e.success," users</li>\n                ").concat(e.duplicates>0?'<li class="text-yellow-600">⚠ Skipped duplicates: '.concat(e.duplicates," users</li>"):"","\n                ").concat(e.failed>0?'<li class="text-red-600">✗ Failed: '.concat(e.failed," users</li>"):"","\n              </ul>\n              ").concat(e.errors.length>0?"<br><p><strong>Errors:</strong></p><ul>".concat(e.errors.slice(0,5).map(e=>'<li class="text-red-600">'.concat(e,"</li>")).join(""),"</ul>"):"","\n            </div>\n          "),timer:e.failed>0?void 0:5e3,showConfirmButton:e.failed>0}):w().fire({icon:"error",title:"Upload Failed",text:"No users were successfully created. Please check your data and try again."})}catch(e){console.error("Error uploading users:",e),w().fire({icon:"error",title:"Upload Failed",text:e.message||"Failed to upload users. Please try again."})}finally{c(!1)}};return a?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"spinner"})}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 p-4",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Upload Users"}),(0,t.jsx)("p",{className:"text-white/80",children:"Transfer existing users from old platform"})]}),l?(0,t.jsxs)("button",{disabled:!0,className:"btn-secondary opacity-50 cursor-not-allowed",title:"Upload in progress - navigation disabled",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Users"]}):(0,t.jsxs)(n(),{href:"/admin/users",className:"btn-secondary",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Users"]})]}),(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload User Data"]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Upload Format"}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"radio",value:"csv",checked:"csv"===x,onChange:e=>v(e.target.value),className:"mr-2"}),(0,t.jsx)("span",{className:"text-white",children:"CSV/TSV File"})]}),(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"radio",value:"json",checked:"json"===x,onChange:e=>v(e.target.value),className:"mr-2"}),(0,t.jsx)("span",{className:"text-white",children:"JSON File"})]})]})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Sample Files"}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)("button",{onClick:()=>{let e=new Blob(["name,email,mobile,password,plan,activeDays,wallet,totalTranslations,referredBy,referralCode,quickTranslationAdvantage,quickTranslationAdvantageDays,quickTranslationAdvantageSeconds,quickTranslationAdvantageGrantedBy\nJohn Doe,<EMAIL>,9876543210,password123,Junior,30,2000,100,TN0001,TN1001,true,7,10,<EMAIL>\nJane Smith,<EMAIL>,9876543211,password456,Senior,25,5000,150,TN0002,TN1002,false,,,,,\nMike Johnson,<EMAIL>,9876543212,password789,Trial,30,1000,50,,TN1003,true,14,30,<EMAIL>\nSarah Wilson,<EMAIL>,9876543213,password321,Expert,20,8000,200,TN0001,TN1004,true,3,1,<EMAIL>"],{type:"text/csv"}),a=URL.createObjectURL(e),s=document.createElement("a");s.href=a,s.download="sample-users.csv",s.click(),URL.revokeObjectURL(a)},className:"btn-secondary text-sm",children:[(0,t.jsx)("i",{className:"fas fa-download mr-2"}),"Download Sample CSV"]}),(0,t.jsxs)("button",{onClick:()=>{let e=new Blob([JSON.stringify([{name:"John Doe",email:"<EMAIL>",mobile:"9876543210",password:"password123",plan:"Junior",activeDays:30,wallet:2e3,totalTranslations:100,referredBy:"TN0001",referralCode:"TN1001",quickTranslationAdvantage:!0,quickTranslationAdvantageDays:7,quickTranslationAdvantageSeconds:10,quickTranslationAdvantageGrantedBy:"<EMAIL>"},{name:"Jane Smith",email:"<EMAIL>",mobile:"9876543211",password:"password456",plan:"Senior",activeDays:25,wallet:5e3,totalTranslations:150,referredBy:"TN0002",referralCode:"TN1002",quickTranslationAdvantage:!1},{name:"Mike Johnson",email:"<EMAIL>",mobile:"9876543212",password:"password789",plan:"Trial",activeDays:30,wallet:1e3,totalTranslations:50,referralCode:"TN1003",quickTranslationAdvantage:!0,quickTranslationAdvantageDays:14,quickTranslationAdvantageSeconds:30,quickTranslationAdvantageGrantedBy:"<EMAIL>"},{name:"Sarah Wilson",email:"<EMAIL>",mobile:"9876543213",password:"password321",plan:"Expert",activeDays:20,wallet:8e3,totalTranslations:200,referredBy:"TN0001",referralCode:"TN1004",quickTranslationAdvantage:!0,quickTranslationAdvantageDays:3,quickTranslationAdvantageSeconds:1,quickTranslationAdvantageGrantedBy:"<EMAIL>"}],null,2)],{type:"application/json"}),a=URL.createObjectURL(e),s=document.createElement("a");s.href=a,s.download="sample-users.json",s.click(),URL.revokeObjectURL(a)},className:"btn-secondary text-sm",children:[(0,t.jsx)("i",{className:"fas fa-download mr-2"}),"Download Sample JSON"]})]})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Select File"}),(0,t.jsx)("input",{type:"file",accept:"csv"===x?".csv,.tsv,.txt":".json",onChange:e=>{var a;let s=null==(a=e.target.files)?void 0:a[0];s&&(p(s),b([]),N(!1),u(null))},className:"form-input"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsx)("button",{onClick:A,disabled:!m||l,className:"btn-secondary",children:l?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Processing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-eye mr-2"}),"Preview Data"]})}),(0,t.jsx)("button",{onClick:q,disabled:!m||l||!y,className:"btn-primary",children:l?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Uploading..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload Users"]})})]}),l&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("div",{className:"bg-yellow-500/20 border border-yellow-400/30 rounded-lg p-3",children:(0,t.jsxs)("div",{className:"flex items-center text-yellow-300",children:[(0,t.jsx)("i",{className:"fas fa-exclamation-triangle mr-2"}),(0,t.jsxs)("span",{className:"text-sm",children:[(0,t.jsx)("strong",{children:"Upload in progress!"})," Please do not close this page or navigate away until the upload is complete."]})]})}),(0,t.jsx)("div",{className:"bg-blue-500/20 border border-blue-400/30 rounded-lg p-3",children:(0,t.jsxs)("div",{className:"text-blue-300 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)("i",{className:"fas fa-info-circle mr-2"}),(0,t.jsx)("strong",{children:"Rate Limiting Protection Active"})]}),(0,t.jsxs)("ul",{className:"text-blue-200 text-xs space-y-1 ml-4",children:[(0,t.jsx)("li",{children:"• Automatic delays between users to prevent Firebase rate limiting"}),(0,t.jsx)("li",{children:"• 0.8s delay between each user, 1.5s every 5 users, 3s every 10 users"}),(0,t.jsx)("li",{children:"• Failed requests are automatically retried up to 3 times"}),(0,t.jsx)("li",{children:"• Large uploads may take several minutes - this is normal"})]})]})})]})]})]}),y&&j.length>0&&(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-table mr-2"}),"Data Preview (First 5 Records)"]}),(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full text-white",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b border-white/20",children:[(0,t.jsx)("th",{className:"text-left p-2",children:"Name"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Email"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Mobile"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Plan"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Active Days"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Wallet"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Total Translations"})]})}),(0,t.jsx)("tbody",{children:j.map((e,a)=>(0,t.jsxs)("tr",{className:"border-b border-white/10",children:[(0,t.jsx)("td",{className:"p-2",children:e.name||"N/A"}),(0,t.jsx)("td",{className:"p-2",children:e.email||"N/A"}),(0,t.jsx)("td",{className:"p-2",children:e.mobile||"N/A"}),(0,t.jsx)("td",{className:"p-2",children:e.plan||"Trial"}),(0,t.jsx)("td",{className:"p-2",children:e.activeDays||0}),(0,t.jsxs)("td",{className:"p-2",children:["₹",e.wallet||0]}),(0,t.jsx)("td",{className:"p-2",children:e.totalTranslations||0})]},a))})]})})]}),d&&(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-chart-bar mr-2"}),"Upload Results"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,t.jsxs)("div",{className:"bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"text-green-400 text-2xl font-bold",children:d.success}),(0,t.jsx)("div",{className:"text-green-300 text-sm",children:"Successfully Created"})]}),d.duplicates>0&&(0,t.jsxs)("div",{className:"bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"text-yellow-400 text-2xl font-bold",children:d.duplicates}),(0,t.jsx)("div",{className:"text-yellow-300 text-sm",children:"Skipped (Duplicates)"})]}),d.failed>0&&(0,t.jsxs)("div",{className:"bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"text-red-400 text-2xl font-bold",children:d.failed}),(0,t.jsx)("div",{className:"text-red-300 text-sm",children:"Failed"})]})]}),d.errors.length>0&&(0,t.jsxs)("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-red-400 font-bold mb-2",children:"Errors:"}),(0,t.jsxs)("ul",{className:"text-red-300 text-sm space-y-1",children:[d.errors.slice(0,10).map((e,a)=>(0,t.jsxs)("li",{children:["• ",e]},a)),d.errors.length>10&&(0,t.jsxs)("li",{className:"text-red-400",children:["... and ",d.errors.length-10," more errors"]})]})]})]}),(0,t.jsxs)("div",{className:"glass-card p-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Upload Instructions"]}),(0,t.jsxs)("div",{className:"text-white/80 space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-bold text-white mb-2",children:"Required Fields:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"name:"})," User's full name"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"email:"})," Valid email address (must be unique)"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"mobile:"})," 10-digit mobile number (must be unique)"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"password:"})," Password for the user account (min 6 characters)"]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-bold text-white mb-2",children:"Optional Fields:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"plan:"})," User's plan (Trial, Junior, Senior, Expert) - defaults to Trial"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"activeDays:"})," Number of active days remaining - defaults to 1"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"wallet:"})," Wallet balance in rupees - defaults to 0"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"totalTranslations:"})," Total translations completed - defaults to 0"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"referredBy:"})," Referral code of the person who referred this user"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"referralCode:"})," User's own referral code (TN0001, TN0002, etc.) - auto-generated if not provided"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"quickTranslationAdvantage:"})," Whether user has copy-paste permission (true/false) - defaults to false"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"quickTranslationAdvantageDays:"})," Number of days for copy-paste permission (1-365) - only if quickTranslationAdvantage is true"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"quickTranslationAdvantageSeconds:"})," Translation duration in seconds during advantage (1-420) - defaults to 30"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"quickTranslationAdvantageGrantedBy:"})," Admin who granted the advantage - optional"]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-bold text-white mb-2",children:"Important Notes:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,t.jsx)("li",{children:"Each user will get a sequential referral code (TN0001, TN0002, etc.)"}),(0,t.jsx)("li",{children:"Firebase Authentication accounts will be created automatically"}),(0,t.jsx)("li",{children:"Duplicate emails or mobile numbers will be skipped"}),(0,t.jsx)("li",{children:"Users can login immediately with their email and password"}),(0,t.jsx)("li",{children:"All wallet balances and translation counts will be preserved"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-bold text-white mb-2",children:"Rate Limiting & Performance:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Recommended:"})," Upload 20-50 users per batch for optimal performance"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Large uploads:"})," 100+ users may take 5-10 minutes due to rate limiting"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Auto-retry:"})," Failed requests are automatically retried up to 3 times"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"If rate limited:"})," Wait 5-10 minutes before retrying"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Best time:"})," Upload during off-peak hours for faster processing"]})]})]})]})]})]})})}},6993:(e,a,s)=>{Promise.resolve().then(s.bind(s,1187))}},e=>{var a=a=>e(e.s=a);e.O(0,[2992,7416,8320,8818,6874,6681,3592,8441,1684,7358],()=>a(6993)),_N_E=e.O()}]);