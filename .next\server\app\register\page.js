(()=>{var e={};e.id=2454,e.ids=[1705,2454],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8548:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var s=t(60687),a=t(43210),o=t(85814),i=t.n(o),l=t(30474),n=t(63385),c=t(75535),d=t(33784),u=t(87979),p=t(3582),m=t(51705),y=t(77567);function h(){let{user:e,loading:r}=(0,u.hD)(),[t,o]=(0,a.useState)({name:"",email:"",mobile:"",password:"",confirmPassword:"",referralCode:""}),[h,g]=(0,a.useState)(!1),[f,x]=(0,a.useState)(!1),[v,w]=(0,a.useState)(!1),D=e=>{let{name:r,value:t}=e.target;o(e=>({...e,[r]:t}))},b=()=>{let{name:e,email:r,mobile:s,password:a,confirmPassword:o}=t;if(!e||!r||!s||!a||!o)throw Error("Please fill in all required fields");if(e.length<2)throw Error("Name must be at least 2 characters long");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r))throw Error("Please enter a valid email address");if(!/^[6-9]\d{9}$/.test(s))throw Error("Please enter a valid 10-digit mobile number");if(a.length<6)throw Error("Password must be at least 6 characters long");if(a!==o)throw Error("Passwords do not match")},A=async e=>{e.preventDefault();try{b(),g(!0),console.log("Creating user with email and password...");let e=(await (0,n.eJ)(d.auth,t.email,t.password)).user;console.log("Firebase Auth user created successfully:",e.uid),console.log("Generating referral code...");let r=Date.now().toString().slice(-4),s=Math.random().toString(36).substring(2,4).toUpperCase(),a=`TN${r}${s}`;console.log("Generated referral code:",a);let o={[p.Yr.name]:t.name.trim(),[p.Yr.email]:t.email.toLowerCase(),[p.Yr.mobile]:t.mobile,[p.Yr.referralCode]:a,[p.Yr.referredBy]:t.referralCode||"",[p.Yr.referralBonusCredited]:!1,[p.Yr.plan]:"Trial",[p.Yr.planExpiry]:null,[p.Yr.activeDays]:1,[p.Yr.joinedDate]:c.Dc.now(),[p.Yr.wallet]:0,[p.Yr.totalTranslations]:0,[p.Yr.todayTranslations]:0,[p.Yr.lastTranslationDate]:null,status:"active"};console.log("Creating user document with data:",o),console.log("User UID:",e.uid),console.log("Collection:",p.COLLECTIONS.users),console.log("Document path:",`${p.COLLECTIONS.users}/${e.uid}`),console.log("Creating user document in Firestore...");let i=(0,c.H9)(d.db,p.COLLECTIONS.users,e.uid);console.log("Document reference created:",i.path),console.log("About to create document with data:",JSON.stringify(o,null,2));try{console.log("Attempting to create document..."),console.log("User UID:",e.uid),console.log("Document path:",i.path),console.log("Auth user email:",e.email),console.log("Auth user verified:",e.emailVerified),await (0,c.BN)(i,o),console.log("✅ User document created successfully"),await (0,m.S3)(e.uid),console.log("✅ Active days initialized for new user");let r=await (0,c.x7)(i);if(r.exists())console.log("✅ Document verification successful:",r.data()),console.log("✅ Registration completed successfully - both Auth and Firestore created");else throw console.error("❌ Document was not created properly"),Error("User document was not created properly")}catch(e){throw console.error("❌ Firestore setDoc failed:",e),console.error("❌ Firestore error code:",e.code),console.error("❌ Firestore error message:",e.message),console.error("❌ Full error object:",JSON.stringify(e,null,2)),console.error("❌ CRITICAL: Firebase Auth succeeded but Firestore document creation failed"),console.error("❌ User account exists but profile is incomplete"),Error(`Failed to create user profile: ${e.message}. Your account was created but profile setup failed. Please contact support.`)}console.log("User registered successfully. Referral bonus will be processed when upgraded to paid plan."),y.A.fire({icon:"success",title:"Registration Successful!",text:"Your account and profile have been created successfully. Welcome to Instra Global!",timer:2e3,showConfirmButton:!1}).then(()=>{console.log("✅ Complete registration successful - redirecting to dashboard..."),window.location.href="/dashboard"})}catch(r){console.error("Registration error:",r),console.error("Error code:",r.code),console.error("Error message:",r.message),console.error("Full error object:",JSON.stringify(r,null,2));let e="An error occurred during registration";if(r.message.includes("fill in all"))e=r.message;else if(r.message.includes("Name must be"))e=r.message;else if(r.message.includes("valid email"))e=r.message;else if(r.message.includes("valid 10-digit"))e=r.message;else if(r.message.includes("Password must be"))e=r.message;else if(r.message.includes("Passwords do not match"))e=r.message;else if(r.message.includes("email address is already registered"))e=r.message;else if(r.message.includes("mobile number is already registered"))e=r.message;else switch(r.code){case"auth/email-already-in-use":e="An account with this email already exists";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/weak-password":e="Password is too weak";break;default:e=r.message||"Registration failed"}y.A.fire({icon:"error",title:"Registration Failed",text:e})}finally{g(!1)}};return r?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"spinner"})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4 py-8 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,s.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,s.jsx)(l.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:50,height:50,className:"mr-3"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-white",children:"Instra Global"})]}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Create Account"}),(0,s.jsx)("p",{className:"text-white/80",children:"Join Instra Global and start earning today"})]}),(0,s.jsxs)("form",{onSubmit:A,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-white font-medium mb-2",children:"Full Name *"}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",value:t.name,onChange:D,className:"form-input",placeholder:"Enter your full name",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address *"}),(0,s.jsx)("input",{type:"email",id:"email",name:"email",value:t.email,onChange:D,className:"form-input",placeholder:"Enter your email",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"mobile",className:"block text-white font-medium mb-2",children:"Mobile Number *"}),(0,s.jsx)("input",{type:"tel",id:"mobile",name:"mobile",value:t.mobile,onChange:D,className:"form-input",placeholder:"Enter 10-digit mobile number",maxLength:10,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:"Password *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:f?"text":"password",id:"password",name:"password",value:t.password,onChange:D,className:"form-input pr-12",placeholder:"Enter password (min 6 characters)",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>x(!f),className:"password-toggle-btn","aria-label":f?"Hide password":"Show password",children:(0,s.jsx)("i",{className:`fas ${f?"fa-eye-slash":"fa-eye"}`})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-white font-medium mb-2",children:"Confirm Password *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:v?"text":"password",id:"confirmPassword",name:"confirmPassword",value:t.confirmPassword,onChange:D,className:"form-input pr-12",placeholder:"Confirm your password",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>w(!v),className:"password-toggle-btn","aria-label":v?"Hide confirm password":"Show confirm password",children:(0,s.jsx)("i",{className:`fas ${v?"fa-eye-slash":"fa-eye"}`})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"referralCode",className:"block text-white font-medium mb-2",children:"Referral Code (Optional)"}),(0,s.jsx)("input",{type:"text",id:"referralCode",name:"referralCode",value:t.referralCode,onChange:D,className:"form-input",placeholder:"Enter referral code if you have one"})]}),(0,s.jsx)("button",{type:"submit",disabled:h,className:"w-full btn-primary flex items-center justify-center mt-6",children:h?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Creating Account..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-user-plus mr-2"}),"Create Account"]})})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("div",{className:"text-white/60",children:["Already have an account?"," ",(0,s.jsx)(i(),{href:"/login",className:"text-white font-semibold hover:underline",children:"Sign in here"})]})}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsxs)(i(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14008:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=t(65239),a=t(48088),o=t(88170),i=t.n(o),l=t(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(r,n);let c={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94530)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\register\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\register\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},35397:(e,r,t)=>{Promise.resolve().then(t.bind(t,8548))},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41477:(e,r,t)=>{Promise.resolve().then(t.bind(t,94530))},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},51705:(e,r,t)=>{"use strict";t.d(r,{ActiveDaysService:()=>l,S3:()=>p,i7:()=>n,isUserPlanExpired:()=>m,mH:()=>d,nd:()=>u,updateUserActiveDays:()=>c});var s=t(33784),a=t(75535);let o={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},i={users:"users"};class l{static async calculateActiveDays(e){try{let r,t=await (0,a.x7)((0,a.H9)(s.db,i.users,e));if(!t.exists())return console.error(`User ${e} not found`),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let o=t.data(),n=o.joinedDate?.toDate()||new Date,c=o.lastActiveDayUpdate?.toDate(),d=o.activeDays||0,u=o.plan||"Trial",p=new Date,m=p.toDateString(),y=c?c.toDateString():null;if(console.log(`📅 Calculating active days for user ${e}:`),console.log(`   - Joined: ${n.toDateString()}`),console.log(`   - Current active days: ${d}`),console.log(`   - Last update: ${y||"Never"}`),console.log(`   - Today: ${m}`),console.log(`   - Plan: ${u}`),console.log(`   - Is new day: ${y!==m}`),y===m)return console.log(`✅ Already updated today for user ${e}`),{activeDays:d,shouldUpdate:!1,isNewDay:!1};if("Admin"===u)return console.log(`⏭️ Skipping active days increment for admin user ${e}`),await l.updateLastActiveDayUpdate(e),{activeDays:d,shouldUpdate:!1,isNewDay:!0};if(await l.isUserOnLeaveToday(e))return console.log(`🏖️ User ${e} is on leave today, not incrementing active days`),await l.updateLastActiveDayUpdate(e),{activeDays:d,shouldUpdate:!1,isNewDay:!0};return r="Trial"===u?Math.floor((p.getTime()-n.getTime())/864e5)+1:d+1,console.log(`📈 New active days calculated: ${d} → ${r}`),{activeDays:r,shouldUpdate:r!==d,isNewDay:!0}}catch(r){return console.error(`Error calculating active days for user ${e}:`,r),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(e){try{let r=await l.calculateActiveDays(e);if(r.shouldUpdate){let t=(0,a.H9)(s.db,i.users,e);await (0,a.mZ)(t,{[o.activeDays]:r.activeDays,[o.lastActiveDayUpdate]:a.Dc.now()}),console.log(`✅ Updated active days for user ${e}: ${r.activeDays}`)}else r.isNewDay&&await l.updateLastActiveDayUpdate(e);return r.activeDays}catch(r){throw console.error(`Error updating active days for user ${e}:`,r),r}}static async updateLastActiveDayUpdate(e){try{let r=(0,a.H9)(s.db,i.users,e);await (0,a.mZ)(r,{[o.lastActiveDayUpdate]:a.Dc.now()})}catch(r){console.error(`Error updating last active day timestamp for user ${e}:`,r)}}static async isUserOnLeaveToday(e){try{let{isUserOnLeave:r}=await t.e(7087).then(t.bind(t,87087));return await r(e,new Date)}catch(r){return console.error(`Error checking leave status for user ${e}:`,r),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let e=await (0,a.getDocs)((0,a.collection)(s.db,i.users)),r=0,t=0,o=0;for(let s of e.docs)try{r++;let e=await l.calculateActiveDays(s.id);(e.shouldUpdate||e.isNewDay)&&(await l.updateUserActiveDays(s.id),e.shouldUpdate&&t++)}catch(e){o++,console.error(`Error processing active days for user ${s.id}:`,e)}return console.log(`✅ Daily active days processing complete:`),console.log(`   - Processed: ${r} users`),console.log(`   - Updated: ${t} users`),console.log(`   - Errors: ${o} users`),{processed:r,updated:t,errors:o}}catch(e){throw console.error("Error in daily active days processing:",e),e}}static async getUserActiveDays(e){try{let r=await (0,a.x7)((0,a.H9)(s.db,i.users,e));if(!r.exists())return 0;return r.data().activeDays||0}catch(r){return console.error(`Error getting active days for user ${e}:`,r),0}}static async initializeActiveDaysForNewUser(e){try{let r=(0,a.H9)(s.db,i.users,e);await (0,a.mZ)(r,{[o.activeDays]:1,[o.lastActiveDayUpdate]:a.Dc.now()}),console.log(`✅ Initialized active days for new user ${e}: Day 1`)}catch(r){throw console.error(`Error initializing active days for user ${e}:`,r),r}}static async getActiveDaysDisplay(e){try{let r,t=await (0,a.x7)((0,a.H9)(s.db,i.users,e));if(!t.exists())return{current:0,total:2,displayText:"0/2"};let o=t.data(),l=o.plan||"Trial",n=o.activeDays||0;return r="Trial"===l?2:30,{current:n,total:r,displayText:`${n}/${r}`}}catch(r){return console.error(`Error getting active days display for user ${e}:`,r),{current:0,total:2,displayText:"0/2"}}}static async isUserPlanExpired(e){try{let r=await (0,a.x7)((0,a.H9)(s.db,i.users,e));if(!r.exists())return{expired:!0,reason:"User not found"};let t=r.data(),o=t.plan||"Trial",l=t.activeDays||0,n=t.planExpiry;if(console.log(`📅 Checking plan expiry for user ${e}:`,{plan:o,activeDays:l,hasPlanExpiry:!!n,planExpiryDate:n?n.toDate():null}),"Admin"===o){let r={expired:!1,activeDays:l};return console.log(`📅 Plan expiry result for admin user ${e}:`,r),r}if("Trial"===o){let r=Math.max(0,2-l),t={expired:r<=0,reason:r<=0?"Trial period expired":void 0,daysLeft:r,activeDays:l};return console.log(`📅 Plan expiry result for trial user ${e}:`,t),t}if(n){let r=new Date,t=n.toDate(),s=r>t,a=s?0:Math.ceil((t.getTime()-r.getTime())/864e5),o={expired:s,reason:s?"Plan subscription expired":void 0,daysLeft:a,activeDays:l};return console.log(`📅 Plan expiry result for user ${e} (using planExpiry field):`,o),o}let c=Math.max(0,30-l),d=l>30,u={expired:d,reason:d?`Plan expired - You have used ${l} days out of 30 allowed days`:void 0,daysLeft:c,activeDays:l};return console.log(`📅 Plan expiry result for user ${e}:`,u),u}catch(r){return console.error(`Error checking plan expiry for user ${e}:`,r),{expired:!0,reason:"Error checking plan status"}}}static async setPlanExpiryForUser(e,r=30){try{let t=(0,a.H9)(s.db,i.users,e),o=await (0,a.x7)(t);if(!o.exists())return console.error(`User ${e} not found`),!1;let l=o.data(),n=l.plan||"Trial";if("Trial"===n||"Admin"===n)return console.log(`Skipping plan expiry setup for ${n} user: ${e}`),!1;if(l.planExpiry)return console.log(`User ${e} already has plan expiry set: ${l.planExpiry.toDate()}`),!1;let c=l.joinedDate?.toDate()||new Date,d=new Date(c);return d.setDate(d.getDate()+r),await (0,a.mZ)(t,{planExpiry:a.Dc.fromDate(d),planExpirySetDate:a.Dc.now()}),console.log(`✅ Set plan expiry for user ${e}: ${d}`),!0}catch(r){return console.error(`Error setting plan expiry for user ${e}:`,r),!1}}static async forceUpdateActiveDays(e,r,t){try{let l=(0,a.H9)(s.db,i.users,e);await (0,a.mZ)(l,{[o.activeDays]:r,[o.lastActiveDayUpdate]:a.Dc.now()}),console.log(`🔧 Admin ${t} force updated active days for user ${e}: ${r}`)}catch(r){throw console.error(`Error force updating active days for user ${e}:`,r),r}}static async getActiveDaysStatistics(){try{let e=await (0,a.getDocs)((0,a.collection)(s.db,i.users)),r=0,t=0,o=0,l=0,n=0,c=0,d=new Date().toDateString();for(let s of e.docs){let e=s.data(),a=e.plan||"Trial",i=e.activeDays||0,u=e.lastActiveDayUpdate?.toDate();r++,n+=i,"Trial"===a?t++:"Admin"===a?l++:o++,u&&u.toDateString()===d&&c++}return{totalUsers:r,trialUsers:t,paidUsers:o,adminUsers:l,averageActiveDays:r>0?Math.round(n/r*100)/100:0,usersUpdatedToday:c}}catch(e){throw console.error("Error getting active days statistics:",e),e}}}let n=l.calculateActiveDays,c=l.updateUserActiveDays,d=l.processAllUsersActiveDays,u=l.getUserActiveDays,p=l.initializeActiveDaysForNewUser;l.getActiveDaysDisplay;let m=l.isUserPlanExpired;l.forceUpdateActiveDays,l.getActiveDaysStatistics},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94530:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\register\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4573,6803,3582],()=>t(14008));module.exports=s})();