(()=>{var e={};e.id=2922,e.ids=[2922,7878],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15426:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-sample-upload\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-sample-upload\\page.tsx","default")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27878:(e,s,t)=>{"use strict";t.d(s,{Mk:()=>p,checkCopyPastePermission:()=>c,grantCopyPastePermission:()=>l,i7:()=>u,removeCopyPastePermission:()=>d});var r=t(33784),a=t(75535);let i={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},o={users:"users"};class n{static async checkCopyPastePermission(e){try{let s=await (0,a.x7)((0,a.H9)(r.db,o.users,e));if(!s.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let t=s.data()[i.quickTranslationAdvantageExpiry];if(!t)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let n=t.toDate(),c=new Date,l=n>c,d=l?Math.ceil((n.getTime()-c.getTime())/864e5):0;return{hasPermission:l,daysRemaining:d,expiryDate:n}}catch(s){return console.error(`Error checking copy-paste permission for user ${e}:`,s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,s){try{let t=new Date;t.setDate(t.getDate()+s);let n=(0,a.H9)(r.db,o.users,e);await (0,a.mZ)(n,{[i.quickTranslationAdvantageExpiry]:a.Dc.fromDate(t),[i.lastCopyPasteReduction]:a.Dc.now()}),console.log(`✅ Granted copy-paste permission to user ${e} for ${s} days (expires: ${t.toDateString()})`)}catch(s){throw console.error(`Error granting copy-paste permission to user ${e}:`,s),s}}static async removeCopyPastePermission(e){try{let s=(0,a.H9)(r.db,o.users,e);await (0,a.mZ)(s,{[i.quickTranslationAdvantageExpiry]:null}),console.log(`✅ Removed copy-paste permission from user ${e}`)}catch(s){throw console.error(`Error removing copy-paste permission from user ${e}:`,s),s}}static async reduceCopyPasteDays(e){try{let s=await (0,a.x7)((0,a.H9)(r.db,o.users,e));if(!s.exists())return{reduced:!1,daysRemaining:0,expired:!1};let t=s.data(),n=t[i.quickTranslationAdvantageExpiry],c=t[i.lastCopyPasteReduction];if(!n)return{reduced:!1,daysRemaining:0,expired:!1};let l=new Date().toDateString();if((c?c.toDate().toDateString():null)===l){let e=n.toDate(),s=new Date,t=Math.max(0,Math.ceil((e.getTime()-s.getTime())/864e5));return{reduced:!1,daysRemaining:t,expired:0===t}}let d=n.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let p=(0,a.H9)(r.db,o.users,e);if(u<=new Date)return await (0,a.mZ)(p,{[i.quickTranslationAdvantageExpiry]:null,[i.lastCopyPasteReduction]:a.Dc.now()}),console.log(`📅 Copy-paste permission expired for user ${e}`),{reduced:!0,daysRemaining:0,expired:!0};{await (0,a.mZ)(p,{[i.quickTranslationAdvantageExpiry]:a.Dc.fromDate(u),[i.lastCopyPasteReduction]:a.Dc.now()});let s=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log(`📅 Reduced copy-paste days for user ${e}: ${s} days remaining`),{reduced:!0,daysRemaining:s,expired:!1}}}catch(s){return console.error(`Error reducing copy-paste days for user ${e}:`,s),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,a.getDocs)((0,a.collection)(r.db,o.users)),s=0,t=0,i=0,c=0;for(let r of e.docs)try{s++;let e=await n.reduceCopyPasteDays(r.id);e.reduced&&(t++,e.expired&&i++)}catch(e){c++,console.error(`Error processing copy-paste reduction for user ${r.id}:`,e)}return console.log(`✅ Daily copy-paste reduction complete:`),console.log(`   - Processed: ${s} users`),console.log(`   - Reduced: ${t} users`),console.log(`   - Expired: ${i} users`),console.log(`   - Errors: ${c} users`),{processed:s,reduced:t,expired:i,errors:c}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let s=await n.checkCopyPastePermission(e);return{hasPermission:s.hasPermission,daysRemaining:s.daysRemaining,expiryDate:s.expiryDate?s.expiryDate.toDateString():null}}catch(s){return console.error(`Error getting copy-paste status for user ${e}:`,s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let c=n.checkCopyPastePermission,l=n.grantCopyPastePermission,d=n.removeCopyPastePermission,u=n.reduceCopyPasteDays,p=n.processAllUsersCopyPasteReduction;n.getCopyPasteStatus},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34485:(e,s,t)=>{Promise.resolve().then(t.bind(t,15426))},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},35280:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var r=t(60687),a=t(43210),i=t(87979),o=t(27878),n=t(95871);function c(){let{user:e,loading:s}=(0,i.Nu)(),[c,l]=(0,a.useState)(null),[d,u]=(0,a.useState)(!1),p=async()=>{u(!0),l(null);try{console.log("\uD83E\uDDEA Testing daily reduction processes..."),console.log("1. Testing active days increment via Firebase Function...");let e=await (0,n.e5)();console.log("2. Testing copy-paste reduction...");let s=await (0,o.Mk)();l({success:!0,activeDaysResult:e,copyPasteResult:s,message:"Daily reduction test completed successfully"})}catch(e){l({success:!1,error:e.message,message:"Daily reduction test failed"})}finally{u(!1)}},h=async()=>{let e=prompt("Enter user email to check copy-paste status:");if(e){u(!0);try{let{searchUsers:s}=await Promise.all([t.e(3582),t.e(3772)]).then(t.bind(t,91391)),r=(await s(e)).find(s=>s.email?.toLowerCase()===e.toLowerCase());if(!r)return void l({success:!1,message:`User not found: ${e}`});let a=await (0,o.checkCopyPastePermission)(r.id);l({success:!0,userEmail:e,userId:r.id,copyPasteStatus:a,message:"Copy-paste status retrieved successfully"})}catch(e){l({success:!1,error:e.message,message:"Failed to check copy-paste status"})}finally{u(!1)}}};return s?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,r.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"glass-card p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Sample Upload & Daily Reduction"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-3",children:"1. Download Test CSV"}),(0,r.jsx)("p",{className:"text-white/80 mb-3",children:"Download a test CSV file with sample data including copy-paste days."}),(0,r.jsxs)("button",{onClick:()=>{let e=new Blob(["email,totalTranslations,walletBalance,activeDays,copyPasteDays\<EMAIL>,50,250,10,7\<EMAIL>,100,500,15,14\<EMAIL>,25,125,5,3\<EMAIL>,75,375,12,0"],{type:"text/csv"}),s=URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download="test-sample-upload.csv",t.click(),URL.revokeObjectURL(s)},className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),"Download Test CSV"]})]}),(0,r.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-3",children:"2. Upload Test Data"}),(0,r.jsxs)("p",{className:"text-white/80 mb-3",children:["Go to ",(0,r.jsx)("strong",{children:"Admin → Simple Upload"})," and upload the test CSV file."]}),(0,r.jsxs)("a",{href:"/admin/simple-upload",className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg inline-block",children:[(0,r.jsx)("i",{className:"fas fa-upload mr-2"}),"Go to Simple Upload"]})]}),(0,r.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-3",children:"3. Test Daily Reduction"}),(0,r.jsx)("p",{className:"text-white/80 mb-3",children:"Test the daily reduction process for active days and copy-paste permissions."}),(0,r.jsx)("button",{onClick:p,disabled:d,className:"bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-4 h-4 inline-block"}),"Testing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-clock mr-2"}),"Test Daily Reduction"]})})]}),(0,r.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-3",children:"4. Check User Copy-Paste Status"}),(0,r.jsx)("p",{className:"text-white/80 mb-3",children:"Check the copy-paste permission status for a specific user."}),(0,r.jsx)("button",{onClick:h,disabled:d,className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-4 h-4 inline-block"}),"Checking..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-search mr-2"}),"Check User Status"]})})]}),(0,r.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-3",children:"5. Test Export with Copy-Paste Days"}),(0,r.jsx)("p",{className:"text-white/80 mb-3",children:"Export users to verify copy-paste remaining days are included."}),(0,r.jsxs)("a",{href:"/admin/users",className:"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg inline-block",children:[(0,r.jsx)("i",{className:"fas fa-users mr-2"}),"Go to Users & Export"]})]}),c&&(0,r.jsxs)("div",{className:`rounded-lg p-4 ${c.success?"bg-green-500/20 border border-green-500/30":"bg-red-500/20 border border-red-500/30"}`,children:[(0,r.jsx)("h3",{className:`font-bold ${c.success?"text-green-400":"text-red-400"}`,children:c.success?"Test Results - Success!":"Test Results - Failed"}),(0,r.jsx)("p",{className:"text-white mt-2",children:c.message}),c.activeDaysResult&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Active Days Result:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(c.activeDaysResult,null,2)})]}),c.copyPasteResult&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Copy-Paste Reduction Result:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(c.copyPasteResult,null,2)})]}),c.copyPasteStatus&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"User Copy-Paste Status:"}),(0,r.jsxs)("div",{className:"text-white/80 text-sm mt-1",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Email:"})," ",c.userEmail]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"User ID:"})," ",c.userId]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Has Permission:"})," ",c.copyPasteStatus.hasPermission?"Yes":"No"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Days Remaining:"})," ",c.copyPasteStatus.daysRemaining]}),c.copyPasteStatus.expiryDate&&(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Expiry Date:"})," ",c.copyPasteStatus.expiryDate.toDateString()]})]})]}),c.error&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-red-500/20 rounded",children:[(0,r.jsx)("h4",{className:"text-red-400 font-semibold",children:"Error Details:"}),(0,r.jsx)("p",{className:"text-white/80 text-sm mt-1",children:c.error})]})]}),(0,r.jsxs)("div",{className:"bg-blue-500/10 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-blue-400 font-bold mb-3",children:"Testing Instructions:"}),(0,r.jsxs)("ol",{className:"text-blue-300 text-sm space-y-2 list-decimal list-inside",children:[(0,r.jsx)("li",{children:"Download the test CSV file"}),(0,r.jsx)("li",{children:"Go to Simple Upload and upload the CSV"}),(0,r.jsx)("li",{children:"Verify users are updated with copy-paste permissions"}),(0,r.jsx)("li",{children:"Test daily reduction to see copy-paste days decrease"}),(0,r.jsx)("li",{children:"Check individual user status"}),(0,r.jsx)("li",{children:"Export users to verify copy-paste remaining days are included"}),(0,r.jsx)("li",{children:"Check browser console for detailed logs"})]})]})]})]})})})}},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76341:(e,s,t)=>{Promise.resolve().then(t.bind(t,35280))},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89736:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>l});var r=t(65239),a=t(48088),i=t(88170),o=t.n(i),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let l={children:["",{children:["test-sample-upload",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,15426)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-sample-upload\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-sample-upload\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-sample-upload/page",pathname:"/test-sample-upload",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95871:(e,s,t)=>{"use strict";t.d(s,{Ou:()=>h,Ov:()=>v,PY:()=>P,ck:()=>f,e5:()=>b,iM:()=>g,lA:()=>y,rB:()=>m,tv:()=>w,wh:()=>x});var r=t(24791),a=t(33784);async function i(){let{auth:e}=await Promise.resolve().then(t.bind(t,33784)),s=e.currentUser;if(!s)throw Error("User not authenticated");try{await s.getIdToken(!0),console.log("✅ User token refreshed and validated")}catch(e){throw console.error("❌ Error refreshing user token:",e),Error("Authentication token expired. Please refresh the page.")}}let o=(0,r.Qg)(a.Cn,"getUserWorkData"),n=(0,r.Qg)(a.Cn,"submitTranslationBatch"),c=(0,r.Qg)(a.Cn,"getUserDashboardData"),l=((0,r.Qg)(a.Cn,"getAdminDashboardData"),(0,r.Qg)(a.Cn,"getUserTransactions")),d=(0,r.Qg)(a.Cn,"processWithdrawalRequest"),u=(0,r.Qg)(a.Cn,"processDailyActiveDays"),p=((0,r.Qg)(a.Cn,"processDailyCopyPasteReduction"),(0,r.Qg)(a.Cn,"grantCopyPastePermission"),(0,r.Qg)(a.Cn,"updateUserPlan"),(0,r.Qg)(a.Cn,"getPlatformStats")),h={functionsUsed:0,firestoreReadsAvoided:0,firestoreWritesOptimized:0,incrementFunctionUsage(){this.functionsUsed++,console.log(`🚀 Firebase Functions used: ${this.functionsUsed}`)},addReadsAvoided(e){this.firestoreReadsAvoided+=e,console.log(`💰 Firestore reads avoided: ${e} (Total: ${this.firestoreReadsAvoided})`)},addWritesOptimized(e){this.firestoreWritesOptimized+=e,console.log(`⚡ Firestore writes optimized: ${e} (Total: ${this.firestoreWritesOptimized})`)},getStats(){return{functionsUsed:this.functionsUsed,firestoreReadsAvoided:this.firestoreReadsAvoided,firestoreWritesOptimized:this.firestoreWritesOptimized,estimatedCostSavings:(36e-5*this.firestoreReadsAvoided+.00108*this.firestoreWritesOptimized).toFixed(4)}}};async function m(){try{console.log("\uD83D\uDE80 Fetching user work data via Firebase Function..."),await i();let e=(await o()).data;return h.incrementFunctionUsage(),h.addReadsAvoided(4),console.log("✅ User work data fetched successfully:",e),e}catch(e){if(console.error("❌ Error fetching user work data:",e),"unauthenticated"===e.code||e.message?.includes("User must be authenticated"))throw Error("Authentication required. Please refresh the page and try again.");throw Error("Failed to fetch user work data")}}async function g(e=50){try{console.log(`🚀 Submitting translation batch via Firebase Function: ${e} translations`),await i();let s=(await n({batchSize:e})).data;return h.incrementFunctionUsage(),h.addWritesOptimized(3),console.log("✅ Translation batch submitted successfully:",s),s}catch(e){if(console.error("❌ Error submitting translation batch:",e),"unauthenticated"===e.code||e.message?.includes("User must be authenticated"))throw Error("Authentication required. Please refresh the page and try again.");if("permission-denied"===e.code)throw Error("Permission denied. Please check your account status.");throw Error("Failed to submit translation batch")}}async function x(){try{console.log("\uD83D\uDE80 Fetching user dashboard data via Firebase Function..."),await i();let e=(await c()).data;return h.incrementFunctionUsage(),h.addReadsAvoided(3),console.log("✅ User dashboard data fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching user dashboard data:",e),Error("Failed to fetch user dashboard data")}}async function y(e=20,s){try{console.log(`🚀 Fetching user transactions via Firebase Function: limit=${e}`);let t=(await l({limit:e,startAfter:s})).data;return h.incrementFunctionUsage(),h.addReadsAvoided(Math.max(0,100-e)),console.log("✅ User transactions fetched successfully:",t),t}catch(e){throw console.error("❌ Error fetching user transactions:",e),Error("Failed to fetch user transactions")}}async function f(e,s){try{console.log(`🚀 Processing withdrawal request via Firebase Function: ₹${e}`);let t=(await d({amount:e,upiId:s})).data;return h.incrementFunctionUsage(),h.addWritesOptimized(2),console.log("✅ Withdrawal request processed successfully:",t),t}catch(e){throw console.error("❌ Error processing withdrawal request:",e),Error("Failed to process withdrawal request")}}async function b(){try{console.log("\uD83D\uDE80 Processing daily active days via Firebase Function...");let e=(await u()).data;return h.incrementFunctionUsage(),h.addWritesOptimized(e.updated||0),console.log("✅ Daily active days processed successfully:",e),e}catch(e){throw console.error("❌ Error processing daily active days:",e),Error("Failed to process daily active days")}}async function w(){try{console.log("\uD83D\uDE80 Fetching platform stats via Firebase Function...");let e=(await p()).data;return h.incrementFunctionUsage(),h.addReadsAvoided(10),console.log("✅ Platform stats fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching platform stats:",e),Error("Failed to fetch platform stats")}}async function v(){try{console.log("\uD83D\uDE80 Getting optimized user work data..."),await i();let e=await o();return console.log("✅ Optimized user work data retrieved"),e.data}catch(e){throw console.error("❌ Error getting optimized user work data:",e),e}}async function P(e=50){try{console.log(`🚀 Submitting optimized translation batch: ${e} translations`);let s=await n({batchSize:e});return console.log("✅ Optimized translation batch submitted"),s.data}catch(e){throw console.error("❌ Error submitting optimized translation batch:",e),e}}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4573,6803],()=>t(89736));module.exports=r})();