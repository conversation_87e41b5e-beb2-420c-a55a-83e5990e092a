/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth-test/page";
exports.ids = ["app/auth-test/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth-test%2Fpage&page=%2Fauth-test%2Fpage&appPaths=%2Fauth-test%2Fpage&pagePath=private-next-app-dir%2Fauth-test%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth-test%2Fpage&page=%2Fauth-test%2Fpage&appPaths=%2Fauth-test%2Fpage&pagePath=private-next-app-dir%2Fauth-test%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\"));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth-test/page.tsx */ \"(rsc)/./src/app/auth-test/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth-test',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\auth-test\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\layout.tsx\"],\n'error': [module1, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\"],\n'loading': [module2, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [module3, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module4, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module5, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\auth-test\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth-test/page\",\n        pathname: \"/auth-test\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth-test%2Fpage&page=%2Fauth-test%2Fpage&appPaths=%2Fauth-test%2Fpage&pagePath=private-next-app-dir%2Fauth-test%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(rsc)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyMEluc3RyYSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBU1VTJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDTVklMjBQUk9KRUNUUyU1QyU1Q05vZGUlMjBJbnN0cmElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2ltYWdlLWNvbXBvbmVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQStMO0FBQy9MO0FBQ0Esc05BQXVKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgSW5zdHJhXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgSW5zdHJhXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGltYWdlLWNvbXBvbmVudC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CDailyActiveDaysScheduler.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5COfflineSupport.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CUpdateManager.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CDailyActiveDaysScheduler.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5COfflineSupport.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CUpdateManager.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/DailyActiveDaysScheduler.tsx */ \"(rsc)/./src/components/DailyActiveDaysScheduler.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(rsc)/./src/components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/OfflineSupport.tsx */ \"(rsc)/./src/components/OfflineSupport.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstaller.tsx */ \"(rsc)/./src/components/PWAInstaller.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/UpdateManager.tsx */ \"(rsc)/./src/components/UpdateManager.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyMEluc3RyYSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIyUG9wcGlucyU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCUyQyU1QyUyMndlaWdodCU1QyUyMiUzQSU1QiU1QyUyMjMwMCU1QyUyMiUyQyU1QyUyMjQwMCU1QyUyMiUyQyU1QyUyMjUwMCU1QyUyMiUyQyU1QyUyMjYwMCU1QyUyMiUyQyU1QyUyMjcwMCU1QyUyMiU1RCUyQyU1QyUyMnZhcmlhYmxlJTVDJTIyJTNBJTVDJTIyLS1mb250LXBvcHBpbnMlNUMlMjIlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJwb3BwaW5zJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyMEluc3RyYSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyMEluc3RyYSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNEYWlseUFjdGl2ZURheXNTY2hlZHVsZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBU1VTJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDTVklMjBQUk9KRUNUUyU1QyU1Q05vZGUlMjBJbnN0cmElNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDRXJyb3JCb3VuZGFyeS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyMEluc3RyYSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNPZmZsaW5lU3VwcG9ydC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyMEluc3RyYSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNQV0FJbnN0YWxsZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBU1VTJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDTVklMjBQUk9KRUNUUyU1QyU1Q05vZGUlMjBJbnN0cmElNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDVXBkYXRlTWFuYWdlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTUFBNks7QUFDN0s7QUFDQSxnTEFBa0s7QUFDbEs7QUFDQSxrTEFBbUs7QUFDbks7QUFDQSw4S0FBaUs7QUFDaks7QUFDQSxnTEFBa0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBJbnN0cmFcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcRGFpbHlBY3RpdmVEYXlzU2NoZWR1bGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXEFTVVNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxNWSBQUk9KRUNUU1xcXFxOb2RlIEluc3RyYVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxFcnJvckJvdW5kYXJ5LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXEFTVVNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxNWSBQUk9KRUNUU1xcXFxOb2RlIEluc3RyYVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxPZmZsaW5lU3VwcG9ydC50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBJbnN0cmFcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcUFdBSW5zdGFsbGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXEFTVVNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxNWSBQUk9KRUNUU1xcXFxOb2RlIEluc3RyYVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxVcGRhdGVNYW5hZ2VyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CDailyActiveDaysScheduler.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5COfflineSupport.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CUpdateManager.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cauth-test%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cauth-test%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth-test/page.tsx */ \"(rsc)/./src/app/auth-test/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyMEluc3RyYSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2F1dGgtdGVzdCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBZ0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFTVVNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxNWSBQUk9KRUNUU1xcXFxOb2RlIEluc3RyYVxcXFxzcmNcXFxcYXBwXFxcXGF1dGgtdGVzdFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cauth-test%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyMEluc3RyYSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXNIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBJbnN0cmFcXFxcc3JjXFxcXGFwcFxcXFxlcnJvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/auth-test/page.tsx":
/*!************************************!*\
  !*** ./src/app/auth-test/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\auth-test\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\auth-test\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fd6b0e8f89fe\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFTVVNcXE9uZURyaXZlXFxEZXNrdG9wXFxNWSBQUk9KRUNUU1xcTm9kZSBJbnN0cmFcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZkNmIwZThmODlmZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-poppins\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_PWAInstaller__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PWAInstaller */ \"(rsc)/./src/components/PWAInstaller.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(rsc)/./src/components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_DailyActiveDaysScheduler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DailyActiveDaysScheduler */ \"(rsc)/./src/components/DailyActiveDaysScheduler.tsx\");\n/* harmony import */ var _components_OfflineSupport__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/OfflineSupport */ \"(rsc)/./src/components/OfflineSupport.tsx\");\n/* harmony import */ var _components_UpdateManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/UpdateManager */ \"(rsc)/./src/components/UpdateManager.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: 'Instra Global - Instant Translation & Earn',\n    description: 'Translate text and earn money. Complete daily translation tasks to earn rewards.',\n    keywords: 'translation, earn money, online earning, translation tasks, rewards, language services',\n    authors: [\n        {\n            name: 'Instra Global Team'\n        }\n    ],\n    manifest: '/manifest.json',\n    icons: {\n        icon: '/img/instra-favicon.svg',\n        apple: '/img/instra-favicon.svg'\n    }\n};\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1.0,\n    maximumScale: 1.0,\n    userScalable: false,\n    themeColor: '#6A11CB'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_7___default().variable),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        src: \"https://cdn.jsdelivr.net/npm/sweetalert2@11\",\n                        async: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_7___default().className)} antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animated-bg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UpdateManager__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OfflineSupport__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PWAInstaller__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DailyActiveDaysScheduler__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"spinner mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-white/80\",\n                    children: \"Loading Instra Global...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\loading.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzs7Ozs7OEJBQ2YsOERBQUNDO29CQUFFRCxXQUFVOzhCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJckMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQVNVU1xcT25lRHJpdmVcXERlc2t0b3BcXE1ZIFBST0pFQ1RTXFxOb2RlIEluc3RyYVxcc3JjXFxhcHBcXGxvYWRpbmcudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmcoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGlubmVyIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS84MFwiPkxvYWRpbmcgSW5zdHJhIEdsb2JhbC4uLjwvcD5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiTG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSIsInAiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/img/instra-logo.svg\",\n                            alt: \"Instra Global Logo\",\n                            width: 80,\n                            height: 80,\n                            className: \"mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-6xl font-bold text-white mb-4\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-white mb-2\",\n                            children: \"Page Not Found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-8 max-w-md mx-auto\",\n                            children: \"The page you're looking for doesn't exist or has been moved.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-4\",\n                                    children: \"Need help finding what you're looking for?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:<EMAIL>\",\n                                        className: \"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-envelope mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                                                lineNumber: 30,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Email Support\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"btn-primary inline-flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-home mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go Home\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-tachometer-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/work\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-play-circle mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Watch Videos\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/DailyActiveDaysScheduler.tsx":
/*!*****************************************************!*\
  !*** ./src/components/DailyActiveDaysScheduler.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\DailyActiveDaysScheduler.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\components\\DailyActiveDaysScheduler.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\components\\ErrorBoundary.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/OfflineSupport.tsx":
/*!*******************************************!*\
  !*** ./src/components/OfflineSupport.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useOfflineStatus: () => (/* binding */ useOfflineStatus)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\components\\OfflineSupport.tsx",
"default",
));
const useOfflineStatus = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useOfflineStatus() from the server but useOfflineStatus is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\components\\OfflineSupport.tsx",
"useOfflineStatus",
);

/***/ }),

/***/ "(rsc)/./src/components/PWAInstaller.tsx":
/*!*****************************************!*\
  !*** ./src/components/PWAInstaller.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\PWAInstaller.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\components\\PWAInstaller.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/UpdateManager.tsx":
/*!******************************************!*\
  !*** ./src/components/UpdateManager.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\UpdateManager.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\components\\UpdateManager.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyMEluc3RyYSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBU1VTJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDTVklMjBQUk9KRUNUUyU1QyU1Q05vZGUlMjBJbnN0cmElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2ltYWdlLWNvbXBvbmVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQStMO0FBQy9MO0FBQ0Esc05BQXVKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgSW5zdHJhXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXE1ZIFBST0pFQ1RTXFxcXE5vZGUgSW5zdHJhXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGltYWdlLWNvbXBvbmVudC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CDailyActiveDaysScheduler.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5COfflineSupport.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CUpdateManager.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CDailyActiveDaysScheduler.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5COfflineSupport.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CUpdateManager.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/DailyActiveDaysScheduler.tsx */ \"(ssr)/./src/components/DailyActiveDaysScheduler.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(ssr)/./src/components/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/OfflineSupport.tsx */ \"(ssr)/./src/components/OfflineSupport.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PWAInstaller.tsx */ \"(ssr)/./src/components/PWAInstaller.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/UpdateManager.tsx */ \"(ssr)/./src/components/UpdateManager.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CDailyActiveDaysScheduler.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5COfflineSupport.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CPWAInstaller.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Ccomponents%5C%5CUpdateManager.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cauth-test%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cauth-test%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth-test/page.tsx */ \"(ssr)/./src/app/auth-test/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyMEluc3RyYSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2F1dGgtdGVzdCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBZ0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFTVVNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxNWSBQUk9KRUNUU1xcXFxOb2RlIEluc3RyYVxcXFxzcmNcXFxcYXBwXFxcXGF1dGgtdGVzdFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cauth-test%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FTVVMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNNWSUyMFBST0pFQ1RTJTVDJTVDTm9kZSUyMEluc3RyYSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXNIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBU1VTXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcTVkgUFJPSkVDVFNcXFxcTm9kZSBJbnN0cmFcXFxcc3JjXFxcXGFwcFxcXFxlcnJvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CASUS%5C%5COneDrive%5C%5CDesktop%5C%5CMY%20PROJECTS%5C%5CNode%20Instra%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/auth-test/page.tsx":
/*!************************************!*\
  !*** ./src/app/auth-test/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthTestPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_AuthTest__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/AuthTest */ \"(ssr)/./src/components/AuthTest.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AuthTestPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-800 mb-2\",\n                            children: \"Authentication Test Page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Test Firebase Functions authentication and error handling\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthTest__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2F1dGgtdGVzdC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUU0QztBQUU3QixTQUFTQztJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUdELFdBQVU7c0NBQXdDOzs7Ozs7c0NBR3RELDhEQUFDRTs0QkFBRUYsV0FBVTtzQ0FBZ0I7Ozs7Ozs7Ozs7Ozs4QkFLL0IsOERBQUNILDREQUFRQTs7Ozs7Ozs7Ozs7Ozs7OztBQUlqQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBU1VTXFxPbmVEcml2ZVxcRGVza3RvcFxcTVkgUFJPSkVDVFNcXE5vZGUgSW5zdHJhXFxzcmNcXGFwcFxcYXV0aC10ZXN0XFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IEF1dGhUZXN0IGZyb20gJ0AvY29tcG9uZW50cy9BdXRoVGVzdCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXV0aFRlc3RQYWdlKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHktOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLThcIj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS04MDAgbWItMlwiPlxuICAgICAgICAgICAgQXV0aGVudGljYXRpb24gVGVzdCBQYWdlXG4gICAgICAgICAgPC9oMT5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICBUZXN0IEZpcmViYXNlIEZ1bmN0aW9ucyBhdXRoZW50aWNhdGlvbiBhbmQgZXJyb3IgaGFuZGxpbmdcbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAgPEF1dGhUZXN0IC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkF1dGhUZXN0IiwiQXV0aFRlc3RQYWdlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth-test/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Error.useEffect\": ()=>{\n            console.error('Application error:', error);\n        }\n    }[\"Error.useEffect\"], [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/img/instra-logo.svg\",\n                            alt: \"Instra Global Logo\",\n                            width: 80,\n                            height: 80,\n                            className: \"mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-white mb-4\",\n                            children: \"Oops!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-white mb-2\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-8 max-w-md mx-auto\",\n                            children: \"We encountered an unexpected error. Please try again or contact support if the problem persists.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-4\",\n                                    children: \"Need immediate help?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:<EMAIL>\",\n                                        className: \"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-envelope mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Email Support\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: reset,\n                            className: \"btn-primary inline-flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-redo mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-home mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Go Home\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"btn-secondary inline-flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-tachometer-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mt-8 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"text-white/60 cursor-pointer\",\n                            children: \"Error Details (Development)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-4 p-4 bg-red-900/20 rounded-lg text-red-300 text-sm overflow-auto\",\n                            children: [\n                                error.message,\n                                error.stack && '\\n\\n' + error.stack\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthTest.tsx":
/*!*************************************!*\
  !*** ./src/components/AuthTest.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthTest)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _lib_firebaseFunctions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebaseFunctions */ \"(ssr)/./src/lib/firebaseFunctions.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AuthTest() {\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth)();\n    const [testResults, setTestResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const addResult = (testName, success, data, error)=>{\n        setTestResults((prev)=>[\n                ...prev,\n                {\n                    testName,\n                    success,\n                    data,\n                    error: error?.message || error?.code || error,\n                    timestamp: new Date().toISOString()\n                }\n            ]);\n    };\n    const runAuthTest = async ()=>{\n        setIsLoading(true);\n        setTestResults([]);\n        try {\n            // Test 1: Check authentication status\n            console.log('🧪 Testing authentication status...');\n            const authStatus = await (0,_lib_firebaseFunctions__WEBPACK_IMPORTED_MODULE_3__.checkAuthenticationStatus)();\n            addResult('Authentication Status Check', true, authStatus);\n            // Test 2: Test getUserDashboardData\n            console.log('🧪 Testing getUserDashboardData...');\n            try {\n                const dashboardData = await (0,_lib_firebaseFunctions__WEBPACK_IMPORTED_MODULE_3__.getUserDashboardData)();\n                addResult('Get User Dashboard Data', true, dashboardData);\n            } catch (error) {\n                addResult('Get User Dashboard Data', false, null, error);\n            }\n            // Test 3: Test getUserWorkData\n            console.log('🧪 Testing getUserWorkData...');\n            try {\n                const workData = await (0,_lib_firebaseFunctions__WEBPACK_IMPORTED_MODULE_3__.getUserWorkData)();\n                addResult('Get User Work Data', true, workData);\n            } catch (error) {\n                addResult('Get User Work Data', false, null, error);\n            }\n        } catch (error) {\n            console.error('❌ Auth test failed:', error);\n            addResult('Authentication Test', false, null, error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading authentication...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-red-800 font-semibold mb-2\",\n                    children: \"Authentication Required\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600\",\n                    children: \"Please log in to test authentication.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-800 mb-6\",\n                    children: \"Authentication Test\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-blue-800 font-semibold mb-2\",\n                            children: \"Current User\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"UID:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                user.uid,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 45\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Email:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                user.email,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 49\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Display Name:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                user.displayName || 'Not set'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: runAuthTest,\n                        disabled: isLoading,\n                        className: \"bg-purple-600 hover:bg-purple-700 disabled:bg-purple-300 text-white font-semibold py-2 px-6 rounded-lg transition-colors\",\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 17\n                                }, this),\n                                \"Running Tests...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 15\n                        }, this) : 'Run Authentication Tests'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this),\n                testResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-800\",\n                            children: \"Test Results\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, this),\n                        testResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `border rounded-lg p-4 ${result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: `font-semibold ${result.success ? 'text-green-800' : 'text-red-800'}`,\n                                                children: [\n                                                    result.success ? '✅' : '❌',\n                                                    \" \",\n                                                    result.testName\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: new Date(result.timestamp).toLocaleTimeString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this),\n                                    result.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-100 border border-red-300 rounded p-2 mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-700 text-sm font-mono\",\n                                            children: result.error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 19\n                                    }, this),\n                                    result.data && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                        className: \"mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                className: \"cursor-pointer text-sm text-gray-600 hover:text-gray-800\",\n                                                children: \"View Data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"mt-2 bg-gray-100 border rounded p-2 text-xs overflow-auto max-h-40\",\n                                                children: JSON.stringify(result.data, null, 2)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\AuthTest.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthTest.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/DailyActiveDaysScheduler.tsx":
/*!*****************************************************!*\
  !*** ./src/components/DailyActiveDaysScheduler.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DailyActiveDaysScheduler)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction DailyActiveDaysScheduler() {\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useAuthState)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"DailyActiveDaysScheduler.useEffect\": ()=>{\n            // NEW APPROACH: First user after 12 AM IST triggers daily processing\n            if (loading || !user) {\n                console.log('⏸️ DailyActiveDaysScheduler: Waiting for authentication...');\n                return;\n            }\n            const checkAndTriggerDailyProcessing = {\n                \"DailyActiveDaysScheduler.useEffect.checkAndTriggerDailyProcessing\": async ()=>{\n                    try {\n                        const today = new Date().toDateString();\n                        const lastProcessed = localStorage.getItem('lastDailyProcessing');\n                        // Check if we've already processed today\n                        if (lastProcessed === today) {\n                            console.log('✅ Daily processing already completed today');\n                            return;\n                        }\n                        // Check if it's after 12:00 AM IST (current day)\n                        const now = new Date();\n                        const istTime = new Date(now.toLocaleString(\"en-US\", {\n                            timeZone: \"Asia/Kolkata\"\n                        }));\n                        const todayMidnight = new Date(istTime);\n                        todayMidnight.setHours(0, 0, 0, 0);\n                        // Only trigger if it's a new day (after midnight IST) and not processed yet\n                        if (istTime >= todayMidnight && lastProcessed !== today) {\n                            console.log('🌅 First user after 12 AM IST - triggering daily processing...');\n                            // Call the Firebase Function for daily processing\n                            const response = await fetch('https://us-central1-instra-global.cloudfunctions.net/dailyProcessingCron');\n                            const result = await response.json();\n                            if (result.success) {\n                                console.log('✅ Daily processing completed successfully:', result);\n                                localStorage.setItem('lastDailyProcessing', today);\n                            } else {\n                                console.error('❌ Daily processing failed:', result);\n                            }\n                        } else {\n                            console.log('⏸️ Not time for daily processing yet or already processed');\n                        }\n                    } catch (error) {\n                        console.error('❌ Error in daily processing trigger:', error);\n                    }\n                }\n            }[\"DailyActiveDaysScheduler.useEffect.checkAndTriggerDailyProcessing\"];\n            console.log('🔄 DailyActiveDaysScheduler: Checking if daily processing needed...');\n            checkAndTriggerDailyProcessing();\n        }\n    }[\"DailyActiveDaysScheduler.useEffect\"], [\n        user,\n        loading\n    ]) // Re-run when user authentication changes\n    ;\n    // This component doesn't render anything visible\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/DailyActiveDaysScheduler.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-card p-8 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"fas fa-exclamation-triangle text-red-400 text-4xl mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-white mb-2\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-4\",\n                            children: \"An error occurred while loading this page. Please refresh and try again.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"btn-primary\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-refresh mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this),\n                                \"Refresh Page\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/OfflineSupport.tsx":
/*!*******************************************!*\
  !*** ./src/components/OfflineSupport.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OfflineSupport),\n/* harmony export */   useOfflineStatus: () => (/* binding */ useOfflineStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* __next_internal_client_entry_do_not_use__ default,useOfflineStatus auto */ \n\n\nfunction OfflineSupport({ children }) {\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showOfflineMessage, setShowOfflineMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [offlineStartTime, setOfflineStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuthState)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OfflineSupport.useEffect\": ()=>{\n            // Set initial online status (only on client side)\n            if (typeof navigator !== 'undefined') {\n                setIsOnline(navigator.onLine);\n            }\n            const handleOnline = {\n                \"OfflineSupport.useEffect.handleOnline\": ()=>{\n                    console.log('🌐 Network: Back online');\n                    setIsOnline(true);\n                    setShowOfflineMessage(false);\n                    setOfflineStartTime(null);\n                    // Show reconnection message\n                    if (offlineStartTime) {\n                        const offlineDuration = Math.round((Date.now() - offlineStartTime.getTime()) / 1000);\n                        console.log(`📶 Reconnected after ${offlineDuration} seconds offline`);\n                    }\n                }\n            }[\"OfflineSupport.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"OfflineSupport.useEffect.handleOffline\": ()=>{\n                    console.log('📵 Network: Offline');\n                    setIsOnline(false);\n                    setOfflineStartTime(new Date());\n                    // Show offline message after 2 seconds\n                    setTimeout({\n                        \"OfflineSupport.useEffect.handleOffline\": ()=>{\n                            if (!navigator.onLine) {\n                                setShowOfflineMessage(true);\n                            }\n                        }\n                    }[\"OfflineSupport.useEffect.handleOffline\"], 2000);\n                }\n            }[\"OfflineSupport.useEffect.handleOffline\"];\n            // Add event listeners (only on client side)\n            if (false) {}\n            // Cleanup\n            return ({\n                \"OfflineSupport.useEffect\": ()=>{\n                    if (false) {}\n                }\n            })[\"OfflineSupport.useEffect\"];\n        }\n    }[\"OfflineSupport.useEffect\"], [\n        offlineStartTime\n    ]);\n    // Note: Removed periodic connectivity check to avoid 404 errors\n    // Browser's native online/offline events are sufficient for most cases\n    if (showOfflineMessage) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/10 backdrop-blur-lg rounded-2xl p-8 text-center border border-white/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto w-20 h-20 bg-red-500/20 rounded-full flex items-center justify-center mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-wifi-slash text-red-400 text-3xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-white mb-4\",\n                            children: \"You're Offline\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/80 mb-6 leading-relaxed\",\n                            children: \"Your internet connection seems to be down. Don't worry - your work progress is safely saved locally and will be restored when you're back online.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/5 rounded-lg p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white/60\",\n                                            children: \"Connection Status:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-400 font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-circle text-xs mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Offline\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this),\n                                offlineStartTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white/60\",\n                                            children: \"Offline Since:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white/80\",\n                                            children: offlineStartTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 17\n                                }, this),\n                                user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white/60\",\n                                            children: \"User Session:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-400 font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-check text-xs mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Protected\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-left mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold mb-3 text-center\",\n                                    children: \"Available Offline:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-white/80 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-check text-green-400 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Continue current translation work\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-check text-green-400 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Auto-save progress locally\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-check text-green-400 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"View completed translations\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-times text-red-400 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Submit translations for earnings\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.reload(),\n                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-sync-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Check Connection\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowOfflineMessage(false),\n                                    className: \"w-full bg-white/10 hover:bg-white/20 text-white font-semibold py-3 px-6 rounded-lg transition-colors border border-white/20\",\n                                    children: \"Continue Offline\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 p-4 bg-yellow-500/10 rounded-lg border border-yellow-500/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-yellow-200 text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-lightbulb mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Tip:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" Your work is automatically saved every 10 seconds. When you reconnect, everything will sync automatically.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this);\n    }\n    // Show online content with offline indicator if needed\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            !isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 left-0 right-0 bg-red-600 text-white text-center py-2 text-sm z-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"fas fa-wifi-slash mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    \"You're offline - work is saved locally\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowOfflineMessage(true),\n                        className: \"ml-4 underline hover:no-underline\",\n                        children: \"View Details\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: !isOnline ? 'pt-10' : '',\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, this);\n}\n// Hook for offline status\nfunction useOfflineStatus() {\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wasOffline, setWasOffline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useOfflineStatus.useEffect\": ()=>{\n            if (typeof navigator !== 'undefined') {\n                setIsOnline(navigator.onLine);\n            }\n            const handleOnline = {\n                \"useOfflineStatus.useEffect.handleOnline\": ()=>{\n                    setIsOnline(true);\n                    if (wasOffline) {\n                        // Show reconnection notification\n                        console.log('🌐 Reconnected to internet');\n                        setWasOffline(false);\n                    }\n                }\n            }[\"useOfflineStatus.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"useOfflineStatus.useEffect.handleOffline\": ()=>{\n                    setIsOnline(false);\n                    setWasOffline(true);\n                }\n            }[\"useOfflineStatus.useEffect.handleOffline\"];\n            if (false) {}\n            return ({\n                \"useOfflineStatus.useEffect\": ()=>{\n                    if (false) {}\n                }\n            })[\"useOfflineStatus.useEffect\"];\n        }\n    }[\"useOfflineStatus.useEffect\"], [\n        wasOffline\n    ]);\n    return {\n        isOnline,\n        wasOffline\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9PZmZsaW5lU3VwcG9ydC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFMkM7QUFDRztBQU0vQixTQUFTRyxlQUFlLEVBQUVDLFFBQVEsRUFBdUI7SUFDdEUsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdOLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ08sb0JBQW9CQyxzQkFBc0IsR0FBR1IsK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxDQUFDUyxrQkFBa0JDLG9CQUFvQixHQUFHViwrQ0FBUUEsQ0FBYztJQUN0RSxNQUFNLEVBQUVXLElBQUksRUFBRSxHQUFHVCw0REFBWUE7SUFFN0JELGdEQUFTQTtvQ0FBQztZQUNSLGtEQUFrRDtZQUNsRCxJQUFJLE9BQU9XLGNBQWMsYUFBYTtnQkFDcENOLFlBQVlNLFVBQVVDLE1BQU07WUFDOUI7WUFFQSxNQUFNQzt5REFBZTtvQkFDbkJDLFFBQVFDLEdBQUcsQ0FBQztvQkFDWlYsWUFBWTtvQkFDWkUsc0JBQXNCO29CQUN0QkUsb0JBQW9CO29CQUVwQiw0QkFBNEI7b0JBQzVCLElBQUlELGtCQUFrQjt3QkFDcEIsTUFBTVEsa0JBQWtCQyxLQUFLQyxLQUFLLENBQUMsQ0FBQ0MsS0FBS0MsR0FBRyxLQUFLWixpQkFBaUJhLE9BQU8sRUFBQyxJQUFLO3dCQUMvRVAsUUFBUUMsR0FBRyxDQUFDLENBQUMscUJBQXFCLEVBQUVDLGdCQUFnQixnQkFBZ0IsQ0FBQztvQkFDdkU7Z0JBQ0Y7O1lBRUEsTUFBTU07MERBQWdCO29CQUNwQlIsUUFBUUMsR0FBRyxDQUFDO29CQUNaVixZQUFZO29CQUNaSSxvQkFBb0IsSUFBSVU7b0JBRXhCLHVDQUF1QztvQkFDdkNJO2tFQUFXOzRCQUNULElBQUksQ0FBQ1osVUFBVUMsTUFBTSxFQUFFO2dDQUNyQkwsc0JBQXNCOzRCQUN4Qjt3QkFDRjtpRUFBRztnQkFDTDs7WUFFQSw0Q0FBNEM7WUFDNUMsSUFBSSxLQUE2QixFQUFFLEVBR2xDO1lBRUQsVUFBVTtZQUNWOzRDQUFPO29CQUNMLElBQUksS0FBNkIsRUFBRSxFQUdsQztnQkFDSDs7UUFDRjttQ0FBRztRQUFDQztLQUFpQjtJQUVyQixnRUFBZ0U7SUFDaEUsdUVBQXVFO0lBRXZFLElBQUlGLG9CQUFvQjtRQUN0QixxQkFDRSw4REFBQ3FCO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOzBCQUViLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDQztnQ0FBRUQsV0FBVTs7Ozs7Ozs7Ozs7c0NBSWYsOERBQUNFOzRCQUFHRixXQUFVO3NDQUFxQzs7Ozs7O3NDQUtuRCw4REFBQ0c7NEJBQUVILFdBQVU7c0NBQXFDOzs7Ozs7c0NBS2xELDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0k7NENBQUtKLFdBQVU7c0RBQWdCOzs7Ozs7c0RBQ2hDLDhEQUFDSTs0Q0FBS0osV0FBVTs7OERBQ2QsOERBQUNDO29EQUFFRCxXQUFVOzs7Ozs7Z0RBQWlDOzs7Ozs7Ozs7Ozs7O2dDQUlqRHBCLGtDQUNDLDhEQUFDbUI7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDSTs0Q0FBS0osV0FBVTtzREFBZ0I7Ozs7OztzREFDaEMsOERBQUNJOzRDQUFLSixXQUFVO3NEQUNicEIsaUJBQWlCeUIsa0JBQWtCOzs7Ozs7Ozs7Ozs7Z0NBSXpDdkIsc0JBQ0MsOERBQUNpQjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNJOzRDQUFLSixXQUFVO3NEQUFnQjs7Ozs7O3NEQUNoQyw4REFBQ0k7NENBQUtKLFdBQVU7OzhEQUNkLDhEQUFDQztvREFBRUQsV0FBVTs7Ozs7O2dEQUFnQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FRckQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ007b0NBQUdOLFdBQVU7OENBQTRDOzs7Ozs7OENBQzFELDhEQUFDTztvQ0FBR1AsV0FBVTs7c0RBQ1osOERBQUNROzRDQUFHUixXQUFVOzs4REFDWiw4REFBQ0M7b0RBQUVELFdBQVU7Ozs7OztnREFBdUM7Ozs7Ozs7c0RBR3RELDhEQUFDUTs0Q0FBR1IsV0FBVTs7OERBQ1osOERBQUNDO29EQUFFRCxXQUFVOzs7Ozs7Z0RBQXVDOzs7Ozs7O3NEQUd0RCw4REFBQ1E7NENBQUdSLFdBQVU7OzhEQUNaLDhEQUFDQztvREFBRUQsV0FBVTs7Ozs7O2dEQUF1Qzs7Ozs7OztzREFHdEQsOERBQUNROzRDQUFHUixXQUFVOzs4REFDWiw4REFBQ0M7b0RBQUVELFdBQVU7Ozs7OztnREFBcUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT3hELDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNTO29DQUNDQyxTQUFTLElBQU1kLE9BQU9lLFFBQVEsQ0FBQ0MsTUFBTTtvQ0FDckNaLFdBQVU7O3NEQUVWLDhEQUFDQzs0Q0FBRUQsV0FBVTs7Ozs7O3dDQUEyQjs7Ozs7Ozs4Q0FJMUMsOERBQUNTO29DQUNDQyxTQUFTLElBQU0vQixzQkFBc0I7b0NBQ3JDcUIsV0FBVTs4Q0FDWDs7Ozs7Ozs7Ozs7O3NDQU1ILDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0c7Z0NBQUVILFdBQVU7O2tEQUNYLDhEQUFDQzt3Q0FBRUQsV0FBVTs7Ozs7O2tEQUNiLDhEQUFDYTtrREFBTzs7Ozs7O29DQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBT25DO0lBRUEsdURBQXVEO0lBQ3ZELHFCQUNFLDhEQUFDZDtRQUFJQyxXQUFVOztZQUVaLENBQUN4QiwwQkFDQSw4REFBQ3VCO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUVELFdBQVU7Ozs7OztvQkFBNkI7a0NBRTFDLDhEQUFDUzt3QkFDQ0MsU0FBUyxJQUFNL0Isc0JBQXNCO3dCQUNyQ3FCLFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7OzswQkFPTCw4REFBQ0Q7Z0JBQUlDLFdBQVcsQ0FBQ3hCLFdBQVcsVUFBVTswQkFDbkNEOzs7Ozs7Ozs7Ozs7QUFJVDtBQUVBLDBCQUEwQjtBQUNuQixTQUFTdUM7SUFDZCxNQUFNLENBQUN0QyxVQUFVQyxZQUFZLEdBQUdOLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQzRDLFlBQVlDLGNBQWMsR0FBRzdDLCtDQUFRQSxDQUFDO0lBRTdDQyxnREFBU0E7c0NBQUM7WUFDUixJQUFJLE9BQU9XLGNBQWMsYUFBYTtnQkFDcENOLFlBQVlNLFVBQVVDLE1BQU07WUFDOUI7WUFFQSxNQUFNQzsyREFBZTtvQkFDbkJSLFlBQVk7b0JBQ1osSUFBSXNDLFlBQVk7d0JBQ2QsaUNBQWlDO3dCQUNqQzdCLFFBQVFDLEdBQUcsQ0FBQzt3QkFDWjZCLGNBQWM7b0JBQ2hCO2dCQUNGOztZQUVBLE1BQU10Qjs0REFBZ0I7b0JBQ3BCakIsWUFBWTtvQkFDWnVDLGNBQWM7Z0JBQ2hCOztZQUVBLElBQUksS0FBNkIsRUFBRSxFQUdsQztZQUVEOzhDQUFPO29CQUNMLElBQUksS0FBNkIsRUFBRSxFQUdsQztnQkFDSDs7UUFDRjtxQ0FBRztRQUFDRDtLQUFXO0lBRWYsT0FBTztRQUFFdkM7UUFBVXVDO0lBQVc7QUFDaEMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQVNVU1xcT25lRHJpdmVcXERlc2t0b3BcXE1ZIFBST0pFQ1RTXFxOb2RlIEluc3RyYVxcc3JjXFxjb21wb25lbnRzXFxPZmZsaW5lU3VwcG9ydC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHVzZUF1dGhTdGF0ZSB9IGZyb20gJ0AvaG9va3MvdXNlQXV0aCdcblxuaW50ZXJmYWNlIE9mZmxpbmVTdXBwb3J0UHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE9mZmxpbmVTdXBwb3J0KHsgY2hpbGRyZW4gfTogT2ZmbGluZVN1cHBvcnRQcm9wcykge1xuICBjb25zdCBbaXNPbmxpbmUsIHNldElzT25saW5lXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtzaG93T2ZmbGluZU1lc3NhZ2UsIHNldFNob3dPZmZsaW5lTWVzc2FnZV0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW29mZmxpbmVTdGFydFRpbWUsIHNldE9mZmxpbmVTdGFydFRpbWVdID0gdXNlU3RhdGU8RGF0ZSB8IG51bGw+KG51bGwpXG4gIGNvbnN0IHsgdXNlciB9ID0gdXNlQXV0aFN0YXRlKClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFNldCBpbml0aWFsIG9ubGluZSBzdGF0dXMgKG9ubHkgb24gY2xpZW50IHNpZGUpXG4gICAgaWYgKHR5cGVvZiBuYXZpZ2F0b3IgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBzZXRJc09ubGluZShuYXZpZ2F0b3Iub25MaW5lKVxuICAgIH1cblxuICAgIGNvbnN0IGhhbmRsZU9ubGluZSA9ICgpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn4yQIE5ldHdvcms6IEJhY2sgb25saW5lJylcbiAgICAgIHNldElzT25saW5lKHRydWUpXG4gICAgICBzZXRTaG93T2ZmbGluZU1lc3NhZ2UoZmFsc2UpXG4gICAgICBzZXRPZmZsaW5lU3RhcnRUaW1lKG51bGwpXG4gICAgICBcbiAgICAgIC8vIFNob3cgcmVjb25uZWN0aW9uIG1lc3NhZ2VcbiAgICAgIGlmIChvZmZsaW5lU3RhcnRUaW1lKSB7XG4gICAgICAgIGNvbnN0IG9mZmxpbmVEdXJhdGlvbiA9IE1hdGgucm91bmQoKERhdGUubm93KCkgLSBvZmZsaW5lU3RhcnRUaW1lLmdldFRpbWUoKSkgLyAxMDAwKVxuICAgICAgICBjb25zb2xlLmxvZyhg8J+TtiBSZWNvbm5lY3RlZCBhZnRlciAke29mZmxpbmVEdXJhdGlvbn0gc2Vjb25kcyBvZmZsaW5lYClcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCBoYW5kbGVPZmZsaW5lID0gKCkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ/Cfk7UgTmV0d29yazogT2ZmbGluZScpXG4gICAgICBzZXRJc09ubGluZShmYWxzZSlcbiAgICAgIHNldE9mZmxpbmVTdGFydFRpbWUobmV3IERhdGUoKSlcbiAgICAgIFxuICAgICAgLy8gU2hvdyBvZmZsaW5lIG1lc3NhZ2UgYWZ0ZXIgMiBzZWNvbmRzXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgaWYgKCFuYXZpZ2F0b3Iub25MaW5lKSB7XG4gICAgICAgICAgc2V0U2hvd09mZmxpbmVNZXNzYWdlKHRydWUpXG4gICAgICAgIH1cbiAgICAgIH0sIDIwMDApXG4gICAgfVxuXG4gICAgLy8gQWRkIGV2ZW50IGxpc3RlbmVycyAob25seSBvbiBjbGllbnQgc2lkZSlcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdvbmxpbmUnLCBoYW5kbGVPbmxpbmUpXG4gICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignb2ZmbGluZScsIGhhbmRsZU9mZmxpbmUpXG4gICAgfVxuXG4gICAgLy8gQ2xlYW51cFxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ29ubGluZScsIGhhbmRsZU9ubGluZSlcbiAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ29mZmxpbmUnLCBoYW5kbGVPZmZsaW5lKVxuICAgICAgfVxuICAgIH1cbiAgfSwgW29mZmxpbmVTdGFydFRpbWVdKVxuXG4gIC8vIE5vdGU6IFJlbW92ZWQgcGVyaW9kaWMgY29ubmVjdGl2aXR5IGNoZWNrIHRvIGF2b2lkIDQwNCBlcnJvcnNcbiAgLy8gQnJvd3NlcidzIG5hdGl2ZSBvbmxpbmUvb2ZmbGluZSBldmVudHMgYXJlIHN1ZmZpY2llbnQgZm9yIG1vc3QgY2FzZXNcblxuICBpZiAoc2hvd09mZmxpbmVNZXNzYWdlKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcHVycGxlLTkwMCB2aWEtYmx1ZS05MDAgdG8taW5kaWdvLTkwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy1tZCB3LWZ1bGxcIj5cbiAgICAgICAgICB7LyogT2ZmbGluZSBDYXJkICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvMTAgYmFja2Ryb3AtYmx1ci1sZyByb3VuZGVkLTJ4bCBwLTggdGV4dC1jZW50ZXIgYm9yZGVyIGJvcmRlci13aGl0ZS8yMFwiPlxuICAgICAgICAgICAgey8qIE9mZmxpbmUgSWNvbiAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byB3LTIwIGgtMjAgYmctcmVkLTUwMC8yMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWItNlwiPlxuICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJmYXMgZmEtd2lmaS1zbGFzaCB0ZXh0LXJlZC00MDAgdGV4dC0zeGxcIj48L2k+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgey8qIFRpdGxlICovfVxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5cbiAgICAgICAgICAgICAgWW91J3JlIE9mZmxpbmVcbiAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiBEZXNjcmlwdGlvbiAqL31cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvODAgbWItNiBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgWW91ciBpbnRlcm5ldCBjb25uZWN0aW9uIHNlZW1zIHRvIGJlIGRvd24uIERvbid0IHdvcnJ5IC0geW91ciB3b3JrIHByb2dyZXNzIGlzIHNhZmVseSBzYXZlZCBsb2NhbGx5IGFuZCB3aWxsIGJlIHJlc3RvcmVkIHdoZW4geW91J3JlIGJhY2sgb25saW5lLlxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7LyogU3RhdHVzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS81IHJvdW5kZWQtbGcgcC00IG1iLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvNjBcIj5Db25uZWN0aW9uIFN0YXR1czo8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNDAwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJmYXMgZmEtY2lyY2xlIHRleHQteHMgbXItMVwiPjwvaT5cbiAgICAgICAgICAgICAgICAgIE9mZmxpbmVcbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICB7b2ZmbGluZVN0YXJ0VGltZSAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gdGV4dC1zbSBtdC0yXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzYwXCI+T2ZmbGluZSBTaW5jZTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzgwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtvZmZsaW5lU3RhcnRUaW1lLnRvTG9jYWxlVGltZVN0cmluZygpfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICB7dXNlciAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gdGV4dC1zbSBtdC0yXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzYwXCI+VXNlciBTZXNzaW9uOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNDAwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgIDxpIGNsYXNzTmFtZT1cImZhcyBmYS1jaGVjayB0ZXh0LXhzIG1yLTFcIj48L2k+XG4gICAgICAgICAgICAgICAgICAgIFByb3RlY3RlZFxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiBGZWF0dXJlcyBBdmFpbGFibGUgT2ZmbGluZSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZWZ0IG1iLTZcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCBtYi0zIHRleHQtY2VudGVyXCI+QXZhaWxhYmxlIE9mZmxpbmU6PC9oMz5cbiAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMiB0ZXh0LXdoaXRlLzgwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxpIGNsYXNzTmFtZT1cImZhcyBmYS1jaGVjayB0ZXh0LWdyZWVuLTQwMCBtci0yXCI+PC9pPlxuICAgICAgICAgICAgICAgICAgQ29udGludWUgY3VycmVudCB0cmFuc2xhdGlvbiB3b3JrXG4gICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxpIGNsYXNzTmFtZT1cImZhcyBmYS1jaGVjayB0ZXh0LWdyZWVuLTQwMCBtci0yXCI+PC9pPlxuICAgICAgICAgICAgICAgICAgQXV0by1zYXZlIHByb2dyZXNzIGxvY2FsbHlcbiAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwiZmFzIGZhLWNoZWNrIHRleHQtZ3JlZW4tNDAwIG1yLTJcIj48L2k+XG4gICAgICAgICAgICAgICAgICBWaWV3IGNvbXBsZXRlZCB0cmFuc2xhdGlvbnNcbiAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwiZmFzIGZhLXRpbWVzIHRleHQtcmVkLTQwMCBtci0yXCI+PC9pPlxuICAgICAgICAgICAgICAgICAgU3VibWl0IHRyYW5zbGF0aW9ucyBmb3IgZWFybmluZ3NcbiAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiBBY3Rpb24gQnV0dG9ucyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB3aW5kb3cubG9jYXRpb24ucmVsb2FkKCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCBweS0zIHB4LTYgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJmYXMgZmEtc3luYy1hbHQgbXItMlwiPjwvaT5cbiAgICAgICAgICAgICAgICBDaGVjayBDb25uZWN0aW9uXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dPZmZsaW5lTWVzc2FnZShmYWxzZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXdoaXRlLzEwIGhvdmVyOmJnLXdoaXRlLzIwIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCBweS0zIHB4LTYgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyBib3JkZXIgYm9yZGVyLXdoaXRlLzIwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIENvbnRpbnVlIE9mZmxpbmVcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgey8qIFRpcHMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgcC00IGJnLXllbGxvdy01MDAvMTAgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLXllbGxvdy01MDAvMjBcIj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctMjAwIHRleHQteHNcIj5cbiAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJmYXMgZmEtbGlnaHRidWxiIG1yLTFcIj48L2k+XG4gICAgICAgICAgICAgICAgPHN0cm9uZz5UaXA6PC9zdHJvbmc+IFlvdXIgd29yayBpcyBhdXRvbWF0aWNhbGx5IHNhdmVkIGV2ZXJ5IDEwIHNlY29uZHMuIFdoZW4geW91IHJlY29ubmVjdCwgZXZlcnl0aGluZyB3aWxsIHN5bmMgYXV0b21hdGljYWxseS5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgLy8gU2hvdyBvbmxpbmUgY29udGVudCB3aXRoIG9mZmxpbmUgaW5kaWNhdG9yIGlmIG5lZWRlZFxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgIHsvKiBPZmZsaW5lIEluZGljYXRvciBCYXIgKi99XG4gICAgICB7IWlzT25saW5lICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCB0b3AtMCBsZWZ0LTAgcmlnaHQtMCBiZy1yZWQtNjAwIHRleHQtd2hpdGUgdGV4dC1jZW50ZXIgcHktMiB0ZXh0LXNtIHotNTBcIj5cbiAgICAgICAgICA8aSBjbGFzc05hbWU9XCJmYXMgZmEtd2lmaS1zbGFzaCBtci0yXCI+PC9pPlxuICAgICAgICAgIFlvdSdyZSBvZmZsaW5lIC0gd29yayBpcyBzYXZlZCBsb2NhbGx5XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd09mZmxpbmVNZXNzYWdlKHRydWUpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwibWwtNCB1bmRlcmxpbmUgaG92ZXI6bm8tdW5kZXJsaW5lXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICBWaWV3IERldGFpbHNcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgICAgXG4gICAgICB7LyogTWFpbiBDb250ZW50ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9eyFpc09ubGluZSA/ICdwdC0xMCcgOiAnJ30+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG5cbi8vIEhvb2sgZm9yIG9mZmxpbmUgc3RhdHVzXG5leHBvcnQgZnVuY3Rpb24gdXNlT2ZmbGluZVN0YXR1cygpIHtcbiAgY29uc3QgW2lzT25saW5lLCBzZXRJc09ubGluZV0gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbd2FzT2ZmbGluZSwgc2V0V2FzT2ZmbGluZV0gPSB1c2VTdGF0ZShmYWxzZSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh0eXBlb2YgbmF2aWdhdG9yICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgc2V0SXNPbmxpbmUobmF2aWdhdG9yLm9uTGluZSlcbiAgICB9XG5cbiAgICBjb25zdCBoYW5kbGVPbmxpbmUgPSAoKSA9PiB7XG4gICAgICBzZXRJc09ubGluZSh0cnVlKVxuICAgICAgaWYgKHdhc09mZmxpbmUpIHtcbiAgICAgICAgLy8gU2hvdyByZWNvbm5lY3Rpb24gbm90aWZpY2F0aW9uXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn4yQIFJlY29ubmVjdGVkIHRvIGludGVybmV0JylcbiAgICAgICAgc2V0V2FzT2ZmbGluZShmYWxzZSlcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCBoYW5kbGVPZmZsaW5lID0gKCkgPT4ge1xuICAgICAgc2V0SXNPbmxpbmUoZmFsc2UpXG4gICAgICBzZXRXYXNPZmZsaW5lKHRydWUpXG4gICAgfVxuXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignb25saW5lJywgaGFuZGxlT25saW5lKVxuICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ29mZmxpbmUnLCBoYW5kbGVPZmZsaW5lKVxuICAgIH1cblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ29ubGluZScsIGhhbmRsZU9ubGluZSlcbiAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ29mZmxpbmUnLCBoYW5kbGVPZmZsaW5lKVxuICAgICAgfVxuICAgIH1cbiAgfSwgW3dhc09mZmxpbmVdKVxuXG4gIHJldHVybiB7IGlzT25saW5lLCB3YXNPZmZsaW5lIH1cbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUF1dGhTdGF0ZSIsIk9mZmxpbmVTdXBwb3J0IiwiY2hpbGRyZW4iLCJpc09ubGluZSIsInNldElzT25saW5lIiwic2hvd09mZmxpbmVNZXNzYWdlIiwic2V0U2hvd09mZmxpbmVNZXNzYWdlIiwib2ZmbGluZVN0YXJ0VGltZSIsInNldE9mZmxpbmVTdGFydFRpbWUiLCJ1c2VyIiwibmF2aWdhdG9yIiwib25MaW5lIiwiaGFuZGxlT25saW5lIiwiY29uc29sZSIsImxvZyIsIm9mZmxpbmVEdXJhdGlvbiIsIk1hdGgiLCJyb3VuZCIsIkRhdGUiLCJub3ciLCJnZXRUaW1lIiwiaGFuZGxlT2ZmbGluZSIsInNldFRpbWVvdXQiLCJ3aW5kb3ciLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImRpdiIsImNsYXNzTmFtZSIsImkiLCJoMSIsInAiLCJzcGFuIiwidG9Mb2NhbGVUaW1lU3RyaW5nIiwiaDMiLCJ1bCIsImxpIiwiYnV0dG9uIiwib25DbGljayIsImxvY2F0aW9uIiwicmVsb2FkIiwic3Ryb25nIiwidXNlT2ZmbGluZVN0YXR1cyIsIndhc09mZmxpbmUiLCJzZXRXYXNPZmZsaW5lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/OfflineSupport.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PWAInstaller.tsx":
/*!*****************************************!*\
  !*** ./src/components/PWAInstaller.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PWAInstaller)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction PWAInstaller() {\n    const [deferredPrompt, setDeferredPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInstallButton, setShowInstallButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PWAInstaller.useEffect\": ()=>{\n            // Register service worker\n            if ('serviceWorker' in navigator) {\n                navigator.serviceWorker.register('/sw.js').then({\n                    \"PWAInstaller.useEffect\": (registration)=>{\n                        console.log('SW registered: ', registration);\n                    }\n                }[\"PWAInstaller.useEffect\"]).catch({\n                    \"PWAInstaller.useEffect\": (registrationError)=>{\n                        console.log('SW registration failed: ', registrationError);\n                    }\n                }[\"PWAInstaller.useEffect\"]);\n            }\n            // Listen for beforeinstallprompt event\n            const handleBeforeInstallPrompt = {\n                \"PWAInstaller.useEffect.handleBeforeInstallPrompt\": (e)=>{\n                    e.preventDefault();\n                    setDeferredPrompt(e);\n                    setShowInstallButton(true);\n                }\n            }[\"PWAInstaller.useEffect.handleBeforeInstallPrompt\"];\n            window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n            // Check if app is already installed\n            if (window.matchMedia('(display-mode: standalone)').matches) {\n                setShowInstallButton(false);\n            }\n            return ({\n                \"PWAInstaller.useEffect\": ()=>{\n                    window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n                }\n            })[\"PWAInstaller.useEffect\"];\n        }\n    }[\"PWAInstaller.useEffect\"], []);\n    const handleInstallClick = async ()=>{\n        if (!deferredPrompt) return;\n        deferredPrompt.prompt();\n        const { outcome } = await deferredPrompt.userChoice;\n        if (outcome === 'accepted') {\n            console.log('User accepted the install prompt');\n        } else {\n            console.log('User dismissed the install prompt');\n        }\n        setDeferredPrompt(null);\n        setShowInstallButton(false);\n    };\n    if (!showInstallButton) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleInstallClick,\n            className: \"glass-button px-4 py-3 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"fas fa-download mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\PWAInstaller.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                \"Install App\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\PWAInstaller.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\PWAInstaller.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PWAInstaller.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/UpdateManager.tsx":
/*!******************************************!*\
  !*** ./src/components/UpdateManager.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UpdateManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sweetalert2 */ \"(ssr)/./node_modules/sweetalert2/dist/sweetalert2.esm.all.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction UpdateManager({ children }) {\n    const [updateAvailable, setUpdateAvailable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [swRegistration, setSwRegistration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentVersion, setCurrentVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UpdateManager.useEffect\": ()=>{\n            // Register service worker\n            if ('serviceWorker' in navigator) {\n                registerServiceWorker();\n            }\n            // Listen for app updates\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"UpdateManager.useEffect\": ()=>{\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"UpdateManager.useEffect\"];\n        }\n    }[\"UpdateManager.useEffect\"], []);\n    const registerServiceWorker = async ()=>{\n        try {\n            const registration = await navigator.serviceWorker.register('/sw.js', {\n                scope: '/',\n                updateViaCache: 'none' // Always check for updates\n            });\n            setSwRegistration(registration);\n            console.log('✅ Service Worker registered successfully');\n            // Check for updates immediately\n            registration.update();\n            // Listen for service worker updates\n            registration.addEventListener('updatefound', ()=>{\n                const newWorker = registration.installing;\n                if (newWorker) {\n                    console.log('🔄 New service worker found, installing...');\n                    newWorker.addEventListener('statechange', ()=>{\n                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {\n                            console.log('✨ New service worker installed, update available');\n                            setUpdateAvailable(true);\n                            showUpdateNotification();\n                        }\n                    });\n                }\n            });\n            // Listen for messages from service worker\n            navigator.serviceWorker.addEventListener('message', (event)=>{\n                if (event.data && event.data.type === 'SW_UPDATED') {\n                    console.log('📱 Service worker updated to version:', event.data.version);\n                    setCurrentVersion(event.data.version);\n                }\n            });\n            // Check if there's a waiting service worker\n            if (registration.waiting) {\n                setUpdateAvailable(true);\n                showUpdateNotification();\n            }\n            // Get current version\n            getCurrentVersion();\n        } catch (error) {\n            console.error('❌ Service Worker registration failed:', error);\n        }\n    };\n    const getCurrentVersion = async ()=>{\n        if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {\n            try {\n                const messageChannel = new MessageChannel();\n                messageChannel.port1.onmessage = (event)=>{\n                    if (event.data && event.data.version) {\n                        setCurrentVersion(event.data.version);\n                        console.log('📱 Current app version:', event.data.version);\n                    }\n                };\n                navigator.serviceWorker.controller.postMessage({\n                    type: 'GET_VERSION'\n                }, [\n                    messageChannel.port2\n                ]);\n            } catch (error) {\n                console.error('Error getting version:', error);\n            }\n        }\n    };\n    const showUpdateNotification = ()=>{\n        sweetalert2__WEBPACK_IMPORTED_MODULE_2__[\"default\"].fire({\n            icon: 'info',\n            title: 'App Update Available!',\n            text: 'A new version of Instra Global is available. Update now for the latest features and improvements.',\n            showCancelButton: true,\n            confirmButtonText: 'Update Now',\n            cancelButtonText: 'Later',\n            confirmButtonColor: '#3b82f6',\n            cancelButtonColor: '#6b7280',\n            allowOutsideClick: false,\n            allowEscapeKey: false\n        }).then((result)=>{\n            if (result.isConfirmed) {\n                applyUpdate();\n            } else {\n                // Show reminder after 30 minutes\n                setTimeout(()=>{\n                    if (updateAvailable) {\n                        showUpdateReminder();\n                    }\n                }, 30 * 60 * 1000) // 30 minutes\n                ;\n            }\n        });\n    };\n    const showUpdateReminder = ()=>{\n        sweetalert2__WEBPACK_IMPORTED_MODULE_2__[\"default\"].fire({\n            icon: 'warning',\n            title: 'Update Reminder',\n            text: 'Please update the app to ensure optimal performance and security.',\n            confirmButtonText: 'Update Now',\n            confirmButtonColor: '#3b82f6',\n            timer: 10000,\n            timerProgressBar: true\n        }).then((result)=>{\n            if (result.isConfirmed) {\n                applyUpdate();\n            }\n        });\n    };\n    const applyUpdate = async ()=>{\n        if (!swRegistration || !swRegistration.waiting) {\n            console.log('No waiting service worker found');\n            return;\n        }\n        try {\n            // Show loading\n            sweetalert2__WEBPACK_IMPORTED_MODULE_2__[\"default\"].fire({\n                title: 'Updating App...',\n                text: 'Please wait while we update the app.',\n                allowOutsideClick: false,\n                allowEscapeKey: false,\n                showConfirmButton: false,\n                didOpen: ()=>{\n                    sweetalert2__WEBPACK_IMPORTED_MODULE_2__[\"default\"].showLoading();\n                }\n            });\n            // Tell the waiting service worker to skip waiting\n            swRegistration.waiting.postMessage({\n                type: 'SKIP_WAITING'\n            });\n            // Wait for the new service worker to take control\n            navigator.serviceWorker.addEventListener('controllerchange', ()=>{\n                console.log('🔄 New service worker took control, reloading...');\n                // Clear any cached data\n                if ('caches' in window) {\n                    caches.keys().then((cacheNames)=>{\n                        cacheNames.forEach((cacheName)=>{\n                            if (cacheName.includes('instra-global')) {\n                                caches.delete(cacheName);\n                            }\n                        });\n                    });\n                }\n                // Reload the page\n                window.location.reload();\n            });\n        } catch (error) {\n            console.error('Error applying update:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_2__[\"default\"].fire({\n                icon: 'error',\n                title: 'Update Failed',\n                text: 'Failed to update the app. Please refresh the page manually.',\n                confirmButtonText: 'Refresh Page'\n            }).then(()=>{\n                window.location.reload();\n            });\n        }\n    };\n    const handleBeforeUnload = ()=>{\n        // Save any pending data before page unload\n        console.log('💾 Saving data before page unload');\n    };\n    // Check for updates every 30 minutes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UpdateManager.useEffect\": ()=>{\n            const interval = setInterval({\n                \"UpdateManager.useEffect.interval\": ()=>{\n                    if (swRegistration) {\n                        console.log('🔍 Checking for app updates...');\n                        swRegistration.update();\n                    }\n                }\n            }[\"UpdateManager.useEffect.interval\"], 30 * 60 * 1000) // 30 minutes\n            ;\n            return ({\n                \"UpdateManager.useEffect\": ()=>clearInterval(interval)\n            })[\"UpdateManager.useEffect\"];\n        }\n    }[\"UpdateManager.useEffect\"], [\n        swRegistration\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            children,\n            updateAvailable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-4 right-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: applyUpdate,\n                    className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2 animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"fas fa-download\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\UpdateManager.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Update Available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\UpdateManager.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\UpdateManager.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\UpdateManager.tsx\",\n                lineNumber: 221,\n                columnNumber: 9\n            }, this),\n             true && currentVersion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-4 left-4 z-50 bg-gray-800 text-white px-2 py-1 rounded text-xs\",\n                children: [\n                    \"v\",\n                    currentVersion\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\UpdateManager.tsx\",\n                lineNumber: 234,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/UpdateManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthState: () => (/* binding */ useAuthState),\n/* harmony export */   useRequireAdmin: () => (/* binding */ useRequireAdmin),\n/* harmony export */   useRequireAuth: () => (/* binding */ useRequireAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/authUtils */ \"(ssr)/./src/lib/authUtils.ts\");\n/* harmony import */ var _lib_sessionManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/sessionManager */ \"(ssr)/./src/lib/sessionManager.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuthState,useRequireAuth,useRequireAdmin auto */ \n\n\n\n\nfunction useAuthState() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuthState.useEffect\": ()=>{\n            let retryCount = 0;\n            const maxRetries = 3;\n            const setupAuthListener = {\n                \"useAuthState.useEffect.setupAuthListener\": ()=>{\n                    try {\n                        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, {\n                            \"useAuthState.useEffect.setupAuthListener.unsubscribe\": (user)=>{\n                                console.log('Auth state changed:', user ? 'User logged in' : 'No user');\n                                setUser(user);\n                                setLoading(false);\n                                _lib_sessionManager__WEBPACK_IMPORTED_MODULE_4__.sessionManager.updateActivity();\n                                retryCount = 0 // Reset retry count on successful auth state change\n                                ;\n                            }\n                        }[\"useAuthState.useEffect.setupAuthListener.unsubscribe\"], {\n                            \"useAuthState.useEffect.setupAuthListener.unsubscribe\": (error)=>{\n                                console.error('Auth state listener error:', error);\n                                // Don't immediately set loading to false on error\n                                // Instead, retry a few times for network issues\n                                if (retryCount < maxRetries) {\n                                    retryCount++;\n                                    console.log(`Retrying auth listener (${retryCount}/${maxRetries})...`);\n                                    setTimeout(setupAuthListener, 2000 * retryCount) // Exponential backoff\n                                    ;\n                                } else {\n                                    console.error('Max auth retries reached, setting loading to false');\n                                    setLoading(false);\n                                }\n                            }\n                        }[\"useAuthState.useEffect.setupAuthListener.unsubscribe\"]);\n                        return unsubscribe;\n                    } catch (error) {\n                        console.error('Error setting up auth state listener:', error);\n                        setLoading(false);\n                        return ({\n                            \"useAuthState.useEffect.setupAuthListener\": ()=>{}\n                        })[\"useAuthState.useEffect.setupAuthListener\"];\n                    }\n                }\n            }[\"useAuthState.useEffect.setupAuthListener\"];\n            const unsubscribe = setupAuthListener();\n            // Set up network status monitoring\n            const handleOnline = {\n                \"useAuthState.useEffect.handleOnline\": ()=>{\n                    setIsOnline(true);\n                    console.log('Network: Back online');\n                }\n            }[\"useAuthState.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"useAuthState.useEffect.handleOffline\": ()=>{\n                    setIsOnline(false);\n                    console.log('Network: Offline');\n                }\n            }[\"useAuthState.useEffect.handleOffline\"];\n            window.addEventListener('online', handleOnline);\n            window.addEventListener('offline', handleOffline);\n            setIsOnline(navigator.onLine);\n            return ({\n                \"useAuthState.useEffect\": ()=>{\n                    unsubscribe();\n                    window.removeEventListener('online', handleOnline);\n                    window.removeEventListener('offline', handleOffline);\n                }\n            })[\"useAuthState.useEffect\"];\n        }\n    }[\"useAuthState.useEffect\"], []);\n    const signOut = async ()=>{\n        try {\n            await (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_3__.quickLogout)(user?.uid, '/');\n        } catch (error) {\n            console.error('Error signing out:', error);\n            // Force redirect on error\n            window.location.href = '/';\n        }\n    };\n    return {\n        user,\n        loading,\n        signOut,\n        isOnline\n    };\n}\nfunction useRequireAuth() {\n    const { user, loading, isOnline } = useAuthState();\n    const [redirectTimer, setRedirectTimer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRequireAuth.useEffect\": ()=>{\n            // Clear any existing redirect timer\n            if (redirectTimer) {\n                clearTimeout(redirectTimer);\n                setRedirectTimer(null);\n            }\n            if (!loading && !user) {\n                // If offline, don't redirect immediately - user might come back online\n                if (!isOnline) {\n                    console.log('User not authenticated but offline - waiting for network');\n                    return;\n                }\n                // If online and no user, wait a bit before redirecting (in case of temporary auth issues)\n                const timer = setTimeout({\n                    \"useRequireAuth.useEffect.timer\": ()=>{\n                        console.log('Redirecting to login - no authenticated user');\n                        window.location.href = '/login';\n                    }\n                }[\"useRequireAuth.useEffect.timer\"], 2000) // 2 second delay\n                ;\n                setRedirectTimer(timer);\n            }\n            return ({\n                \"useRequireAuth.useEffect\": ()=>{\n                    if (redirectTimer) {\n                        clearTimeout(redirectTimer);\n                    }\n                }\n            })[\"useRequireAuth.useEffect\"];\n        }\n    }[\"useRequireAuth.useEffect\"], [\n        user,\n        loading,\n        isOnline\n    ]);\n    return {\n        user,\n        loading,\n        isOnline\n    };\n}\nfunction useRequireAdmin() {\n    const { user, loading } = useAuthState();\n    const [isAdmin, setIsAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [adminLoading, setAdminLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRequireAdmin.useEffect\": ()=>{\n            if (!loading && !user) {\n                window.location.href = '/admin/login';\n                return;\n            }\n            if (user) {\n                // Check if user is admin\n                // This would typically involve checking a custom claim or database\n                // For now, we'll use a simple email check\n                const adminEmails = [\n                    '<EMAIL>',\n                    '<EMAIL>'\n                ];\n                const userIsAdmin = adminEmails.includes(user.email || '');\n                setIsAdmin(userIsAdmin);\n                setAdminLoading(false);\n                if (!userIsAdmin) {\n                    window.location.href = '/login';\n                }\n            }\n        }\n    }[\"useRequireAdmin.useEffect\"], [\n        user,\n        loading\n    ]);\n    return {\n        user,\n        loading: loading || adminLoading,\n        isAdmin\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/authUtils.ts":
/*!******************************!*\
  !*** ./src/lib/authUtils.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearExpiredSessions: () => (/* binding */ clearExpiredSessions),\n/* harmony export */   clearUserLocalStorage: () => (/* binding */ clearUserLocalStorage),\n/* harmony export */   getUserSessionInfo: () => (/* binding */ getUserSessionInfo),\n/* harmony export */   handleUserLogout: () => (/* binding */ handleUserLogout),\n/* harmony export */   quickLogout: () => (/* binding */ quickLogout)\n/* harmony export */ });\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! sweetalert2 */ \"(ssr)/./node_modules/sweetalert2/dist/sweetalert2.esm.all.js\");\n\n\n/**\n * Clear all user-specific data from localStorage\n */ function clearUserLocalStorage(userId) {\n    try {\n        // Get all localStorage keys\n        const keys = Object.keys(localStorage);\n        // Remove user-specific data\n        keys.forEach((key)=>{\n            if (key.includes(userId) || key.startsWith('video_session_') || key.startsWith('watch_times_') || key.startsWith('video_refresh_') || key.startsWith('video_change_notification_') || key.startsWith('leave_') || key.includes('mytube_') || key.includes('user_')) {\n                localStorage.removeItem(key);\n            }\n        });\n        // Also clear common app data\n        const commonKeys = [\n            'currentUser',\n            'authToken',\n            'userSession',\n            'appState',\n            'videoProgress',\n            'sessionData',\n            'workSession',\n            'walletCache',\n            'transactionCache'\n        ];\n        commonKeys.forEach((key)=>{\n            localStorage.removeItem(key);\n        });\n        console.log('Local storage cleared for user:', userId);\n    } catch (error) {\n        console.error('Error clearing local storage:', error);\n    }\n}\n/**\n * Handle user logout with confirmation and cleanup\n */ async function handleUserLogout(userId, redirectPath = '/login') {\n    try {\n        const result = await sweetalert2__WEBPACK_IMPORTED_MODULE_1__[\"default\"].fire({\n            title: 'Logout Confirmation',\n            text: 'Are you sure you want to logout?',\n            icon: 'question',\n            showCancelButton: true,\n            confirmButtonColor: '#ef4444',\n            cancelButtonColor: '#6b7280',\n            confirmButtonText: 'Yes, Logout',\n            cancelButtonText: 'Cancel'\n        });\n        if (result.isConfirmed) {\n            // Clear user-specific local storage data\n            if (userId) {\n                clearUserLocalStorage(userId);\n            }\n            // Sign out from Firebase\n            await _firebase__WEBPACK_IMPORTED_MODULE_0__.auth.signOut();\n            // Show success message\n            sweetalert2__WEBPACK_IMPORTED_MODULE_1__[\"default\"].fire({\n                icon: 'success',\n                title: 'Logged Out Successfully',\n                text: 'You have been logged out. Redirecting...',\n                timer: 2000,\n                showConfirmButton: false\n            }).then(()=>{\n                // Redirect to specified path\n                window.location.href = redirectPath;\n            });\n            return true;\n        }\n        return false;\n    } catch (error) {\n        console.error('Logout error:', error);\n        sweetalert2__WEBPACK_IMPORTED_MODULE_1__[\"default\"].fire({\n            icon: 'error',\n            title: 'Logout Failed',\n            text: 'There was an error logging out. Please try again.'\n        });\n        return false;\n    }\n}\n/**\n * Quick logout without confirmation (for emergency cases)\n */ async function quickLogout(userId, redirectPath = '/login') {\n    try {\n        // Clear user-specific local storage data\n        if (userId) {\n            clearUserLocalStorage(userId);\n        }\n        // Sign out from Firebase\n        await _firebase__WEBPACK_IMPORTED_MODULE_0__.auth.signOut();\n        // Redirect immediately\n        window.location.href = redirectPath;\n    } catch (error) {\n        console.error('Quick logout error:', error);\n        // Force redirect even if logout fails\n        window.location.href = redirectPath;\n    }\n}\n/**\n * Clear session data on app start (useful for cleanup)\n */ function clearExpiredSessions() {\n    try {\n        const keys = Object.keys(localStorage);\n        const today = new Date().toDateString();\n        keys.forEach((key)=>{\n            // Clear old session data (not from today)\n            if (key.startsWith('video_session_') || key.startsWith('watch_times_')) {\n                const storedData = localStorage.getItem(key);\n                if (storedData) {\n                    try {\n                        // Check if it's from today\n                        if (!key.includes(today)) {\n                            localStorage.removeItem(key);\n                            console.log('Cleared expired session:', key);\n                        }\n                    } catch (error) {\n                        // If we can't parse it, remove it\n                        localStorage.removeItem(key);\n                    }\n                }\n            }\n        });\n    } catch (error) {\n        console.error('Error clearing expired sessions:', error);\n    }\n}\n/**\n * Get user session info from localStorage\n */ function getUserSessionInfo(userId) {\n    try {\n        const today = new Date().toDateString();\n        const sessionKey = `video_session_${userId}_${today}`;\n        const watchTimesKey = `watch_times_${userId}_${today}`;\n        const sessionCount = localStorage.getItem(sessionKey);\n        const watchTimes = localStorage.getItem(watchTimesKey);\n        return {\n            videoCount: sessionCount ? parseInt(sessionCount) : 0,\n            watchTimes: watchTimes ? JSON.parse(watchTimes) : [],\n            hasActiveSession: !!(sessionCount || watchTimes)\n        };\n    } catch (error) {\n        console.error('Error getting session info:', error);\n        return {\n            videoCount: 0,\n            watchTimes: [],\n            hasActiveSession: false\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/authUtils.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   functions: () => (/* binding */ functions),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/firebase/storage/dist/index.mjs\");\n/* harmony import */ var firebase_functions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/functions */ \"(ssr)/./node_modules/firebase/functions/dist/index.mjs\");\n\n\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68\",\n    authDomain: \"instra-global.firebaseapp.com\",\n    projectId: \"instra-global\",\n    storageBucket: \"instra-global.firebasestorage.app\",\n    messagingSenderId: \"725774700748\",\n    appId: \"1:725774700748:web:4cdac03d835a7e2e133269\",\n    measurementId: \"G-QGHBLY3DLQ\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApp)() : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n// Initialize Firebase services\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n// Configure Firebase Functions for India region (asia-south1) for better performance\nconst functions = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_4__.getFunctions)(app, 'asia-south1');\n// Connect to emulators in development (if needed)\nif (false) {}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebaseFunctions.ts":
/*!**************************************!*\
  !*** ./src/lib/firebaseFunctions.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COST_OPTIMIZATION_STATS: () => (/* binding */ COST_OPTIMIZATION_STATS),\n/* harmony export */   areFirebaseFunctionsAvailable: () => (/* binding */ areFirebaseFunctionsAvailable),\n/* harmony export */   checkAuthenticationStatus: () => (/* binding */ checkAuthenticationStatus),\n/* harmony export */   getAdminDashboardData: () => (/* binding */ getAdminDashboardData),\n/* harmony export */   getEstimatedCostSavings: () => (/* binding */ getEstimatedCostSavings),\n/* harmony export */   getOptimizedAdminDashboardData: () => (/* binding */ getOptimizedAdminDashboardData),\n/* harmony export */   getOptimizedUserTransactions: () => (/* binding */ getOptimizedUserTransactions),\n/* harmony export */   getOptimizedUserWorkData: () => (/* binding */ getOptimizedUserWorkData),\n/* harmony export */   getPlatformStats: () => (/* binding */ getPlatformStats),\n/* harmony export */   getUserDashboardData: () => (/* binding */ getUserDashboardData),\n/* harmony export */   getUserTransactions: () => (/* binding */ getUserTransactions),\n/* harmony export */   getUserWorkData: () => (/* binding */ getUserWorkData),\n/* harmony export */   grantCopyPastePermission: () => (/* binding */ grantCopyPastePermission),\n/* harmony export */   processDailyActiveDays: () => (/* binding */ processDailyActiveDays),\n/* harmony export */   processDailyCopyPasteReduction: () => (/* binding */ processDailyCopyPasteReduction),\n/* harmony export */   processOptimizedWithdrawalRequest: () => (/* binding */ processOptimizedWithdrawalRequest),\n/* harmony export */   processWithdrawalRequest: () => (/* binding */ processWithdrawalRequest),\n/* harmony export */   submitOptimizedTranslationBatch: () => (/* binding */ submitOptimizedTranslationBatch),\n/* harmony export */   submitTranslationBatch: () => (/* binding */ submitTranslationBatch),\n/* harmony export */   updateUserPlan: () => (/* binding */ updateUserPlan)\n/* harmony export */ });\n/* harmony import */ var firebase_functions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/functions */ \"(ssr)/./node_modules/firebase/functions/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n\n\n// Utility function to check if user is authenticated\nasync function checkAuthenticationStatus() {\n    try {\n        const { auth } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\"));\n        const currentUser = auth.currentUser;\n        if (!currentUser) {\n            return {\n                isAuthenticated: false,\n                user: null,\n                tokenValid: false,\n                error: 'No user logged in'\n            };\n        }\n        // Check if token is valid\n        try {\n            const token = await currentUser.getIdToken(false);\n            return {\n                isAuthenticated: true,\n                user: {\n                    uid: currentUser.uid,\n                    email: currentUser.email,\n                    displayName: currentUser.displayName\n                },\n                tokenValid: !!token,\n                error: undefined\n            };\n        } catch (tokenError) {\n            return {\n                isAuthenticated: true,\n                user: {\n                    uid: currentUser.uid,\n                    email: currentUser.email,\n                    displayName: currentUser.displayName\n                },\n                tokenValid: false,\n                error: 'Token invalid or expired'\n            };\n        }\n    } catch (error) {\n        return {\n            isAuthenticated: false,\n            user: null,\n            tokenValid: false,\n            error: error.message || 'Authentication check failed'\n        };\n    }\n}\n// Utility function to ensure user authentication and refresh token\nasync function ensureAuthenticated() {\n    const { auth } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\"));\n    const currentUser = auth.currentUser;\n    if (!currentUser) {\n        console.error('❌ No current user found');\n        throw new Error('User not authenticated. Please log in again.');\n    }\n    // Check if user is still valid\n    try {\n        // First try to get the current token without forcing refresh\n        const currentToken = await currentUser.getIdToken(false);\n        if (!currentToken) {\n            console.error('❌ No ID token available');\n            throw new Error('Authentication token not available');\n        }\n        // Check token expiration (tokens expire after 1 hour)\n        const tokenResult = await currentUser.getIdTokenResult(false);\n        const expirationTime = new Date(tokenResult.expirationTime);\n        const now = new Date();\n        const timeUntilExpiry = expirationTime.getTime() - now.getTime();\n        // If token expires in less than 5 minutes, refresh it\n        if (timeUntilExpiry < 5 * 60 * 1000) {\n            console.log('🔄 Token expires soon, refreshing...');\n            await currentUser.getIdToken(true); // Force refresh\n            console.log('✅ User token refreshed proactively');\n        } else {\n            console.log('✅ User token is valid');\n        }\n    } catch (tokenError) {\n        console.error('❌ Error with user token:', tokenError);\n        // Try to refresh the token as a last resort\n        try {\n            console.log('🔄 Attempting token refresh...');\n            await currentUser.getIdToken(true); // Force refresh\n            console.log('✅ User token refreshed after error');\n        } catch (refreshError) {\n            console.error('❌ Failed to refresh token:', refreshError);\n            // If token refresh fails, the user needs to re-authenticate\n            if (refreshError.code === 'auth/user-token-expired' || refreshError.code === 'auth/invalid-user-token') {\n                throw new Error('Your session has expired. Please log in again.');\n            }\n            throw new Error('Authentication error. Please refresh the page and try again.');\n        }\n    }\n}\n// Firebase Functions for optimized Firestore operations\nconst getUserWorkDataFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'getUserWorkData');\nconst submitTranslationBatchFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'submitTranslationBatch');\nconst getUserDashboardDataFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'getUserDashboardData');\nconst getAdminDashboardDataFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'getAdminDashboardData');\nconst getUserTransactionsFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'getUserTransactions');\nconst processWithdrawalRequestFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'processWithdrawalRequest');\nconst processDailyActiveDaysFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'processDailyActiveDays');\nconst processDailyCopyPasteReductionFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'processDailyCopyPasteReduction');\nconst grantCopyPastePermissionFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'grantCopyPastePermission');\nconst updateUserPlanFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'updateUserPlan');\nconst getPlatformStatsFunction = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_0__.httpsCallable)(_firebase__WEBPACK_IMPORTED_MODULE_1__.functions, 'getPlatformStats');\n// Cost optimization tracking\nconst COST_OPTIMIZATION_STATS = {\n    functionsUsed: 0,\n    firestoreReadsAvoided: 0,\n    firestoreWritesOptimized: 0,\n    incrementFunctionUsage () {\n        this.functionsUsed++;\n        console.log(`🚀 Firebase Functions used: ${this.functionsUsed}`);\n    },\n    addReadsAvoided (count) {\n        this.firestoreReadsAvoided += count;\n        console.log(`💰 Firestore reads avoided: ${count} (Total: ${this.firestoreReadsAvoided})`);\n    },\n    addWritesOptimized (count) {\n        this.firestoreWritesOptimized += count;\n        console.log(`⚡ Firestore writes optimized: ${count} (Total: ${this.firestoreWritesOptimized})`);\n    },\n    getStats () {\n        return {\n            functionsUsed: this.functionsUsed,\n            firestoreReadsAvoided: this.firestoreReadsAvoided,\n            firestoreWritesOptimized: this.firestoreWritesOptimized,\n            estimatedCostSavings: (this.firestoreReadsAvoided * 0.00036 + this.firestoreWritesOptimized * 0.00108).toFixed(4)\n        };\n    }\n};\n/**\n * Get comprehensive user work data with minimal Firestore reads\n * Replaces multiple separate data fetching operations\n */ async function getUserWorkData() {\n    let retryCount = 0;\n    const maxRetries = 2;\n    while(retryCount <= maxRetries){\n        try {\n            console.log(`🚀 Fetching user work data via Firebase Function... (attempt ${retryCount + 1}/${maxRetries + 1})`);\n            // Ensure user is authenticated and token is fresh\n            await ensureAuthenticated();\n            const result = await getUserWorkDataFunction();\n            const data = result.data;\n            // Track optimization\n            COST_OPTIMIZATION_STATS.incrementFunctionUsage();\n            COST_OPTIMIZATION_STATS.addReadsAvoided(4); // Avoided 4 separate Firestore reads\n            console.log('✅ User work data fetched successfully:', data);\n            return data;\n        } catch (error) {\n            console.error(`❌ Error fetching user work data (attempt ${retryCount + 1}):`, error);\n            // Check if it's an authentication error\n            if (error.code === 'unauthenticated' || error.message?.includes('User must be authenticated') || error.message?.includes('session has expired')) {\n                if (retryCount < maxRetries) {\n                    console.log('🔄 Authentication error, retrying with fresh token...');\n                    retryCount++;\n                    // Wait a bit before retrying\n                    await new Promise((resolve)=>setTimeout(resolve, 1000 * retryCount));\n                    continue;\n                } else {\n                    throw new Error('Authentication failed. Please refresh the page and log in again.');\n                }\n            }\n            // Check if it's a network error\n            if (error.code === 'unavailable' || error.message?.includes('network') || error.message?.includes('fetch')) {\n                if (retryCount < maxRetries) {\n                    console.log('🔄 Network error, retrying...');\n                    retryCount++;\n                    // Wait longer for network errors\n                    await new Promise((resolve)=>setTimeout(resolve, 2000 * retryCount));\n                    continue;\n                } else {\n                    throw new Error('Network error. Please check your connection and try again.');\n                }\n            }\n            // For other errors, don't retry\n            throw new Error(`Failed to fetch user work data: ${error.message || 'Unknown error'}`);\n        }\n    }\n    throw new Error('Failed to fetch user work data after multiple attempts');\n}\n/**\n * Submit translation batch with atomic transaction\n * Replaces multiple separate Firestore operations\n */ async function submitTranslationBatch(batchSize = 50) {\n    try {\n        console.log(`🚀 Submitting translation batch via Firebase Function: ${batchSize} translations`);\n        // Ensure user is authenticated and token is fresh\n        await ensureAuthenticated();\n        const result = await submitTranslationBatchFunction({\n            batchSize\n        });\n        const data = result.data;\n        // Track optimization\n        COST_OPTIMIZATION_STATS.incrementFunctionUsage();\n        COST_OPTIMIZATION_STATS.addWritesOptimized(3); // Avoided 3 separate write operations\n        console.log('✅ Translation batch submitted successfully:', data);\n        return data;\n    } catch (error) {\n        console.error('❌ Error submitting translation batch:', error);\n        // If it's an authentication error, provide more specific error message\n        if (error.code === 'unauthenticated' || error.message?.includes('User must be authenticated')) {\n            throw new Error('Authentication required. Please refresh the page and try again.');\n        }\n        // If it's a permission error, provide helpful message\n        if (error.code === 'permission-denied') {\n            throw new Error('Permission denied. Please check your account status.');\n        }\n        throw new Error('Failed to submit translation batch');\n    }\n}\n/**\n * Get user dashboard data with minimal reads\n * Combines profile, wallet, and recent transactions\n */ async function getUserDashboardData() {\n    let retryCount = 0;\n    const maxRetries = 2;\n    while(retryCount <= maxRetries){\n        try {\n            console.log(`🚀 Fetching user dashboard data via Firebase Function... (attempt ${retryCount + 1}/${maxRetries + 1})`);\n            // Ensure user is authenticated and token is fresh\n            await ensureAuthenticated();\n            const result = await getUserDashboardDataFunction();\n            const data = result.data;\n            // Track optimization\n            COST_OPTIMIZATION_STATS.incrementFunctionUsage();\n            COST_OPTIMIZATION_STATS.addReadsAvoided(3); // Avoided 3 separate reads (user + transactions query)\n            console.log('✅ User dashboard data fetched successfully:', data);\n            return data;\n        } catch (error) {\n            console.error(`❌ Error fetching user dashboard data (attempt ${retryCount + 1}):`, error);\n            // Check if it's an authentication error\n            if (error.code === 'unauthenticated' || error.message?.includes('User must be authenticated') || error.message?.includes('session has expired')) {\n                if (retryCount < maxRetries) {\n                    console.log('🔄 Authentication error, retrying with fresh token...');\n                    retryCount++;\n                    // Wait a bit before retrying\n                    await new Promise((resolve)=>setTimeout(resolve, 1000 * retryCount));\n                    continue;\n                } else {\n                    throw new Error('Authentication failed. Please refresh the page and log in again.');\n                }\n            }\n            // Check if it's a network error\n            if (error.code === 'unavailable' || error.message?.includes('network') || error.message?.includes('fetch')) {\n                if (retryCount < maxRetries) {\n                    console.log('🔄 Network error, retrying...');\n                    retryCount++;\n                    // Wait longer for network errors\n                    await new Promise((resolve)=>setTimeout(resolve, 2000 * retryCount));\n                    continue;\n                } else {\n                    throw new Error('Network error. Please check your connection and try again.');\n                }\n            }\n            // For other errors, don't retry\n            throw new Error(`Failed to fetch user dashboard data: ${error.message || 'Unknown error'}`);\n        }\n    }\n    throw new Error('Failed to fetch user dashboard data after multiple attempts');\n}\n/**\n * Get admin dashboard data with aggregated statistics\n */ async function getAdminDashboardData() {\n    try {\n        console.log('🚀 Fetching admin dashboard data via Firebase Function...');\n        const result = await getAdminDashboardDataFunction();\n        const data = result.data;\n        // Track optimization\n        COST_OPTIMIZATION_STATS.incrementFunctionUsage();\n        COST_OPTIMIZATION_STATS.addReadsAvoided(15); // Avoided multiple collection scans\n        console.log('✅ Admin dashboard data fetched successfully:', data);\n        return data;\n    } catch (error) {\n        console.error('❌ Error fetching admin dashboard data:', error);\n        throw new Error('Failed to fetch admin dashboard data');\n    }\n}\n/**\n * Get user transactions with server-side pagination\n */ async function getUserTransactions(limit = 20, startAfter) {\n    try {\n        console.log(`🚀 Fetching user transactions via Firebase Function: limit=${limit}`);\n        const result = await getUserTransactionsFunction({\n            limit,\n            startAfter\n        });\n        const data = result.data;\n        // Track optimization\n        COST_OPTIMIZATION_STATS.incrementFunctionUsage();\n        COST_OPTIMIZATION_STATS.addReadsAvoided(Math.max(0, 100 - limit)); // Avoided reading all transactions\n        console.log('✅ User transactions fetched successfully:', data);\n        return data;\n    } catch (error) {\n        console.error('❌ Error fetching user transactions:', error);\n        throw new Error('Failed to fetch user transactions');\n    }\n}\n/**\n * Process withdrawal request with validation\n */ async function processWithdrawalRequest(amount, upiId) {\n    try {\n        console.log(`🚀 Processing withdrawal request via Firebase Function: ₹${amount}`);\n        const result = await processWithdrawalRequestFunction({\n            amount,\n            upiId\n        });\n        const data = result.data;\n        // Track optimization\n        COST_OPTIMIZATION_STATS.incrementFunctionUsage();\n        COST_OPTIMIZATION_STATS.addWritesOptimized(2); // Atomic transaction instead of separate writes\n        console.log('✅ Withdrawal request processed successfully:', data);\n        return data;\n    } catch (error) {\n        console.error('❌ Error processing withdrawal request:', error);\n        throw new Error('Failed to process withdrawal request');\n    }\n}\n/**\n * Admin function: Process daily active days for all users\n */ async function processDailyActiveDays() {\n    try {\n        console.log('🚀 Processing daily active days via Firebase Function...');\n        const result = await processDailyActiveDaysFunction();\n        const data = result.data;\n        // Track optimization\n        COST_OPTIMIZATION_STATS.incrementFunctionUsage();\n        COST_OPTIMIZATION_STATS.addWritesOptimized(data.updated || 0); // Batch processing\n        console.log('✅ Daily active days processed successfully:', data);\n        return data;\n    } catch (error) {\n        console.error('❌ Error processing daily active days:', error);\n        throw new Error('Failed to process daily active days');\n    }\n}\n/**\n * Admin function: Process daily copy-paste reduction for all users\n */ async function processDailyCopyPasteReduction() {\n    try {\n        console.log('🚀 Processing daily copy-paste reduction via Firebase Function...');\n        const result = await processDailyCopyPasteReductionFunction();\n        const data = result.data;\n        // Track optimization\n        COST_OPTIMIZATION_STATS.incrementFunctionUsage();\n        COST_OPTIMIZATION_STATS.addWritesOptimized(data.reduced || 0); // Batch processing\n        console.log('✅ Daily copy-paste reduction processed successfully:', data);\n        return data;\n    } catch (error) {\n        console.error('❌ Error processing daily copy-paste reduction:', error);\n        throw new Error('Failed to process daily copy-paste reduction');\n    }\n}\n/**\n * Admin function: Grant copy-paste permission to user\n */ async function grantCopyPastePermission(userId, days) {\n    try {\n        console.log(`🚀 Granting copy-paste permission via Firebase Function: ${days} days to ${userId}`);\n        const result = await grantCopyPastePermissionFunction({\n            userId,\n            days\n        });\n        const data = result.data;\n        // Track optimization\n        COST_OPTIMIZATION_STATS.incrementFunctionUsage();\n        console.log('✅ Copy-paste permission granted successfully:', data);\n        return data;\n    } catch (error) {\n        console.error('❌ Error granting copy-paste permission:', error);\n        throw new Error('Failed to grant copy-paste permission');\n    }\n}\n/**\n * Admin function: Update user plan\n */ async function updateUserPlan(userId, plan, duration) {\n    try {\n        console.log(`🚀 Updating user plan via Firebase Function: ${plan} for ${userId}`);\n        const result = await updateUserPlanFunction({\n            userId,\n            plan,\n            duration\n        });\n        const data = result.data;\n        // Track optimization\n        COST_OPTIMIZATION_STATS.incrementFunctionUsage();\n        console.log('✅ User plan updated successfully:', data);\n        return data;\n    } catch (error) {\n        console.error('❌ Error updating user plan:', error);\n        throw new Error('Failed to update user plan');\n    }\n}\n/**\n * Admin function: Get platform statistics\n */ async function getPlatformStats() {\n    try {\n        console.log('🚀 Fetching platform stats via Firebase Function...');\n        const result = await getPlatformStatsFunction();\n        const data = result.data;\n        // Track optimization\n        COST_OPTIMIZATION_STATS.incrementFunctionUsage();\n        COST_OPTIMIZATION_STATS.addReadsAvoided(10); // Avoided multiple collection scans\n        console.log('✅ Platform stats fetched successfully:', data);\n        return data;\n    } catch (error) {\n        console.error('❌ Error fetching platform stats:', error);\n        throw new Error('Failed to fetch platform stats');\n    }\n}\n/**\n * Get optimized user work data with minimal Firestore reads\n * Replaces multiple separate API calls with a single function call\n */ async function getOptimizedUserWorkData() {\n    try {\n        console.log('🚀 Getting optimized user work data...');\n        // Ensure user is authenticated and token is fresh\n        await ensureAuthenticated();\n        const result = await getUserWorkDataFunction();\n        console.log('✅ Optimized user work data retrieved');\n        return result.data;\n    } catch (error) {\n        console.error('❌ Error getting optimized user work data:', error);\n        throw error;\n    }\n}\n/**\n * Submit translation batch with optimized writes\n * Combines multiple operations into a single atomic transaction\n */ async function submitOptimizedTranslationBatch(batchSize = 50) {\n    try {\n        console.log(`🚀 Submitting optimized translation batch: ${batchSize} translations`);\n        const result = await submitTranslationBatchFunction({\n            batchSize\n        });\n        console.log('✅ Optimized translation batch submitted');\n        return result.data;\n    } catch (error) {\n        console.error('❌ Error submitting optimized translation batch:', error);\n        throw error;\n    }\n}\n/**\n * Get optimized admin dashboard data with minimal reads\n * Aggregates data from multiple collections in a single call\n */ async function getOptimizedAdminDashboardData() {\n    try {\n        console.log('🚀 Getting optimized admin dashboard data...');\n        const result = await getAdminDashboardDataFunction();\n        console.log('✅ Optimized admin dashboard data retrieved');\n        return result.data;\n    } catch (error) {\n        console.error('❌ Error getting optimized admin dashboard data:', error);\n        throw error;\n    }\n}\n/**\n * Get user transactions with server-side pagination\n * Reduces client-side data processing and network overhead\n */ async function getOptimizedUserTransactions(limit = 20, lastDocId = null, type = null) {\n    try {\n        console.log(`🚀 Getting optimized user transactions: limit=${limit}, type=${type}`);\n        const result = await getUserTransactionsFunction({\n            limit,\n            lastDocId,\n            type\n        });\n        console.log('✅ Optimized user transactions retrieved');\n        return result.data;\n    } catch (error) {\n        console.error('❌ Error getting optimized user transactions:', error);\n        throw error;\n    }\n}\n/**\n * Process withdrawal request with optimized validation and writes\n * Combines validation, wallet debit, and record creation in a single transaction\n */ async function processOptimizedWithdrawalRequest(amount, bankDetails) {\n    try {\n        console.log(`🚀 Processing optimized withdrawal request: ₹${amount}`);\n        const result = await processWithdrawalRequestFunction({\n            amount,\n            bankDetails\n        });\n        console.log('✅ Optimized withdrawal request processed');\n        return result.data;\n    } catch (error) {\n        console.error('❌ Error processing optimized withdrawal request:', error);\n        throw error;\n    }\n}\n/**\n * Utility function to check if Firebase Functions are available\n * Useful for fallback to direct Firestore operations if needed\n */ function areFirebaseFunctionsAvailable() {\n    try {\n        return !!_firebase__WEBPACK_IMPORTED_MODULE_1__.functions;\n    } catch (error) {\n        console.warn('Firebase Functions not available:', error);\n        return false;\n    }\n}\n/**\n * Get estimated cost savings from using Firebase Functions\n * This is for monitoring and optimization purposes\n */ function getEstimatedCostSavings() {\n    return {\n        userWorkData: {\n            directReads: 5,\n            optimizedReads: 1,\n            savings: '80% reduction in reads'\n        },\n        translationBatch: {\n            directWrites: 3,\n            optimizedWrites: 1,\n            savings: '67% reduction in writes'\n        },\n        adminDashboard: {\n            directReads: 15,\n            optimizedReads: 3,\n            savings: '80% reduction in reads'\n        },\n        userTransactions: {\n            directReads: 'Variable (all transactions)',\n            optimizedReads: 'Paginated (20 per call)',\n            savings: 'Up to 90% reduction for large datasets'\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebaseFunctions.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/sessionManager.ts":
/*!***********************************!*\
  !*** ./src/lib/sessionManager.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sessionManager: () => (/* binding */ sessionManager)\n/* harmony export */ });\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n// Session Manager - Handles authentication persistence and work progress preservation\n\n\nclass SessionManager {\n    constructor(){\n        this.sessionData = null;\n        this.heartbeatInterval = null;\n        this.saveInterval = null;\n        this.authStateListener = null;\n        this.onlineListener = null;\n        this.offlineListener = null;\n        this.initializeSession();\n    }\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    initializeSession() {\n        // Only initialize on client side\n        if (true) {\n            console.log('Session Manager: Server-side rendering detected, skipping initialization');\n            return;\n        }\n        // Generate unique session ID\n        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        // Initialize session data\n        this.sessionData = {\n            user: null,\n            lastActivity: new Date(),\n            isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,\n            sessionId\n        };\n        // Set up authentication state listener with persistence\n        this.setupAuthStateListener();\n        // Set up network status listeners\n        this.setupNetworkListeners();\n        // Set up periodic session save\n        this.setupPeriodicSave();\n        // Set up heartbeat to keep session alive\n        this.setupHeartbeat();\n        // Restore previous session if available\n        this.restoreSession();\n        console.log('Session Manager initialized with ID:', sessionId);\n    }\n    setupAuthStateListener() {\n        this.authStateListener = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_0__.auth, (user)=>{\n            console.log('Auth state changed:', user ? `User ${user.uid}` : 'No user');\n            if (this.sessionData) {\n                this.sessionData.user = user;\n                this.sessionData.lastActivity = new Date();\n                if (user) {\n                    // User logged in - restore their work progress\n                    this.restoreWorkProgress(user.uid);\n                } else {\n                    // User logged out - save current progress before clearing\n                    this.saveCurrentProgress();\n                }\n                this.saveSession();\n            }\n        }, (error)=>{\n            console.error('Auth state listener error:', error);\n        // Don't clear session on auth errors - might be temporary network issue\n        });\n    }\n    setupNetworkListeners() {\n        // Only set up listeners on client side\n        if (true) {\n            return;\n        }\n        this.onlineListener = ()=>{\n            console.log('Network: Online');\n            if (this.sessionData) {\n                this.sessionData.isOnline = true;\n                this.saveSession();\n                // Try to sync any pending data\n                this.syncPendingData();\n            }\n        };\n        this.offlineListener = ()=>{\n            console.log('Network: Offline');\n            if (this.sessionData) {\n                this.sessionData.isOnline = false;\n                this.saveSession();\n            }\n        };\n        window.addEventListener('online', this.onlineListener);\n        window.addEventListener('offline', this.offlineListener);\n    }\n    setupPeriodicSave() {\n        // Save session every 30 seconds\n        this.saveInterval = setInterval(()=>{\n            this.saveSession();\n            this.saveCurrentProgress();\n        }, 30000);\n    }\n    setupHeartbeat() {\n        // Update last activity every 60 seconds\n        this.heartbeatInterval = setInterval(()=>{\n            if (this.sessionData) {\n                this.sessionData.lastActivity = new Date();\n                this.saveSession();\n            }\n        }, 60000);\n    }\n    saveWorkProgress(progress) {\n        if (!this.sessionData?.user || \"undefined\" === 'undefined' || 0) {\n            return;\n        }\n        const workProgress = {\n            currentStep: progress.currentStep || null,\n            userTypedText: progress.userTypedText || '',\n            selectedLanguage: progress.selectedLanguage || '',\n            isTypingComplete: progress.isTypingComplete || false,\n            completedTranslations: progress.completedTranslations || 0,\n            batchProgress: progress.batchProgress || 0,\n            lastSaved: new Date(),\n            userId: this.sessionData.user.uid\n        };\n        this.sessionData.workProgress = workProgress;\n        this.saveSession();\n        // Also save to user-specific storage\n        try {\n            const progressKey = `work_progress_${this.sessionData.user.uid}`;\n            localStorage.setItem(progressKey, JSON.stringify(workProgress));\n            console.log('Work progress saved for user:', this.sessionData.user.uid);\n        } catch (error) {\n            console.error('Error saving work progress to localStorage:', error);\n        }\n    }\n    getWorkProgress() {\n        if (!this.sessionData?.user || \"undefined\" === 'undefined' || 0) {\n            return null;\n        }\n        // Try to get from session first\n        if (this.sessionData.workProgress) {\n            return this.sessionData.workProgress;\n        }\n        // Try to get from localStorage\n        try {\n            const progressKey = `work_progress_${this.sessionData.user.uid}`;\n            const saved = localStorage.getItem(progressKey);\n            if (saved) {\n                return JSON.parse(saved);\n            }\n        } catch (error) {\n            console.error('Error parsing saved work progress:', error);\n        }\n        return null;\n    }\n    clearWorkProgress() {\n        if (!this.sessionData?.user || \"undefined\" === 'undefined' || 0) {\n            return;\n        }\n        this.sessionData.workProgress = undefined;\n        try {\n            const progressKey = `work_progress_${this.sessionData.user.uid}`;\n            localStorage.removeItem(progressKey);\n            this.saveSession();\n            console.log('Work progress cleared for user:', this.sessionData.user.uid);\n        } catch (error) {\n            console.error('Error clearing work progress:', error);\n        }\n    }\n    restoreWorkProgress(userId) {\n        if (true) {\n            return;\n        }\n        try {\n            const progressKey = `work_progress_${userId}`;\n            const saved = localStorage.getItem(progressKey);\n            if (saved) {\n                const progress = JSON.parse(saved);\n                if (this.sessionData) {\n                    this.sessionData.workProgress = progress;\n                }\n                console.log('Work progress restored for user:', userId);\n            }\n        } catch (error) {\n            console.error('Error restoring work progress:', error);\n        }\n    }\n    saveCurrentProgress() {\n        // This will be called by work page to save current state\n        if (this.sessionData?.workProgress && \"undefined\" !== 'undefined' && 0) {}\n    }\n    saveSession() {\n        if (!this.sessionData || \"undefined\" === 'undefined' || 0) {\n            return;\n        }\n        try {\n            const sessionKey = 'instra_session';\n            const sessionToSave = {\n                ...this.sessionData,\n                user: this.sessionData.user ? {\n                    uid: this.sessionData.user.uid,\n                    email: this.sessionData.user.email,\n                    displayName: this.sessionData.user.displayName\n                } : null\n            };\n            localStorage.setItem(sessionKey, JSON.stringify(sessionToSave));\n        } catch (error) {\n            console.error('Error saving session:', error);\n        }\n    }\n    restoreSession() {\n        if (true) {\n            return;\n        }\n        try {\n            const sessionKey = 'instra_session';\n            const saved = localStorage.getItem(sessionKey);\n            if (saved) {\n                const restoredSession = JSON.parse(saved);\n                // Check if session is recent (within last 24 hours)\n                const lastActivity = new Date(restoredSession.lastActivity);\n                const now = new Date();\n                const hoursSinceLastActivity = (now.getTime() - lastActivity.getTime()) / (1000 * 60 * 60);\n                if (hoursSinceLastActivity < 24) {\n                    console.log('Restored session from:', lastActivity.toLocaleString());\n                    // Don't restore user object - let Firebase auth handle that\n                    if (this.sessionData) {\n                        this.sessionData.lastActivity = lastActivity;\n                        this.sessionData.isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;\n                    }\n                } else {\n                    console.log('Session expired, clearing old session');\n                    localStorage.removeItem(sessionKey);\n                }\n            }\n        } catch (error) {\n            console.error('Error restoring session:', error);\n        }\n    }\n    syncPendingData() {\n        // Sync any data that was saved while offline\n        console.log('Syncing pending data...');\n    // This can be expanded to sync work progress to server\n    }\n    updateActivity() {\n        if (this.sessionData) {\n            this.sessionData.lastActivity = new Date();\n        }\n    }\n    isOnline() {\n        if (typeof navigator !== 'undefined') {\n            return navigator.onLine;\n        }\n        return this.sessionData?.isOnline || true;\n    }\n    getSessionId() {\n        return this.sessionData?.sessionId || '';\n    }\n    getCurrentUser() {\n        return this.sessionData?.user || null;\n    }\n    cleanup() {\n        if (this.heartbeatInterval) {\n            clearInterval(this.heartbeatInterval);\n        }\n        if (this.saveInterval) {\n            clearInterval(this.saveInterval);\n        }\n        if (this.authStateListener) {\n            this.authStateListener();\n        }\n        if (false) {}\n        console.log('Session Manager cleaned up');\n    }\n}\n// Export singleton instance\nconst sessionManager = SessionManager.getInstance();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/sessionManager.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@firebase","vendor-chunks/undici","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/sweetalert2","vendor-chunks/@opentelemetry","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/idb","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth-test%2Fpage&page=%2Fauth-test%2Fpage&appPaths=%2Fauth-test%2Fpage&pagePath=private-next-app-dir%2Fauth-test%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();