(()=>{var e={};e.id=1607,e.ids=[1607,7878],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26734:(e,s,t)=>{Promise.resolve().then(t.bind(t,64791))},27878:(e,s,t)=>{"use strict";t.d(s,{Mk:()=>p,checkCopyPastePermission:()=>c,grantCopyPastePermission:()=>l,i7:()=>u,removeCopyPastePermission:()=>d});var r=t(33784),i=t(75535);let o={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},n={users:"users"};class a{static async checkCopyPastePermission(e){try{let s=await (0,i.x7)((0,i.H9)(r.db,n.users,e));if(!s.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let t=s.data()[o.quickTranslationAdvantageExpiry];if(!t)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let a=t.toDate(),c=new Date,l=a>c,d=l?Math.ceil((a.getTime()-c.getTime())/864e5):0;return{hasPermission:l,daysRemaining:d,expiryDate:a}}catch(s){return console.error(`Error checking copy-paste permission for user ${e}:`,s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,s){try{let t=new Date;t.setDate(t.getDate()+s);let a=(0,i.H9)(r.db,n.users,e);await (0,i.mZ)(a,{[o.quickTranslationAdvantageExpiry]:i.Dc.fromDate(t),[o.lastCopyPasteReduction]:i.Dc.now()}),console.log(`✅ Granted copy-paste permission to user ${e} for ${s} days (expires: ${t.toDateString()})`)}catch(s){throw console.error(`Error granting copy-paste permission to user ${e}:`,s),s}}static async removeCopyPastePermission(e){try{let s=(0,i.H9)(r.db,n.users,e);await (0,i.mZ)(s,{[o.quickTranslationAdvantageExpiry]:null}),console.log(`✅ Removed copy-paste permission from user ${e}`)}catch(s){throw console.error(`Error removing copy-paste permission from user ${e}:`,s),s}}static async reduceCopyPasteDays(e){try{let s=await (0,i.x7)((0,i.H9)(r.db,n.users,e));if(!s.exists())return{reduced:!1,daysRemaining:0,expired:!1};let t=s.data(),a=t[o.quickTranslationAdvantageExpiry],c=t[o.lastCopyPasteReduction];if(!a)return{reduced:!1,daysRemaining:0,expired:!1};let l=new Date().toDateString();if((c?c.toDate().toDateString():null)===l){let e=a.toDate(),s=new Date,t=Math.max(0,Math.ceil((e.getTime()-s.getTime())/864e5));return{reduced:!1,daysRemaining:t,expired:0===t}}let d=a.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let p=(0,i.H9)(r.db,n.users,e);if(u<=new Date)return await (0,i.mZ)(p,{[o.quickTranslationAdvantageExpiry]:null,[o.lastCopyPasteReduction]:i.Dc.now()}),console.log(`📅 Copy-paste permission expired for user ${e}`),{reduced:!0,daysRemaining:0,expired:!0};{await (0,i.mZ)(p,{[o.quickTranslationAdvantageExpiry]:i.Dc.fromDate(u),[o.lastCopyPasteReduction]:i.Dc.now()});let s=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log(`📅 Reduced copy-paste days for user ${e}: ${s} days remaining`),{reduced:!0,daysRemaining:s,expired:!1}}}catch(s){return console.error(`Error reducing copy-paste days for user ${e}:`,s),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,i.getDocs)((0,i.collection)(r.db,n.users)),s=0,t=0,o=0,c=0;for(let r of e.docs)try{s++;let e=await a.reduceCopyPasteDays(r.id);e.reduced&&(t++,e.expired&&o++)}catch(e){c++,console.error(`Error processing copy-paste reduction for user ${r.id}:`,e)}return console.log(`✅ Daily copy-paste reduction complete:`),console.log(`   - Processed: ${s} users`),console.log(`   - Reduced: ${t} users`),console.log(`   - Expired: ${o} users`),console.log(`   - Errors: ${c} users`),{processed:s,reduced:t,expired:o,errors:c}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let s=await a.checkCopyPastePermission(e);return{hasPermission:s.hasPermission,daysRemaining:s.daysRemaining,expiryDate:s.expiryDate?s.expiryDate.toDateString():null}}catch(s){return console.error(`Error getting copy-paste status for user ${e}:`,s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let c=a.checkCopyPastePermission,l=a.grantCopyPastePermission,d=a.removeCopyPastePermission,u=a.reduceCopyPasteDays,p=a.processAllUsersCopyPasteReduction;a.getCopyPasteStatus},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56998:(e,s,t)=>{Promise.resolve().then(t.bind(t,94073))},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64791:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(60687),i=t(43210),o=t(87979),n=t(27878);function a(){let{user:e,loading:s}=(0,o.Nu)(),[t,a]=(0,i.useState)(""),[c,l]=(0,i.useState)(null),[d,u]=(0,i.useState)(!1),p=async()=>{if(!t.trim())return void alert("Please enter a user ID");u(!0),l(null);try{console.log(`🧪 Testing copy-paste service for user: ${t}`),console.log("1. Testing checkCopyPastePermission...");let e=await (0,n.checkCopyPastePermission)(t.trim());console.log("✅ checkCopyPastePermission result:",e),console.log("2. Testing reduceCopyPasteDays...");let s=await (0,n.i7)(t.trim());console.log("✅ reduceCopyPasteDays result:",s),l({success:!0,permission:e,reduction:s,message:"Single user test completed successfully"})}catch(e){console.error("❌ Error in single user test:",e),l({success:!1,error:e.message,message:"Single user test failed"})}finally{u(!1)}},x=async()=>{u(!0),l(null);try{console.log("\uD83E\uDDEA Testing processAllUsersCopyPasteReduction...");let e=await (0,n.Mk)();console.log("✅ processAllUsersCopyPasteReduction result:",e),l({success:!0,allUsersResult:e,message:"All users test completed successfully"})}catch(e){console.error("❌ Error in all users test:",e),l({success:!1,error:e.message,stack:e.stack,message:"All users test failed"})}finally{u(!1)}};return s?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,r.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,r.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,r.jsxs)("div",{className:"glass-card p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Copy-Paste Service"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white mb-2",children:"User ID (for single user test):"}),(0,r.jsx)("input",{type:"text",value:t,onChange:e=>a(e.target.value),className:"w-full p-3 rounded-lg bg-white/10 text-white border border-white/20",placeholder:"Enter user ID"})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{onClick:p,disabled:d,className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:d?"Testing...":"Test Single User"}),(0,r.jsx)("button",{onClick:x,disabled:d,className:"flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:d?"Testing...":"Test All Users"})]}),c&&(0,r.jsxs)("div",{className:`p-4 rounded-lg ${c.success?"bg-green-500/20 border border-green-500/30":"bg-red-500/20 border border-red-500/30"}`,children:[(0,r.jsx)("h3",{className:`font-bold ${c.success?"text-green-400":"text-red-400"}`,children:c.success?"Success!":"Failed"}),(0,r.jsx)("p",{className:"text-white mt-2",children:c.message}),c.permission&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Permission Check:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(c.permission,null,2)})]}),c.reduction&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Reduction Result:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(c.reduction,null,2)})]}),c.allUsersResult&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"All Users Result:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(c.allUsersResult,null,2)})]}),c.error&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-red-500/20 rounded",children:[(0,r.jsx)("h4",{className:"text-red-400 font-semibold",children:"Error Details:"}),(0,r.jsx)("p",{className:"text-white/80 text-sm mt-1",children:c.error}),c.stack&&(0,r.jsx)("pre",{className:"text-white/60 text-xs mt-2 overflow-auto",children:c.stack})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 p-4 bg-white/5 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,r.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• Enter a user ID to test single user functions"}),(0,r.jsx)("li",{children:'• Click "Test Single User" to test permission check and reduction'}),(0,r.jsx)("li",{children:'• Click "Test All Users" to test the batch processing function'}),(0,r.jsx)("li",{children:"• Check browser console for detailed logs"})]})]})]})})})}},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88664:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>l});var r=t(65239),i=t(48088),o=t(88170),n=t.n(o),a=t(30893),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);t.d(s,c);let l={children:["",{children:["test-copy-paste-service",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94073)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-service\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-service\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-copy-paste-service/page",pathname:"/test-copy-paste-service",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},91645:e=>{"use strict";e.exports=require("net")},94073:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-copy-paste-service\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-service\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4573,6803],()=>t(88664));module.exports=r})();