"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sweetalert2";
exports.ids = ["vendor-chunks/sweetalert2"];
exports.modules = {

/***/ "(ssr)/./node_modules/sweetalert2/dist/sweetalert2.esm.all.js":
/*!**************************************************************!*\
  !*** ./node_modules/sweetalert2/dist/sweetalert2.esm.all.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Swal)\n/* harmony export */ });\n/*!\n* sweetalert2 v11.22.0\n* Released under the MIT License.\n*/\nfunction _assertClassBrand(e, t, n) {\n  if (\"function\" == typeof e ? e === t : e.has(t)) return arguments.length < 3 ? t : n;\n  throw new TypeError(\"Private element is not present on this object\");\n}\nfunction _checkPrivateRedeclaration(e, t) {\n  if (t.has(e)) throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n}\nfunction _classPrivateFieldGet2(s, a) {\n  return s.get(_assertClassBrand(s, a));\n}\nfunction _classPrivateFieldInitSpec(e, t, a) {\n  _checkPrivateRedeclaration(e, t), t.set(e, a);\n}\nfunction _classPrivateFieldSet2(s, a, r) {\n  return s.set(_assertClassBrand(s, a), r), r;\n}\n\nconst RESTORE_FOCUS_TIMEOUT = 100;\n\n/** @type {GlobalState} */\nconst globalState = {};\nconst focusPreviousActiveElement = () => {\n  if (globalState.previousActiveElement instanceof HTMLElement) {\n    globalState.previousActiveElement.focus();\n    globalState.previousActiveElement = null;\n  } else if (document.body) {\n    document.body.focus();\n  }\n};\n\n/**\n * Restore previous active (focused) element\n *\n * @param {boolean} returnFocus\n * @returns {Promise<void>}\n */\nconst restoreActiveElement = returnFocus => {\n  return new Promise(resolve => {\n    if (!returnFocus) {\n      return resolve();\n    }\n    const x = window.scrollX;\n    const y = window.scrollY;\n    globalState.restoreFocusTimeout = setTimeout(() => {\n      focusPreviousActiveElement();\n      resolve();\n    }, RESTORE_FOCUS_TIMEOUT); // issues/900\n\n    window.scrollTo(x, y);\n  });\n};\n\nconst swalPrefix = 'swal2-';\n\n/**\n * @typedef {Record<SwalClass, string>} SwalClasses\n */\n\n/**\n * @typedef {'success' | 'warning' | 'info' | 'question' | 'error'} SwalIcon\n * @typedef {Record<SwalIcon, string>} SwalIcons\n */\n\n/** @type {SwalClass[]} */\nconst classNames = ['container', 'shown', 'height-auto', 'iosfix', 'popup', 'modal', 'no-backdrop', 'no-transition', 'toast', 'toast-shown', 'show', 'hide', 'close', 'title', 'html-container', 'actions', 'confirm', 'deny', 'cancel', 'footer', 'icon', 'icon-content', 'image', 'input', 'file', 'range', 'select', 'radio', 'checkbox', 'label', 'textarea', 'inputerror', 'input-label', 'validation-message', 'progress-steps', 'active-progress-step', 'progress-step', 'progress-step-line', 'loader', 'loading', 'styled', 'top', 'top-start', 'top-end', 'top-left', 'top-right', 'center', 'center-start', 'center-end', 'center-left', 'center-right', 'bottom', 'bottom-start', 'bottom-end', 'bottom-left', 'bottom-right', 'grow-row', 'grow-column', 'grow-fullscreen', 'rtl', 'timer-progress-bar', 'timer-progress-bar-container', 'scrollbar-measure', 'icon-success', 'icon-warning', 'icon-info', 'icon-question', 'icon-error', 'draggable', 'dragging'];\nconst swalClasses = classNames.reduce((acc, className) => {\n  acc[className] = swalPrefix + className;\n  return acc;\n}, /** @type {SwalClasses} */{});\n\n/** @type {SwalIcon[]} */\nconst icons = ['success', 'warning', 'info', 'question', 'error'];\nconst iconTypes = icons.reduce((acc, icon) => {\n  acc[icon] = swalPrefix + icon;\n  return acc;\n}, /** @type {SwalIcons} */{});\n\nconst consolePrefix = 'SweetAlert2:';\n\n/**\n * Capitalize the first letter of a string\n *\n * @param {string} str\n * @returns {string}\n */\nconst capitalizeFirstLetter = str => str.charAt(0).toUpperCase() + str.slice(1);\n\n/**\n * Standardize console warnings\n *\n * @param {string | string[]} message\n */\nconst warn = message => {\n  console.warn(`${consolePrefix} ${typeof message === 'object' ? message.join(' ') : message}`);\n};\n\n/**\n * Standardize console errors\n *\n * @param {string} message\n */\nconst error = message => {\n  console.error(`${consolePrefix} ${message}`);\n};\n\n/**\n * Private global state for `warnOnce`\n *\n * @type {string[]}\n * @private\n */\nconst previousWarnOnceMessages = [];\n\n/**\n * Show a console warning, but only if it hasn't already been shown\n *\n * @param {string} message\n */\nconst warnOnce = message => {\n  if (!previousWarnOnceMessages.includes(message)) {\n    previousWarnOnceMessages.push(message);\n    warn(message);\n  }\n};\n\n/**\n * Show a one-time console warning about deprecated params/methods\n *\n * @param {string} deprecatedParam\n * @param {string?} useInstead\n */\nconst warnAboutDeprecation = (deprecatedParam, useInstead = null) => {\n  warnOnce(`\"${deprecatedParam}\" is deprecated and will be removed in the next major release.${useInstead ? ` Use \"${useInstead}\" instead.` : ''}`);\n};\n\n/**\n * If `arg` is a function, call it (with no arguments or context) and return the result.\n * Otherwise, just pass the value through\n *\n * @param {Function | any} arg\n * @returns {any}\n */\nconst callIfFunction = arg => typeof arg === 'function' ? arg() : arg;\n\n/**\n * @param {any} arg\n * @returns {boolean}\n */\nconst hasToPromiseFn = arg => arg && typeof arg.toPromise === 'function';\n\n/**\n * @param {any} arg\n * @returns {Promise<any>}\n */\nconst asPromise = arg => hasToPromiseFn(arg) ? arg.toPromise() : Promise.resolve(arg);\n\n/**\n * @param {any} arg\n * @returns {boolean}\n */\nconst isPromise = arg => arg && Promise.resolve(arg) === arg;\n\n/**\n * Gets the popup container which contains the backdrop and the popup itself.\n *\n * @returns {HTMLElement | null}\n */\nconst getContainer = () => document.body.querySelector(`.${swalClasses.container}`);\n\n/**\n * @param {string} selectorString\n * @returns {HTMLElement | null}\n */\nconst elementBySelector = selectorString => {\n  const container = getContainer();\n  return container ? container.querySelector(selectorString) : null;\n};\n\n/**\n * @param {string} className\n * @returns {HTMLElement | null}\n */\nconst elementByClass = className => {\n  return elementBySelector(`.${className}`);\n};\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getPopup = () => elementByClass(swalClasses.popup);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getIcon = () => elementByClass(swalClasses.icon);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getIconContent = () => elementByClass(swalClasses['icon-content']);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getTitle = () => elementByClass(swalClasses.title);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getHtmlContainer = () => elementByClass(swalClasses['html-container']);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getImage = () => elementByClass(swalClasses.image);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getProgressSteps = () => elementByClass(swalClasses['progress-steps']);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getValidationMessage = () => elementByClass(swalClasses['validation-message']);\n\n/**\n * @returns {HTMLButtonElement | null}\n */\nconst getConfirmButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.confirm}`));\n\n/**\n * @returns {HTMLButtonElement | null}\n */\nconst getCancelButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.cancel}`));\n\n/**\n * @returns {HTMLButtonElement | null}\n */\nconst getDenyButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.deny}`));\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getInputLabel = () => elementByClass(swalClasses['input-label']);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getLoader = () => elementBySelector(`.${swalClasses.loader}`);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getActions = () => elementByClass(swalClasses.actions);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getFooter = () => elementByClass(swalClasses.footer);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getTimerProgressBar = () => elementByClass(swalClasses['timer-progress-bar']);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getCloseButton = () => elementByClass(swalClasses.close);\n\n// https://github.com/jkup/focusable/blob/master/index.js\nconst focusable = `\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex=\"0\"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n`;\n/**\n * @returns {HTMLElement[]}\n */\nconst getFocusableElements = () => {\n  const popup = getPopup();\n  if (!popup) {\n    return [];\n  }\n  /** @type {NodeListOf<HTMLElement>} */\n  const focusableElementsWithTabindex = popup.querySelectorAll('[tabindex]:not([tabindex=\"-1\"]):not([tabindex=\"0\"])');\n  const focusableElementsWithTabindexSorted = Array.from(focusableElementsWithTabindex)\n  // sort according to tabindex\n  .sort((a, b) => {\n    const tabindexA = parseInt(a.getAttribute('tabindex') || '0');\n    const tabindexB = parseInt(b.getAttribute('tabindex') || '0');\n    if (tabindexA > tabindexB) {\n      return 1;\n    } else if (tabindexA < tabindexB) {\n      return -1;\n    }\n    return 0;\n  });\n\n  /** @type {NodeListOf<HTMLElement>} */\n  const otherFocusableElements = popup.querySelectorAll(focusable);\n  const otherFocusableElementsFiltered = Array.from(otherFocusableElements).filter(el => el.getAttribute('tabindex') !== '-1');\n  return [...new Set(focusableElementsWithTabindexSorted.concat(otherFocusableElementsFiltered))].filter(el => isVisible$1(el));\n};\n\n/**\n * @returns {boolean}\n */\nconst isModal = () => {\n  return hasClass(document.body, swalClasses.shown) && !hasClass(document.body, swalClasses['toast-shown']) && !hasClass(document.body, swalClasses['no-backdrop']);\n};\n\n/**\n * @returns {boolean}\n */\nconst isToast = () => {\n  const popup = getPopup();\n  if (!popup) {\n    return false;\n  }\n  return hasClass(popup, swalClasses.toast);\n};\n\n/**\n * @returns {boolean}\n */\nconst isLoading = () => {\n  const popup = getPopup();\n  if (!popup) {\n    return false;\n  }\n  return popup.hasAttribute('data-loading');\n};\n\n/**\n * Securely set innerHTML of an element\n * https://github.com/sweetalert2/sweetalert2/issues/1926\n *\n * @param {HTMLElement} elem\n * @param {string} html\n */\nconst setInnerHtml = (elem, html) => {\n  elem.textContent = '';\n  if (html) {\n    const parser = new DOMParser();\n    const parsed = parser.parseFromString(html, `text/html`);\n    const head = parsed.querySelector('head');\n    if (head) {\n      Array.from(head.childNodes).forEach(child => {\n        elem.appendChild(child);\n      });\n    }\n    const body = parsed.querySelector('body');\n    if (body) {\n      Array.from(body.childNodes).forEach(child => {\n        if (child instanceof HTMLVideoElement || child instanceof HTMLAudioElement) {\n          elem.appendChild(child.cloneNode(true)); // https://github.com/sweetalert2/sweetalert2/issues/2507\n        } else {\n          elem.appendChild(child);\n        }\n      });\n    }\n  }\n};\n\n/**\n * @param {HTMLElement} elem\n * @param {string} className\n * @returns {boolean}\n */\nconst hasClass = (elem, className) => {\n  if (!className) {\n    return false;\n  }\n  const classList = className.split(/\\s+/);\n  for (let i = 0; i < classList.length; i++) {\n    if (!elem.classList.contains(classList[i])) {\n      return false;\n    }\n  }\n  return true;\n};\n\n/**\n * @param {HTMLElement} elem\n * @param {SweetAlertOptions} params\n */\nconst removeCustomClasses = (elem, params) => {\n  Array.from(elem.classList).forEach(className => {\n    if (!Object.values(swalClasses).includes(className) && !Object.values(iconTypes).includes(className) && !Object.values(params.showClass || {}).includes(className)) {\n      elem.classList.remove(className);\n    }\n  });\n};\n\n/**\n * @param {HTMLElement} elem\n * @param {SweetAlertOptions} params\n * @param {string} className\n */\nconst applyCustomClass = (elem, params, className) => {\n  removeCustomClasses(elem, params);\n  if (!params.customClass) {\n    return;\n  }\n  const customClass = params.customClass[(/** @type {keyof SweetAlertCustomClass} */className)];\n  if (!customClass) {\n    return;\n  }\n  if (typeof customClass !== 'string' && !customClass.forEach) {\n    warn(`Invalid type of customClass.${className}! Expected string or iterable object, got \"${typeof customClass}\"`);\n    return;\n  }\n  addClass(elem, customClass);\n};\n\n/**\n * @param {HTMLElement} popup\n * @param {import('./renderers/renderInput').InputClass | SweetAlertInput} inputClass\n * @returns {HTMLInputElement | null}\n */\nconst getInput$1 = (popup, inputClass) => {\n  if (!inputClass) {\n    return null;\n  }\n  switch (inputClass) {\n    case 'select':\n    case 'textarea':\n    case 'file':\n      return popup.querySelector(`.${swalClasses.popup} > .${swalClasses[inputClass]}`);\n    case 'checkbox':\n      return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.checkbox} input`);\n    case 'radio':\n      return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.radio} input:checked`) || popup.querySelector(`.${swalClasses.popup} > .${swalClasses.radio} input:first-child`);\n    case 'range':\n      return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.range} input`);\n    default:\n      return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.input}`);\n  }\n};\n\n/**\n * @param {HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement} input\n */\nconst focusInput = input => {\n  input.focus();\n\n  // place cursor at end of text in text input\n  if (input.type !== 'file') {\n    // http://stackoverflow.com/a/2345915\n    const val = input.value;\n    input.value = '';\n    input.value = val;\n  }\n};\n\n/**\n * @param {HTMLElement | HTMLElement[] | null} target\n * @param {string | string[] | readonly string[] | undefined} classList\n * @param {boolean} condition\n */\nconst toggleClass = (target, classList, condition) => {\n  if (!target || !classList) {\n    return;\n  }\n  if (typeof classList === 'string') {\n    classList = classList.split(/\\s+/).filter(Boolean);\n  }\n  classList.forEach(className => {\n    if (Array.isArray(target)) {\n      target.forEach(elem => {\n        if (condition) {\n          elem.classList.add(className);\n        } else {\n          elem.classList.remove(className);\n        }\n      });\n    } else {\n      if (condition) {\n        target.classList.add(className);\n      } else {\n        target.classList.remove(className);\n      }\n    }\n  });\n};\n\n/**\n * @param {HTMLElement | HTMLElement[] | null} target\n * @param {string | string[] | readonly string[] | undefined} classList\n */\nconst addClass = (target, classList) => {\n  toggleClass(target, classList, true);\n};\n\n/**\n * @param {HTMLElement | HTMLElement[] | null} target\n * @param {string | string[] | readonly string[] | undefined} classList\n */\nconst removeClass = (target, classList) => {\n  toggleClass(target, classList, false);\n};\n\n/**\n * Get direct child of an element by class name\n *\n * @param {HTMLElement} elem\n * @param {string} className\n * @returns {HTMLElement | undefined}\n */\nconst getDirectChildByClass = (elem, className) => {\n  const children = Array.from(elem.children);\n  for (let i = 0; i < children.length; i++) {\n    const child = children[i];\n    if (child instanceof HTMLElement && hasClass(child, className)) {\n      return child;\n    }\n  }\n};\n\n/**\n * @param {HTMLElement} elem\n * @param {string} property\n * @param {*} value\n */\nconst applyNumericalStyle = (elem, property, value) => {\n  if (value === `${parseInt(value)}`) {\n    value = parseInt(value);\n  }\n  if (value || parseInt(value) === 0) {\n    elem.style.setProperty(property, typeof value === 'number' ? `${value}px` : value);\n  } else {\n    elem.style.removeProperty(property);\n  }\n};\n\n/**\n * @param {HTMLElement | null} elem\n * @param {string} display\n */\nconst show = (elem, display = 'flex') => {\n  if (!elem) {\n    return;\n  }\n  elem.style.display = display;\n};\n\n/**\n * @param {HTMLElement | null} elem\n */\nconst hide = elem => {\n  if (!elem) {\n    return;\n  }\n  elem.style.display = 'none';\n};\n\n/**\n * @param {HTMLElement | null} elem\n * @param {string} display\n */\nconst showWhenInnerHtmlPresent = (elem, display = 'block') => {\n  if (!elem) {\n    return;\n  }\n  new MutationObserver(() => {\n    toggle(elem, elem.innerHTML, display);\n  }).observe(elem, {\n    childList: true,\n    subtree: true\n  });\n};\n\n/**\n * @param {HTMLElement} parent\n * @param {string} selector\n * @param {string} property\n * @param {string} value\n */\nconst setStyle = (parent, selector, property, value) => {\n  /** @type {HTMLElement | null} */\n  const el = parent.querySelector(selector);\n  if (el) {\n    el.style.setProperty(property, value);\n  }\n};\n\n/**\n * @param {HTMLElement} elem\n * @param {any} condition\n * @param {string} display\n */\nconst toggle = (elem, condition, display = 'flex') => {\n  if (condition) {\n    show(elem, display);\n  } else {\n    hide(elem);\n  }\n};\n\n/**\n * borrowed from jquery $(elem).is(':visible') implementation\n *\n * @param {HTMLElement | null} elem\n * @returns {boolean}\n */\nconst isVisible$1 = elem => !!(elem && (elem.offsetWidth || elem.offsetHeight || elem.getClientRects().length));\n\n/**\n * @returns {boolean}\n */\nconst allButtonsAreHidden = () => !isVisible$1(getConfirmButton()) && !isVisible$1(getDenyButton()) && !isVisible$1(getCancelButton());\n\n/**\n * @param {HTMLElement} elem\n * @returns {boolean}\n */\nconst isScrollable = elem => !!(elem.scrollHeight > elem.clientHeight);\n\n/**\n * @param {HTMLElement} element\n * @param {HTMLElement} stopElement\n * @returns {boolean}\n */\nconst selfOrParentIsScrollable = (element, stopElement) => {\n  let parent = element;\n  while (parent && parent !== stopElement) {\n    if (isScrollable(parent)) {\n      return true;\n    }\n    parent = parent.parentElement;\n  }\n  return false;\n};\n\n/**\n * borrowed from https://stackoverflow.com/a/46352119\n *\n * @param {HTMLElement} elem\n * @returns {boolean}\n */\nconst hasCssAnimation = elem => {\n  const style = window.getComputedStyle(elem);\n  const animDuration = parseFloat(style.getPropertyValue('animation-duration') || '0');\n  const transDuration = parseFloat(style.getPropertyValue('transition-duration') || '0');\n  return animDuration > 0 || transDuration > 0;\n};\n\n/**\n * @param {number} timer\n * @param {boolean} reset\n */\nconst animateTimerProgressBar = (timer, reset = false) => {\n  const timerProgressBar = getTimerProgressBar();\n  if (!timerProgressBar) {\n    return;\n  }\n  if (isVisible$1(timerProgressBar)) {\n    if (reset) {\n      timerProgressBar.style.transition = 'none';\n      timerProgressBar.style.width = '100%';\n    }\n    setTimeout(() => {\n      timerProgressBar.style.transition = `width ${timer / 1000}s linear`;\n      timerProgressBar.style.width = '0%';\n    }, 10);\n  }\n};\nconst stopTimerProgressBar = () => {\n  const timerProgressBar = getTimerProgressBar();\n  if (!timerProgressBar) {\n    return;\n  }\n  const timerProgressBarWidth = parseInt(window.getComputedStyle(timerProgressBar).width);\n  timerProgressBar.style.removeProperty('transition');\n  timerProgressBar.style.width = '100%';\n  const timerProgressBarFullWidth = parseInt(window.getComputedStyle(timerProgressBar).width);\n  const timerProgressBarPercent = timerProgressBarWidth / timerProgressBarFullWidth * 100;\n  timerProgressBar.style.width = `${timerProgressBarPercent}%`;\n};\n\n/**\n * Detect Node env\n *\n * @returns {boolean}\n */\nconst isNodeEnv = () => typeof window === 'undefined' || typeof document === 'undefined';\n\nconst sweetHTML = `\n <div aria-labelledby=\"${swalClasses.title}\" aria-describedby=\"${swalClasses['html-container']}\" class=\"${swalClasses.popup}\" tabindex=\"-1\">\n   <button type=\"button\" class=\"${swalClasses.close}\"></button>\n   <ul class=\"${swalClasses['progress-steps']}\"></ul>\n   <div class=\"${swalClasses.icon}\"></div>\n   <img class=\"${swalClasses.image}\" />\n   <h2 class=\"${swalClasses.title}\" id=\"${swalClasses.title}\"></h2>\n   <div class=\"${swalClasses['html-container']}\" id=\"${swalClasses['html-container']}\"></div>\n   <input class=\"${swalClasses.input}\" id=\"${swalClasses.input}\" />\n   <input type=\"file\" class=\"${swalClasses.file}\" />\n   <div class=\"${swalClasses.range}\">\n     <input type=\"range\" />\n     <output></output>\n   </div>\n   <select class=\"${swalClasses.select}\" id=\"${swalClasses.select}\"></select>\n   <div class=\"${swalClasses.radio}\"></div>\n   <label class=\"${swalClasses.checkbox}\">\n     <input type=\"checkbox\" id=\"${swalClasses.checkbox}\" />\n     <span class=\"${swalClasses.label}\"></span>\n   </label>\n   <textarea class=\"${swalClasses.textarea}\" id=\"${swalClasses.textarea}\"></textarea>\n   <div class=\"${swalClasses['validation-message']}\" id=\"${swalClasses['validation-message']}\"></div>\n   <div class=\"${swalClasses.actions}\">\n     <div class=\"${swalClasses.loader}\"></div>\n     <button type=\"button\" class=\"${swalClasses.confirm}\"></button>\n     <button type=\"button\" class=\"${swalClasses.deny}\"></button>\n     <button type=\"button\" class=\"${swalClasses.cancel}\"></button>\n   </div>\n   <div class=\"${swalClasses.footer}\"></div>\n   <div class=\"${swalClasses['timer-progress-bar-container']}\">\n     <div class=\"${swalClasses['timer-progress-bar']}\"></div>\n   </div>\n </div>\n`.replace(/(^|\\n)\\s*/g, '');\n\n/**\n * @returns {boolean}\n */\nconst resetOldContainer = () => {\n  const oldContainer = getContainer();\n  if (!oldContainer) {\n    return false;\n  }\n  oldContainer.remove();\n  removeClass([document.documentElement, document.body], [swalClasses['no-backdrop'], swalClasses['toast-shown'], swalClasses['has-column']]);\n  return true;\n};\nconst resetValidationMessage$1 = () => {\n  globalState.currentInstance.resetValidationMessage();\n};\nconst addInputChangeListeners = () => {\n  const popup = getPopup();\n  const input = getDirectChildByClass(popup, swalClasses.input);\n  const file = getDirectChildByClass(popup, swalClasses.file);\n  /** @type {HTMLInputElement} */\n  const range = popup.querySelector(`.${swalClasses.range} input`);\n  /** @type {HTMLOutputElement} */\n  const rangeOutput = popup.querySelector(`.${swalClasses.range} output`);\n  const select = getDirectChildByClass(popup, swalClasses.select);\n  /** @type {HTMLInputElement} */\n  const checkbox = popup.querySelector(`.${swalClasses.checkbox} input`);\n  const textarea = getDirectChildByClass(popup, swalClasses.textarea);\n  input.oninput = resetValidationMessage$1;\n  file.onchange = resetValidationMessage$1;\n  select.onchange = resetValidationMessage$1;\n  checkbox.onchange = resetValidationMessage$1;\n  textarea.oninput = resetValidationMessage$1;\n  range.oninput = () => {\n    resetValidationMessage$1();\n    rangeOutput.value = range.value;\n  };\n  range.onchange = () => {\n    resetValidationMessage$1();\n    rangeOutput.value = range.value;\n  };\n};\n\n/**\n * @param {string | HTMLElement} target\n * @returns {HTMLElement}\n */\nconst getTarget = target => typeof target === 'string' ? document.querySelector(target) : target;\n\n/**\n * @param {SweetAlertOptions} params\n */\nconst setupAccessibility = params => {\n  const popup = getPopup();\n  popup.setAttribute('role', params.toast ? 'alert' : 'dialog');\n  popup.setAttribute('aria-live', params.toast ? 'polite' : 'assertive');\n  if (!params.toast) {\n    popup.setAttribute('aria-modal', 'true');\n  }\n};\n\n/**\n * @param {HTMLElement} targetElement\n */\nconst setupRTL = targetElement => {\n  if (window.getComputedStyle(targetElement).direction === 'rtl') {\n    addClass(getContainer(), swalClasses.rtl);\n  }\n};\n\n/**\n * Add modal + backdrop + no-war message for Russians to DOM\n *\n * @param {SweetAlertOptions} params\n */\nconst init = params => {\n  // Clean up the old popup container if it exists\n  const oldContainerExisted = resetOldContainer();\n  if (isNodeEnv()) {\n    error('SweetAlert2 requires document to initialize');\n    return;\n  }\n  const container = document.createElement('div');\n  container.className = swalClasses.container;\n  if (oldContainerExisted) {\n    addClass(container, swalClasses['no-transition']);\n  }\n  setInnerHtml(container, sweetHTML);\n  container.dataset['swal2Theme'] = params.theme;\n  const targetElement = getTarget(params.target);\n  targetElement.appendChild(container);\n  if (params.topLayer) {\n    container.setAttribute('popover', '');\n    container.showPopover();\n  }\n  setupAccessibility(params);\n  setupRTL(targetElement);\n  addInputChangeListeners();\n};\n\n/**\n * @param {HTMLElement | object | string} param\n * @param {HTMLElement} target\n */\nconst parseHtmlToContainer = (param, target) => {\n  // DOM element\n  if (param instanceof HTMLElement) {\n    target.appendChild(param);\n  }\n\n  // Object\n  else if (typeof param === 'object') {\n    handleObject(param, target);\n  }\n\n  // Plain string\n  else if (param) {\n    setInnerHtml(target, param);\n  }\n};\n\n/**\n * @param {any} param\n * @param {HTMLElement} target\n */\nconst handleObject = (param, target) => {\n  // JQuery element(s)\n  if (param.jquery) {\n    handleJqueryElem(target, param);\n  }\n\n  // For other objects use their string representation\n  else {\n    setInnerHtml(target, param.toString());\n  }\n};\n\n/**\n * @param {HTMLElement} target\n * @param {any} elem\n */\nconst handleJqueryElem = (target, elem) => {\n  target.textContent = '';\n  if (0 in elem) {\n    for (let i = 0; i in elem; i++) {\n      target.appendChild(elem[i].cloneNode(true));\n    }\n  } else {\n    target.appendChild(elem.cloneNode(true));\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderActions = (instance, params) => {\n  const actions = getActions();\n  const loader = getLoader();\n  if (!actions || !loader) {\n    return;\n  }\n\n  // Actions (buttons) wrapper\n  if (!params.showConfirmButton && !params.showDenyButton && !params.showCancelButton) {\n    hide(actions);\n  } else {\n    show(actions);\n  }\n\n  // Custom class\n  applyCustomClass(actions, params, 'actions');\n\n  // Render all the buttons\n  renderButtons(actions, loader, params);\n\n  // Loader\n  setInnerHtml(loader, params.loaderHtml || '');\n  applyCustomClass(loader, params, 'loader');\n};\n\n/**\n * @param {HTMLElement} actions\n * @param {HTMLElement} loader\n * @param {SweetAlertOptions} params\n */\nfunction renderButtons(actions, loader, params) {\n  const confirmButton = getConfirmButton();\n  const denyButton = getDenyButton();\n  const cancelButton = getCancelButton();\n  if (!confirmButton || !denyButton || !cancelButton) {\n    return;\n  }\n\n  // Render buttons\n  renderButton(confirmButton, 'confirm', params);\n  renderButton(denyButton, 'deny', params);\n  renderButton(cancelButton, 'cancel', params);\n  handleButtonsStyling(confirmButton, denyButton, cancelButton, params);\n  if (params.reverseButtons) {\n    if (params.toast) {\n      actions.insertBefore(cancelButton, confirmButton);\n      actions.insertBefore(denyButton, confirmButton);\n    } else {\n      actions.insertBefore(cancelButton, loader);\n      actions.insertBefore(denyButton, loader);\n      actions.insertBefore(confirmButton, loader);\n    }\n  }\n}\n\n/**\n * @param {HTMLElement} confirmButton\n * @param {HTMLElement} denyButton\n * @param {HTMLElement} cancelButton\n * @param {SweetAlertOptions} params\n */\nfunction handleButtonsStyling(confirmButton, denyButton, cancelButton, params) {\n  if (!params.buttonsStyling) {\n    removeClass([confirmButton, denyButton, cancelButton], swalClasses.styled);\n    return;\n  }\n  addClass([confirmButton, denyButton, cancelButton], swalClasses.styled);\n\n  // Apply custom background colors to action buttons\n  if (params.confirmButtonColor) {\n    confirmButton.style.setProperty('--swal2-confirm-button-background-color', params.confirmButtonColor);\n  }\n  if (params.denyButtonColor) {\n    denyButton.style.setProperty('--swal2-deny-button-background-color', params.denyButtonColor);\n  }\n  if (params.cancelButtonColor) {\n    cancelButton.style.setProperty('--swal2-cancel-button-background-color', params.cancelButtonColor);\n  }\n\n  // Apply the outline color to action buttons\n  applyOutlineColor(confirmButton);\n  applyOutlineColor(denyButton);\n  applyOutlineColor(cancelButton);\n}\n\n/**\n * @param {HTMLElement} button\n */\nfunction applyOutlineColor(button) {\n  const buttonStyle = window.getComputedStyle(button);\n  if (buttonStyle.getPropertyValue('--swal2-action-button-focus-box-shadow')) {\n    // If the button already has a custom outline color, no need to change it\n    return;\n  }\n  const outlineColor = buttonStyle.backgroundColor.replace(/rgba?\\((\\d+), (\\d+), (\\d+).*/, 'rgba($1, $2, $3, 0.5)');\n  button.style.setProperty('--swal2-action-button-focus-box-shadow', buttonStyle.getPropertyValue('--swal2-outline').replace(/ rgba\\(.*/, ` ${outlineColor}`));\n}\n\n/**\n * @param {HTMLElement} button\n * @param {'confirm' | 'deny' | 'cancel'} buttonType\n * @param {SweetAlertOptions} params\n */\nfunction renderButton(button, buttonType, params) {\n  const buttonName = /** @type {'Confirm' | 'Deny' | 'Cancel'} */capitalizeFirstLetter(buttonType);\n  toggle(button, params[`show${buttonName}Button`], 'inline-block');\n  setInnerHtml(button, params[`${buttonType}ButtonText`] || ''); // Set caption text\n  button.setAttribute('aria-label', params[`${buttonType}ButtonAriaLabel`] || ''); // ARIA label\n\n  // Add buttons custom classes\n  button.className = swalClasses[buttonType];\n  applyCustomClass(button, params, `${buttonType}Button`);\n}\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderCloseButton = (instance, params) => {\n  const closeButton = getCloseButton();\n  if (!closeButton) {\n    return;\n  }\n  setInnerHtml(closeButton, params.closeButtonHtml || '');\n\n  // Custom class\n  applyCustomClass(closeButton, params, 'closeButton');\n  toggle(closeButton, params.showCloseButton);\n  closeButton.setAttribute('aria-label', params.closeButtonAriaLabel || '');\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderContainer = (instance, params) => {\n  const container = getContainer();\n  if (!container) {\n    return;\n  }\n  handleBackdropParam(container, params.backdrop);\n  handlePositionParam(container, params.position);\n  handleGrowParam(container, params.grow);\n\n  // Custom class\n  applyCustomClass(container, params, 'container');\n};\n\n/**\n * @param {HTMLElement} container\n * @param {SweetAlertOptions['backdrop']} backdrop\n */\nfunction handleBackdropParam(container, backdrop) {\n  if (typeof backdrop === 'string') {\n    container.style.background = backdrop;\n  } else if (!backdrop) {\n    addClass([document.documentElement, document.body], swalClasses['no-backdrop']);\n  }\n}\n\n/**\n * @param {HTMLElement} container\n * @param {SweetAlertOptions['position']} position\n */\nfunction handlePositionParam(container, position) {\n  if (!position) {\n    return;\n  }\n  if (position in swalClasses) {\n    addClass(container, swalClasses[position]);\n  } else {\n    warn('The \"position\" parameter is not valid, defaulting to \"center\"');\n    addClass(container, swalClasses.center);\n  }\n}\n\n/**\n * @param {HTMLElement} container\n * @param {SweetAlertOptions['grow']} grow\n */\nfunction handleGrowParam(container, grow) {\n  if (!grow) {\n    return;\n  }\n  addClass(container, swalClasses[`grow-${grow}`]);\n}\n\n/**\n * This module contains `WeakMap`s for each effectively-\"private  property\" that a `Swal` has.\n * For example, to set the private property \"foo\" of `this` to \"bar\", you can `privateProps.foo.set(this, 'bar')`\n * This is the approach that Babel will probably take to implement private methods/fields\n *   https://github.com/tc39/proposal-private-methods\n *   https://github.com/babel/babel/pull/7555\n * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*\n *   then we can use that language feature.\n */\n\nvar privateProps = {\n  innerParams: new WeakMap(),\n  domCache: new WeakMap()\n};\n\n/// <reference path=\"../../../../sweetalert2.d.ts\"/>\n\n\n/** @type {InputClass[]} */\nconst inputClasses = ['input', 'file', 'range', 'select', 'radio', 'checkbox', 'textarea'];\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderInput = (instance, params) => {\n  const popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  const innerParams = privateProps.innerParams.get(instance);\n  const rerender = !innerParams || params.input !== innerParams.input;\n  inputClasses.forEach(inputClass => {\n    const inputContainer = getDirectChildByClass(popup, swalClasses[inputClass]);\n    if (!inputContainer) {\n      return;\n    }\n\n    // set attributes\n    setAttributes(inputClass, params.inputAttributes);\n\n    // set class\n    inputContainer.className = swalClasses[inputClass];\n    if (rerender) {\n      hide(inputContainer);\n    }\n  });\n  if (params.input) {\n    if (rerender) {\n      showInput(params);\n    }\n    // set custom class\n    setCustomClass(params);\n  }\n};\n\n/**\n * @param {SweetAlertOptions} params\n */\nconst showInput = params => {\n  if (!params.input) {\n    return;\n  }\n  if (!renderInputType[params.input]) {\n    error(`Unexpected type of input! Expected ${Object.keys(renderInputType).join(' | ')}, got \"${params.input}\"`);\n    return;\n  }\n  const inputContainer = getInputContainer(params.input);\n  if (!inputContainer) {\n    return;\n  }\n  const input = renderInputType[params.input](inputContainer, params);\n  show(inputContainer);\n\n  // input autofocus\n  if (params.inputAutoFocus) {\n    setTimeout(() => {\n      focusInput(input);\n    });\n  }\n};\n\n/**\n * @param {HTMLInputElement} input\n */\nconst removeAttributes = input => {\n  for (let i = 0; i < input.attributes.length; i++) {\n    const attrName = input.attributes[i].name;\n    if (!['id', 'type', 'value', 'style'].includes(attrName)) {\n      input.removeAttribute(attrName);\n    }\n  }\n};\n\n/**\n * @param {InputClass} inputClass\n * @param {SweetAlertOptions['inputAttributes']} inputAttributes\n */\nconst setAttributes = (inputClass, inputAttributes) => {\n  const popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  const input = getInput$1(popup, inputClass);\n  if (!input) {\n    return;\n  }\n  removeAttributes(input);\n  for (const attr in inputAttributes) {\n    input.setAttribute(attr, inputAttributes[attr]);\n  }\n};\n\n/**\n * @param {SweetAlertOptions} params\n */\nconst setCustomClass = params => {\n  if (!params.input) {\n    return;\n  }\n  const inputContainer = getInputContainer(params.input);\n  if (inputContainer) {\n    applyCustomClass(inputContainer, params, 'input');\n  }\n};\n\n/**\n * @param {HTMLInputElement | HTMLTextAreaElement} input\n * @param {SweetAlertOptions} params\n */\nconst setInputPlaceholder = (input, params) => {\n  if (!input.placeholder && params.inputPlaceholder) {\n    input.placeholder = params.inputPlaceholder;\n  }\n};\n\n/**\n * @param {Input} input\n * @param {Input} prependTo\n * @param {SweetAlertOptions} params\n */\nconst setInputLabel = (input, prependTo, params) => {\n  if (params.inputLabel) {\n    const label = document.createElement('label');\n    const labelClass = swalClasses['input-label'];\n    label.setAttribute('for', input.id);\n    label.className = labelClass;\n    if (typeof params.customClass === 'object') {\n      addClass(label, params.customClass.inputLabel);\n    }\n    label.innerText = params.inputLabel;\n    prependTo.insertAdjacentElement('beforebegin', label);\n  }\n};\n\n/**\n * @param {SweetAlertInput} inputType\n * @returns {HTMLElement | undefined}\n */\nconst getInputContainer = inputType => {\n  const popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  return getDirectChildByClass(popup, swalClasses[(/** @type {SwalClass} */inputType)] || swalClasses.input);\n};\n\n/**\n * @param {HTMLInputElement | HTMLOutputElement | HTMLTextAreaElement} input\n * @param {SweetAlertOptions['inputValue']} inputValue\n */\nconst checkAndSetInputValue = (input, inputValue) => {\n  if (['string', 'number'].includes(typeof inputValue)) {\n    input.value = `${inputValue}`;\n  } else if (!isPromise(inputValue)) {\n    warn(`Unexpected type of inputValue! Expected \"string\", \"number\" or \"Promise\", got \"${typeof inputValue}\"`);\n  }\n};\n\n/** @type {Record<SweetAlertInput, (input: Input | HTMLElement, params: SweetAlertOptions) => Input>} */\nconst renderInputType = {};\n\n/**\n * @param {HTMLInputElement} input\n * @param {SweetAlertOptions} params\n * @returns {HTMLInputElement}\n */\nrenderInputType.text = renderInputType.email = renderInputType.password = renderInputType.number = renderInputType.tel = renderInputType.url = renderInputType.search = renderInputType.date = renderInputType['datetime-local'] = renderInputType.time = renderInputType.week = renderInputType.month = /** @type {(input: Input | HTMLElement, params: SweetAlertOptions) => Input} */\n(input, params) => {\n  checkAndSetInputValue(input, params.inputValue);\n  setInputLabel(input, input, params);\n  setInputPlaceholder(input, params);\n  input.type = params.input;\n  return input;\n};\n\n/**\n * @param {HTMLInputElement} input\n * @param {SweetAlertOptions} params\n * @returns {HTMLInputElement}\n */\nrenderInputType.file = (input, params) => {\n  setInputLabel(input, input, params);\n  setInputPlaceholder(input, params);\n  return input;\n};\n\n/**\n * @param {HTMLInputElement} range\n * @param {SweetAlertOptions} params\n * @returns {HTMLInputElement}\n */\nrenderInputType.range = (range, params) => {\n  const rangeInput = range.querySelector('input');\n  const rangeOutput = range.querySelector('output');\n  checkAndSetInputValue(rangeInput, params.inputValue);\n  rangeInput.type = params.input;\n  checkAndSetInputValue(rangeOutput, params.inputValue);\n  setInputLabel(rangeInput, range, params);\n  return range;\n};\n\n/**\n * @param {HTMLSelectElement} select\n * @param {SweetAlertOptions} params\n * @returns {HTMLSelectElement}\n */\nrenderInputType.select = (select, params) => {\n  select.textContent = '';\n  if (params.inputPlaceholder) {\n    const placeholder = document.createElement('option');\n    setInnerHtml(placeholder, params.inputPlaceholder);\n    placeholder.value = '';\n    placeholder.disabled = true;\n    placeholder.selected = true;\n    select.appendChild(placeholder);\n  }\n  setInputLabel(select, select, params);\n  return select;\n};\n\n/**\n * @param {HTMLInputElement} radio\n * @returns {HTMLInputElement}\n */\nrenderInputType.radio = radio => {\n  radio.textContent = '';\n  return radio;\n};\n\n/**\n * @param {HTMLLabelElement} checkboxContainer\n * @param {SweetAlertOptions} params\n * @returns {HTMLInputElement}\n */\nrenderInputType.checkbox = (checkboxContainer, params) => {\n  const checkbox = getInput$1(getPopup(), 'checkbox');\n  checkbox.value = '1';\n  checkbox.checked = Boolean(params.inputValue);\n  const label = checkboxContainer.querySelector('span');\n  setInnerHtml(label, params.inputPlaceholder || params.inputLabel);\n  return checkbox;\n};\n\n/**\n * @param {HTMLTextAreaElement} textarea\n * @param {SweetAlertOptions} params\n * @returns {HTMLTextAreaElement}\n */\nrenderInputType.textarea = (textarea, params) => {\n  checkAndSetInputValue(textarea, params.inputValue);\n  setInputPlaceholder(textarea, params);\n  setInputLabel(textarea, textarea, params);\n\n  /**\n   * @param {HTMLElement} el\n   * @returns {number}\n   */\n  const getMargin = el => parseInt(window.getComputedStyle(el).marginLeft) + parseInt(window.getComputedStyle(el).marginRight);\n\n  // https://github.com/sweetalert2/sweetalert2/issues/2291\n  setTimeout(() => {\n    // https://github.com/sweetalert2/sweetalert2/issues/1699\n    if ('MutationObserver' in window) {\n      const initialPopupWidth = parseInt(window.getComputedStyle(getPopup()).width);\n      const textareaResizeHandler = () => {\n        // check if texarea is still in document (i.e. popup wasn't closed in the meantime)\n        if (!document.body.contains(textarea)) {\n          return;\n        }\n        const textareaWidth = textarea.offsetWidth + getMargin(textarea);\n        if (textareaWidth > initialPopupWidth) {\n          getPopup().style.width = `${textareaWidth}px`;\n        } else {\n          applyNumericalStyle(getPopup(), 'width', params.width);\n        }\n      };\n      new MutationObserver(textareaResizeHandler).observe(textarea, {\n        attributes: true,\n        attributeFilter: ['style']\n      });\n    }\n  });\n  return textarea;\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderContent = (instance, params) => {\n  const htmlContainer = getHtmlContainer();\n  if (!htmlContainer) {\n    return;\n  }\n  showWhenInnerHtmlPresent(htmlContainer);\n  applyCustomClass(htmlContainer, params, 'htmlContainer');\n\n  // Content as HTML\n  if (params.html) {\n    parseHtmlToContainer(params.html, htmlContainer);\n    show(htmlContainer, 'block');\n  }\n\n  // Content as plain text\n  else if (params.text) {\n    htmlContainer.textContent = params.text;\n    show(htmlContainer, 'block');\n  }\n\n  // No content\n  else {\n    hide(htmlContainer);\n  }\n  renderInput(instance, params);\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderFooter = (instance, params) => {\n  const footer = getFooter();\n  if (!footer) {\n    return;\n  }\n  showWhenInnerHtmlPresent(footer);\n  toggle(footer, params.footer, 'block');\n  if (params.footer) {\n    parseHtmlToContainer(params.footer, footer);\n  }\n\n  // Custom class\n  applyCustomClass(footer, params, 'footer');\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderIcon = (instance, params) => {\n  const innerParams = privateProps.innerParams.get(instance);\n  const icon = getIcon();\n  if (!icon) {\n    return;\n  }\n\n  // if the given icon already rendered, apply the styling without re-rendering the icon\n  if (innerParams && params.icon === innerParams.icon) {\n    // Custom or default content\n    setContent(icon, params);\n    applyStyles(icon, params);\n    return;\n  }\n  if (!params.icon && !params.iconHtml) {\n    hide(icon);\n    return;\n  }\n  if (params.icon && Object.keys(iconTypes).indexOf(params.icon) === -1) {\n    error(`Unknown icon! Expected \"success\", \"error\", \"warning\", \"info\" or \"question\", got \"${params.icon}\"`);\n    hide(icon);\n    return;\n  }\n  show(icon);\n\n  // Custom or default content\n  setContent(icon, params);\n  applyStyles(icon, params);\n\n  // Animate icon\n  addClass(icon, params.showClass && params.showClass.icon);\n\n  // Re-adjust the success icon on system theme change\n  const colorSchemeQueryList = window.matchMedia('(prefers-color-scheme: dark)');\n  colorSchemeQueryList.addEventListener('change', adjustSuccessIconBackgroundColor);\n};\n\n/**\n * @param {HTMLElement} icon\n * @param {SweetAlertOptions} params\n */\nconst applyStyles = (icon, params) => {\n  for (const [iconType, iconClassName] of Object.entries(iconTypes)) {\n    if (params.icon !== iconType) {\n      removeClass(icon, iconClassName);\n    }\n  }\n  addClass(icon, params.icon && iconTypes[params.icon]);\n\n  // Icon color\n  setColor(icon, params);\n\n  // Success icon background color\n  adjustSuccessIconBackgroundColor();\n\n  // Custom class\n  applyCustomClass(icon, params, 'icon');\n};\n\n// Adjust success icon background color to match the popup background color\nconst adjustSuccessIconBackgroundColor = () => {\n  const popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  const popupBackgroundColor = window.getComputedStyle(popup).getPropertyValue('background-color');\n  /** @type {NodeListOf<HTMLElement>} */\n  const successIconParts = popup.querySelectorAll('[class^=swal2-success-circular-line], .swal2-success-fix');\n  for (let i = 0; i < successIconParts.length; i++) {\n    successIconParts[i].style.backgroundColor = popupBackgroundColor;\n  }\n};\nconst successIconHtml = `\n  <div class=\"swal2-success-circular-line-left\"></div>\n  <span class=\"swal2-success-line-tip\"></span> <span class=\"swal2-success-line-long\"></span>\n  <div class=\"swal2-success-ring\"></div> <div class=\"swal2-success-fix\"></div>\n  <div class=\"swal2-success-circular-line-right\"></div>\n`;\nconst errorIconHtml = `\n  <span class=\"swal2-x-mark\">\n    <span class=\"swal2-x-mark-line-left\"></span>\n    <span class=\"swal2-x-mark-line-right\"></span>\n  </span>\n`;\n\n/**\n * @param {HTMLElement} icon\n * @param {SweetAlertOptions} params\n */\nconst setContent = (icon, params) => {\n  if (!params.icon && !params.iconHtml) {\n    return;\n  }\n  let oldContent = icon.innerHTML;\n  let newContent = '';\n  if (params.iconHtml) {\n    newContent = iconContent(params.iconHtml);\n  } else if (params.icon === 'success') {\n    newContent = successIconHtml;\n    oldContent = oldContent.replace(/ style=\".*?\"/g, ''); // undo adjustSuccessIconBackgroundColor()\n  } else if (params.icon === 'error') {\n    newContent = errorIconHtml;\n  } else if (params.icon) {\n    const defaultIconHtml = {\n      question: '?',\n      warning: '!',\n      info: 'i'\n    };\n    newContent = iconContent(defaultIconHtml[params.icon]);\n  }\n  if (oldContent.trim() !== newContent.trim()) {\n    setInnerHtml(icon, newContent);\n  }\n};\n\n/**\n * @param {HTMLElement} icon\n * @param {SweetAlertOptions} params\n */\nconst setColor = (icon, params) => {\n  if (!params.iconColor) {\n    return;\n  }\n  icon.style.color = params.iconColor;\n  icon.style.borderColor = params.iconColor;\n  for (const sel of ['.swal2-success-line-tip', '.swal2-success-line-long', '.swal2-x-mark-line-left', '.swal2-x-mark-line-right']) {\n    setStyle(icon, sel, 'background-color', params.iconColor);\n  }\n  setStyle(icon, '.swal2-success-ring', 'border-color', params.iconColor);\n};\n\n/**\n * @param {string} content\n * @returns {string}\n */\nconst iconContent = content => `<div class=\"${swalClasses['icon-content']}\">${content}</div>`;\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderImage = (instance, params) => {\n  const image = getImage();\n  if (!image) {\n    return;\n  }\n  if (!params.imageUrl) {\n    hide(image);\n    return;\n  }\n  show(image, '');\n\n  // Src, alt\n  image.setAttribute('src', params.imageUrl);\n  image.setAttribute('alt', params.imageAlt || '');\n\n  // Width, height\n  applyNumericalStyle(image, 'width', params.imageWidth);\n  applyNumericalStyle(image, 'height', params.imageHeight);\n\n  // Class\n  image.className = swalClasses.image;\n  applyCustomClass(image, params, 'image');\n};\n\nlet dragging = false;\nlet mousedownX = 0;\nlet mousedownY = 0;\nlet initialX = 0;\nlet initialY = 0;\n\n/**\n * @param {HTMLElement} popup\n */\nconst addDraggableListeners = popup => {\n  popup.addEventListener('mousedown', down);\n  document.body.addEventListener('mousemove', move);\n  popup.addEventListener('mouseup', up);\n  popup.addEventListener('touchstart', down);\n  document.body.addEventListener('touchmove', move);\n  popup.addEventListener('touchend', up);\n};\n\n/**\n * @param {HTMLElement} popup\n */\nconst removeDraggableListeners = popup => {\n  popup.removeEventListener('mousedown', down);\n  document.body.removeEventListener('mousemove', move);\n  popup.removeEventListener('mouseup', up);\n  popup.removeEventListener('touchstart', down);\n  document.body.removeEventListener('touchmove', move);\n  popup.removeEventListener('touchend', up);\n};\n\n/**\n * @param {MouseEvent | TouchEvent} event\n */\nconst down = event => {\n  const popup = getPopup();\n  if (event.target === popup || getIcon().contains(/** @type {HTMLElement} */event.target)) {\n    dragging = true;\n    const clientXY = getClientXY(event);\n    mousedownX = clientXY.clientX;\n    mousedownY = clientXY.clientY;\n    initialX = parseInt(popup.style.insetInlineStart) || 0;\n    initialY = parseInt(popup.style.insetBlockStart) || 0;\n    addClass(popup, 'swal2-dragging');\n  }\n};\n\n/**\n * @param {MouseEvent | TouchEvent} event\n */\nconst move = event => {\n  const popup = getPopup();\n  if (dragging) {\n    let {\n      clientX,\n      clientY\n    } = getClientXY(event);\n    popup.style.insetInlineStart = `${initialX + (clientX - mousedownX)}px`;\n    popup.style.insetBlockStart = `${initialY + (clientY - mousedownY)}px`;\n  }\n};\nconst up = () => {\n  const popup = getPopup();\n  dragging = false;\n  removeClass(popup, 'swal2-dragging');\n};\n\n/**\n * @param {MouseEvent | TouchEvent} event\n * @returns {{ clientX: number, clientY: number }}\n */\nconst getClientXY = event => {\n  let clientX = 0,\n    clientY = 0;\n  if (event.type.startsWith('mouse')) {\n    clientX = /** @type {MouseEvent} */event.clientX;\n    clientY = /** @type {MouseEvent} */event.clientY;\n  } else if (event.type.startsWith('touch')) {\n    clientX = /** @type {TouchEvent} */event.touches[0].clientX;\n    clientY = /** @type {TouchEvent} */event.touches[0].clientY;\n  }\n  return {\n    clientX,\n    clientY\n  };\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderPopup = (instance, params) => {\n  const container = getContainer();\n  const popup = getPopup();\n  if (!container || !popup) {\n    return;\n  }\n\n  // Width\n  // https://github.com/sweetalert2/sweetalert2/issues/2170\n  if (params.toast) {\n    applyNumericalStyle(container, 'width', params.width);\n    popup.style.width = '100%';\n    const loader = getLoader();\n    if (loader) {\n      popup.insertBefore(loader, getIcon());\n    }\n  } else {\n    applyNumericalStyle(popup, 'width', params.width);\n  }\n\n  // Padding\n  applyNumericalStyle(popup, 'padding', params.padding);\n\n  // Color\n  if (params.color) {\n    popup.style.color = params.color;\n  }\n\n  // Background\n  if (params.background) {\n    popup.style.background = params.background;\n  }\n  hide(getValidationMessage());\n\n  // Classes\n  addClasses$1(popup, params);\n  if (params.draggable && !params.toast) {\n    addClass(popup, swalClasses.draggable);\n    addDraggableListeners(popup);\n  } else {\n    removeClass(popup, swalClasses.draggable);\n    removeDraggableListeners(popup);\n  }\n};\n\n/**\n * @param {HTMLElement} popup\n * @param {SweetAlertOptions} params\n */\nconst addClasses$1 = (popup, params) => {\n  const showClass = params.showClass || {};\n  // Default Class + showClass when updating Swal.update({})\n  popup.className = `${swalClasses.popup} ${isVisible$1(popup) ? showClass.popup : ''}`;\n  if (params.toast) {\n    addClass([document.documentElement, document.body], swalClasses['toast-shown']);\n    addClass(popup, swalClasses.toast);\n  } else {\n    addClass(popup, swalClasses.modal);\n  }\n\n  // Custom class\n  applyCustomClass(popup, params, 'popup');\n  // TODO: remove in the next major\n  if (typeof params.customClass === 'string') {\n    addClass(popup, params.customClass);\n  }\n\n  // Icon class (#1842)\n  if (params.icon) {\n    addClass(popup, swalClasses[`icon-${params.icon}`]);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderProgressSteps = (instance, params) => {\n  const progressStepsContainer = getProgressSteps();\n  if (!progressStepsContainer) {\n    return;\n  }\n  const {\n    progressSteps,\n    currentProgressStep\n  } = params;\n  if (!progressSteps || progressSteps.length === 0 || currentProgressStep === undefined) {\n    hide(progressStepsContainer);\n    return;\n  }\n  show(progressStepsContainer);\n  progressStepsContainer.textContent = '';\n  if (currentProgressStep >= progressSteps.length) {\n    warn('Invalid currentProgressStep parameter, it should be less than progressSteps.length ' + '(currentProgressStep like JS arrays starts from 0)');\n  }\n  progressSteps.forEach((step, index) => {\n    const stepEl = createStepElement(step);\n    progressStepsContainer.appendChild(stepEl);\n    if (index === currentProgressStep) {\n      addClass(stepEl, swalClasses['active-progress-step']);\n    }\n    if (index !== progressSteps.length - 1) {\n      const lineEl = createLineElement(params);\n      progressStepsContainer.appendChild(lineEl);\n    }\n  });\n};\n\n/**\n * @param {string} step\n * @returns {HTMLLIElement}\n */\nconst createStepElement = step => {\n  const stepEl = document.createElement('li');\n  addClass(stepEl, swalClasses['progress-step']);\n  setInnerHtml(stepEl, step);\n  return stepEl;\n};\n\n/**\n * @param {SweetAlertOptions} params\n * @returns {HTMLLIElement}\n */\nconst createLineElement = params => {\n  const lineEl = document.createElement('li');\n  addClass(lineEl, swalClasses['progress-step-line']);\n  if (params.progressStepsDistance) {\n    applyNumericalStyle(lineEl, 'width', params.progressStepsDistance);\n  }\n  return lineEl;\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderTitle = (instance, params) => {\n  const title = getTitle();\n  if (!title) {\n    return;\n  }\n  showWhenInnerHtmlPresent(title);\n  toggle(title, params.title || params.titleText, 'block');\n  if (params.title) {\n    parseHtmlToContainer(params.title, title);\n  }\n  if (params.titleText) {\n    title.innerText = params.titleText;\n  }\n\n  // Custom class\n  applyCustomClass(title, params, 'title');\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst render = (instance, params) => {\n  renderPopup(instance, params);\n  renderContainer(instance, params);\n  renderProgressSteps(instance, params);\n  renderIcon(instance, params);\n  renderImage(instance, params);\n  renderTitle(instance, params);\n  renderCloseButton(instance, params);\n  renderContent(instance, params);\n  renderActions(instance, params);\n  renderFooter(instance, params);\n  const popup = getPopup();\n  if (typeof params.didRender === 'function' && popup) {\n    params.didRender(popup);\n  }\n  globalState.eventEmitter.emit('didRender', popup);\n};\n\n/*\n * Global function to determine if SweetAlert2 popup is shown\n */\nconst isVisible = () => {\n  return isVisible$1(getPopup());\n};\n\n/*\n * Global function to click 'Confirm' button\n */\nconst clickConfirm = () => {\n  var _dom$getConfirmButton;\n  return (_dom$getConfirmButton = getConfirmButton()) === null || _dom$getConfirmButton === void 0 ? void 0 : _dom$getConfirmButton.click();\n};\n\n/*\n * Global function to click 'Deny' button\n */\nconst clickDeny = () => {\n  var _dom$getDenyButton;\n  return (_dom$getDenyButton = getDenyButton()) === null || _dom$getDenyButton === void 0 ? void 0 : _dom$getDenyButton.click();\n};\n\n/*\n * Global function to click 'Cancel' button\n */\nconst clickCancel = () => {\n  var _dom$getCancelButton;\n  return (_dom$getCancelButton = getCancelButton()) === null || _dom$getCancelButton === void 0 ? void 0 : _dom$getCancelButton.click();\n};\n\n/** @typedef {'cancel' | 'backdrop' | 'close' | 'esc' | 'timer'} DismissReason */\n\n/** @type {Record<DismissReason, DismissReason>} */\nconst DismissReason = Object.freeze({\n  cancel: 'cancel',\n  backdrop: 'backdrop',\n  close: 'close',\n  esc: 'esc',\n  timer: 'timer'\n});\n\n/**\n * @param {GlobalState} globalState\n */\nconst removeKeydownHandler = globalState => {\n  if (globalState.keydownTarget && globalState.keydownHandlerAdded) {\n    globalState.keydownTarget.removeEventListener('keydown', globalState.keydownHandler, {\n      capture: globalState.keydownListenerCapture\n    });\n    globalState.keydownHandlerAdded = false;\n  }\n};\n\n/**\n * @param {GlobalState} globalState\n * @param {SweetAlertOptions} innerParams\n * @param {*} dismissWith\n */\nconst addKeydownHandler = (globalState, innerParams, dismissWith) => {\n  removeKeydownHandler(globalState);\n  if (!innerParams.toast) {\n    globalState.keydownHandler = e => keydownHandler(innerParams, e, dismissWith);\n    globalState.keydownTarget = innerParams.keydownListenerCapture ? window : getPopup();\n    globalState.keydownListenerCapture = innerParams.keydownListenerCapture;\n    globalState.keydownTarget.addEventListener('keydown', globalState.keydownHandler, {\n      capture: globalState.keydownListenerCapture\n    });\n    globalState.keydownHandlerAdded = true;\n  }\n};\n\n/**\n * @param {number} index\n * @param {number} increment\n */\nconst setFocus = (index, increment) => {\n  var _dom$getPopup;\n  const focusableElements = getFocusableElements();\n  // search for visible elements and select the next possible match\n  if (focusableElements.length) {\n    index = index + increment;\n\n    // shift + tab when .swal2-popup is focused\n    if (index === -2) {\n      index = focusableElements.length - 1;\n    }\n\n    // rollover to first item\n    if (index === focusableElements.length) {\n      index = 0;\n\n      // go to last item\n    } else if (index === -1) {\n      index = focusableElements.length - 1;\n    }\n    focusableElements[index].focus();\n    return;\n  }\n  // no visible focusable elements, focus the popup\n  (_dom$getPopup = getPopup()) === null || _dom$getPopup === void 0 || _dom$getPopup.focus();\n};\nconst arrowKeysNextButton = ['ArrowRight', 'ArrowDown'];\nconst arrowKeysPreviousButton = ['ArrowLeft', 'ArrowUp'];\n\n/**\n * @param {SweetAlertOptions} innerParams\n * @param {KeyboardEvent} event\n * @param {Function} dismissWith\n */\nconst keydownHandler = (innerParams, event, dismissWith) => {\n  if (!innerParams) {\n    return; // This instance has already been destroyed\n  }\n\n  // Ignore keydown during IME composition\n  // https://developer.mozilla.org/en-US/docs/Web/API/Document/keydown_event#ignoring_keydown_during_ime_composition\n  // https://github.com/sweetalert2/sweetalert2/issues/720\n  // https://github.com/sweetalert2/sweetalert2/issues/2406\n  if (event.isComposing || event.keyCode === 229) {\n    return;\n  }\n  if (innerParams.stopKeydownPropagation) {\n    event.stopPropagation();\n  }\n\n  // ENTER\n  if (event.key === 'Enter') {\n    handleEnter(event, innerParams);\n  }\n\n  // TAB\n  else if (event.key === 'Tab') {\n    handleTab(event);\n  }\n\n  // ARROWS - switch focus between buttons\n  else if ([...arrowKeysNextButton, ...arrowKeysPreviousButton].includes(event.key)) {\n    handleArrows(event.key);\n  }\n\n  // ESC\n  else if (event.key === 'Escape') {\n    handleEsc(event, innerParams, dismissWith);\n  }\n};\n\n/**\n * @param {KeyboardEvent} event\n * @param {SweetAlertOptions} innerParams\n */\nconst handleEnter = (event, innerParams) => {\n  // https://github.com/sweetalert2/sweetalert2/issues/2386\n  if (!callIfFunction(innerParams.allowEnterKey)) {\n    return;\n  }\n  const input = getInput$1(getPopup(), innerParams.input);\n  if (event.target && input && event.target instanceof HTMLElement && event.target.outerHTML === input.outerHTML) {\n    if (['textarea', 'file'].includes(innerParams.input)) {\n      return; // do not submit\n    }\n    clickConfirm();\n    event.preventDefault();\n  }\n};\n\n/**\n * @param {KeyboardEvent} event\n */\nconst handleTab = event => {\n  const targetElement = event.target;\n  const focusableElements = getFocusableElements();\n  let btnIndex = -1;\n  for (let i = 0; i < focusableElements.length; i++) {\n    if (targetElement === focusableElements[i]) {\n      btnIndex = i;\n      break;\n    }\n  }\n\n  // Cycle to the next button\n  if (!event.shiftKey) {\n    setFocus(btnIndex, 1);\n  }\n\n  // Cycle to the prev button\n  else {\n    setFocus(btnIndex, -1);\n  }\n  event.stopPropagation();\n  event.preventDefault();\n};\n\n/**\n * @param {string} key\n */\nconst handleArrows = key => {\n  const actions = getActions();\n  const confirmButton = getConfirmButton();\n  const denyButton = getDenyButton();\n  const cancelButton = getCancelButton();\n  if (!actions || !confirmButton || !denyButton || !cancelButton) {\n    return;\n  }\n  /** @type HTMLElement[] */\n  const buttons = [confirmButton, denyButton, cancelButton];\n  if (document.activeElement instanceof HTMLElement && !buttons.includes(document.activeElement)) {\n    return;\n  }\n  const sibling = arrowKeysNextButton.includes(key) ? 'nextElementSibling' : 'previousElementSibling';\n  let buttonToFocus = document.activeElement;\n  if (!buttonToFocus) {\n    return;\n  }\n  for (let i = 0; i < actions.children.length; i++) {\n    buttonToFocus = buttonToFocus[sibling];\n    if (!buttonToFocus) {\n      return;\n    }\n    if (buttonToFocus instanceof HTMLButtonElement && isVisible$1(buttonToFocus)) {\n      break;\n    }\n  }\n  if (buttonToFocus instanceof HTMLButtonElement) {\n    buttonToFocus.focus();\n  }\n};\n\n/**\n * @param {KeyboardEvent} event\n * @param {SweetAlertOptions} innerParams\n * @param {Function} dismissWith\n */\nconst handleEsc = (event, innerParams, dismissWith) => {\n  if (callIfFunction(innerParams.allowEscapeKey)) {\n    event.preventDefault();\n    dismissWith(DismissReason.esc);\n  }\n};\n\n/**\n * This module contains `WeakMap`s for each effectively-\"private  property\" that a `Swal` has.\n * For example, to set the private property \"foo\" of `this` to \"bar\", you can `privateProps.foo.set(this, 'bar')`\n * This is the approach that Babel will probably take to implement private methods/fields\n *   https://github.com/tc39/proposal-private-methods\n *   https://github.com/babel/babel/pull/7555\n * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*\n *   then we can use that language feature.\n */\n\nvar privateMethods = {\n  swalPromiseResolve: new WeakMap(),\n  swalPromiseReject: new WeakMap()\n};\n\n// From https://developer.paciellogroup.com/blog/2018/06/the-current-state-of-modal-dialog-accessibility/\n// Adding aria-hidden=\"true\" to elements outside of the active modal dialog ensures that\n// elements not within the active modal dialog will not be surfaced if a user opens a screen\n// reader’s list of elements (headings, form controls, landmarks, etc.) in the document.\n\nconst setAriaHidden = () => {\n  const container = getContainer();\n  const bodyChildren = Array.from(document.body.children);\n  bodyChildren.forEach(el => {\n    if (el.contains(container)) {\n      return;\n    }\n    if (el.hasAttribute('aria-hidden')) {\n      el.setAttribute('data-previous-aria-hidden', el.getAttribute('aria-hidden') || '');\n    }\n    el.setAttribute('aria-hidden', 'true');\n  });\n};\nconst unsetAriaHidden = () => {\n  const bodyChildren = Array.from(document.body.children);\n  bodyChildren.forEach(el => {\n    if (el.hasAttribute('data-previous-aria-hidden')) {\n      el.setAttribute('aria-hidden', el.getAttribute('data-previous-aria-hidden') || '');\n      el.removeAttribute('data-previous-aria-hidden');\n    } else {\n      el.removeAttribute('aria-hidden');\n    }\n  });\n};\n\n// @ts-ignore\nconst isSafariOrIOS = typeof window !== 'undefined' && !!window.GestureEvent; // true for Safari desktop + all iOS browsers https://stackoverflow.com/a/70585394\n\n/**\n * Fix iOS scrolling\n * http://stackoverflow.com/q/39626302\n */\nconst iOSfix = () => {\n  if (isSafariOrIOS && !hasClass(document.body, swalClasses.iosfix)) {\n    const offset = document.body.scrollTop;\n    document.body.style.top = `${offset * -1}px`;\n    addClass(document.body, swalClasses.iosfix);\n    lockBodyScroll();\n  }\n};\n\n/**\n * https://github.com/sweetalert2/sweetalert2/issues/1246\n */\nconst lockBodyScroll = () => {\n  const container = getContainer();\n  if (!container) {\n    return;\n  }\n  /** @type {boolean} */\n  let preventTouchMove;\n  /**\n   * @param {TouchEvent} event\n   */\n  container.ontouchstart = event => {\n    preventTouchMove = shouldPreventTouchMove(event);\n  };\n  /**\n   * @param {TouchEvent} event\n   */\n  container.ontouchmove = event => {\n    if (preventTouchMove) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  };\n};\n\n/**\n * @param {TouchEvent} event\n * @returns {boolean}\n */\nconst shouldPreventTouchMove = event => {\n  const target = event.target;\n  const container = getContainer();\n  const htmlContainer = getHtmlContainer();\n  if (!container || !htmlContainer) {\n    return false;\n  }\n  if (isStylus(event) || isZoom(event)) {\n    return false;\n  }\n  if (target === container) {\n    return true;\n  }\n  if (!isScrollable(container) && target instanceof HTMLElement && !selfOrParentIsScrollable(target, htmlContainer) &&\n  // #2823\n  target.tagName !== 'INPUT' &&\n  // #1603\n  target.tagName !== 'TEXTAREA' &&\n  // #2266\n  !(isScrollable(htmlContainer) &&\n  // #1944\n  htmlContainer.contains(target))) {\n    return true;\n  }\n  return false;\n};\n\n/**\n * https://github.com/sweetalert2/sweetalert2/issues/1786\n *\n * @param {*} event\n * @returns {boolean}\n */\nconst isStylus = event => {\n  return event.touches && event.touches.length && event.touches[0].touchType === 'stylus';\n};\n\n/**\n * https://github.com/sweetalert2/sweetalert2/issues/1891\n *\n * @param {TouchEvent} event\n * @returns {boolean}\n */\nconst isZoom = event => {\n  return event.touches && event.touches.length > 1;\n};\nconst undoIOSfix = () => {\n  if (hasClass(document.body, swalClasses.iosfix)) {\n    const offset = parseInt(document.body.style.top, 10);\n    removeClass(document.body, swalClasses.iosfix);\n    document.body.style.top = '';\n    document.body.scrollTop = offset * -1;\n  }\n};\n\n/**\n * Measure scrollbar width for padding body during modal show/hide\n * https://github.com/twbs/bootstrap/blob/master/js/src/modal.js\n *\n * @returns {number}\n */\nconst measureScrollbar = () => {\n  const scrollDiv = document.createElement('div');\n  scrollDiv.className = swalClasses['scrollbar-measure'];\n  document.body.appendChild(scrollDiv);\n  const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth;\n  document.body.removeChild(scrollDiv);\n  return scrollbarWidth;\n};\n\n/**\n * Remember state in cases where opening and handling a modal will fiddle with it.\n * @type {number | null}\n */\nlet previousBodyPadding = null;\n\n/**\n * @param {string} initialBodyOverflow\n */\nconst replaceScrollbarWithPadding = initialBodyOverflow => {\n  // for queues, do not do this more than once\n  if (previousBodyPadding !== null) {\n    return;\n  }\n  // if the body has overflow\n  if (document.body.scrollHeight > window.innerHeight || initialBodyOverflow === 'scroll' // https://github.com/sweetalert2/sweetalert2/issues/2663\n  ) {\n    // add padding so the content doesn't shift after removal of scrollbar\n    previousBodyPadding = parseInt(window.getComputedStyle(document.body).getPropertyValue('padding-right'));\n    document.body.style.paddingRight = `${previousBodyPadding + measureScrollbar()}px`;\n  }\n};\nconst undoReplaceScrollbarWithPadding = () => {\n  if (previousBodyPadding !== null) {\n    document.body.style.paddingRight = `${previousBodyPadding}px`;\n    previousBodyPadding = null;\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {HTMLElement} container\n * @param {boolean} returnFocus\n * @param {Function} didClose\n */\nfunction removePopupAndResetState(instance, container, returnFocus, didClose) {\n  if (isToast()) {\n    triggerDidCloseAndDispose(instance, didClose);\n  } else {\n    restoreActiveElement(returnFocus).then(() => triggerDidCloseAndDispose(instance, didClose));\n    removeKeydownHandler(globalState);\n  }\n\n  // workaround for https://github.com/sweetalert2/sweetalert2/issues/2088\n  // for some reason removing the container in Safari will scroll the document to bottom\n  if (isSafariOrIOS) {\n    container.setAttribute('style', 'display:none !important');\n    container.removeAttribute('class');\n    container.innerHTML = '';\n  } else {\n    container.remove();\n  }\n  if (isModal()) {\n    undoReplaceScrollbarWithPadding();\n    undoIOSfix();\n    unsetAriaHidden();\n  }\n  removeBodyClasses();\n}\n\n/**\n * Remove SweetAlert2 classes from body\n */\nfunction removeBodyClasses() {\n  removeClass([document.documentElement, document.body], [swalClasses.shown, swalClasses['height-auto'], swalClasses['no-backdrop'], swalClasses['toast-shown']]);\n}\n\n/**\n * Instance method to close sweetAlert\n *\n * @param {any} resolveValue\n */\nfunction close(resolveValue) {\n  resolveValue = prepareResolveValue(resolveValue);\n  const swalPromiseResolve = privateMethods.swalPromiseResolve.get(this);\n  const didClose = triggerClosePopup(this);\n  if (this.isAwaitingPromise) {\n    // A swal awaiting for a promise (after a click on Confirm or Deny) cannot be dismissed anymore #2335\n    if (!resolveValue.isDismissed) {\n      handleAwaitingPromise(this);\n      swalPromiseResolve(resolveValue);\n    }\n  } else if (didClose) {\n    // Resolve Swal promise\n    swalPromiseResolve(resolveValue);\n  }\n}\nconst triggerClosePopup = instance => {\n  const popup = getPopup();\n  if (!popup) {\n    return false;\n  }\n  const innerParams = privateProps.innerParams.get(instance);\n  if (!innerParams || hasClass(popup, innerParams.hideClass.popup)) {\n    return false;\n  }\n  removeClass(popup, innerParams.showClass.popup);\n  addClass(popup, innerParams.hideClass.popup);\n  const backdrop = getContainer();\n  removeClass(backdrop, innerParams.showClass.backdrop);\n  addClass(backdrop, innerParams.hideClass.backdrop);\n  handlePopupAnimation(instance, popup, innerParams);\n  return true;\n};\n\n/**\n * @param {any} error\n */\nfunction rejectPromise(error) {\n  const rejectPromise = privateMethods.swalPromiseReject.get(this);\n  handleAwaitingPromise(this);\n  if (rejectPromise) {\n    // Reject Swal promise\n    rejectPromise(error);\n  }\n}\n\n/**\n * @param {SweetAlert} instance\n */\nconst handleAwaitingPromise = instance => {\n  if (instance.isAwaitingPromise) {\n    delete instance.isAwaitingPromise;\n    // The instance might have been previously partly destroyed, we must resume the destroy process in this case #2335\n    if (!privateProps.innerParams.get(instance)) {\n      instance._destroy();\n    }\n  }\n};\n\n/**\n * @param {any} resolveValue\n * @returns {SweetAlertResult}\n */\nconst prepareResolveValue = resolveValue => {\n  // When user calls Swal.close()\n  if (typeof resolveValue === 'undefined') {\n    return {\n      isConfirmed: false,\n      isDenied: false,\n      isDismissed: true\n    };\n  }\n  return Object.assign({\n    isConfirmed: false,\n    isDenied: false,\n    isDismissed: false\n  }, resolveValue);\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {HTMLElement} popup\n * @param {SweetAlertOptions} innerParams\n */\nconst handlePopupAnimation = (instance, popup, innerParams) => {\n  var _globalState$eventEmi;\n  const container = getContainer();\n  // If animation is supported, animate\n  const animationIsSupported = hasCssAnimation(popup);\n  if (typeof innerParams.willClose === 'function') {\n    innerParams.willClose(popup);\n  }\n  (_globalState$eventEmi = globalState.eventEmitter) === null || _globalState$eventEmi === void 0 || _globalState$eventEmi.emit('willClose', popup);\n  if (animationIsSupported) {\n    animatePopup(instance, popup, container, innerParams.returnFocus, innerParams.didClose);\n  } else {\n    // Otherwise, remove immediately\n    removePopupAndResetState(instance, container, innerParams.returnFocus, innerParams.didClose);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {HTMLElement} popup\n * @param {HTMLElement} container\n * @param {boolean} returnFocus\n * @param {Function} didClose\n */\nconst animatePopup = (instance, popup, container, returnFocus, didClose) => {\n  globalState.swalCloseEventFinishedCallback = removePopupAndResetState.bind(null, instance, container, returnFocus, didClose);\n  /**\n   * @param {AnimationEvent | TransitionEvent} e\n   */\n  const swalCloseAnimationFinished = function (e) {\n    if (e.target === popup) {\n      var _globalState$swalClos;\n      (_globalState$swalClos = globalState.swalCloseEventFinishedCallback) === null || _globalState$swalClos === void 0 || _globalState$swalClos.call(globalState);\n      delete globalState.swalCloseEventFinishedCallback;\n      popup.removeEventListener('animationend', swalCloseAnimationFinished);\n      popup.removeEventListener('transitionend', swalCloseAnimationFinished);\n    }\n  };\n  popup.addEventListener('animationend', swalCloseAnimationFinished);\n  popup.addEventListener('transitionend', swalCloseAnimationFinished);\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {Function} didClose\n */\nconst triggerDidCloseAndDispose = (instance, didClose) => {\n  setTimeout(() => {\n    var _globalState$eventEmi2;\n    if (typeof didClose === 'function') {\n      didClose.bind(instance.params)();\n    }\n    (_globalState$eventEmi2 = globalState.eventEmitter) === null || _globalState$eventEmi2 === void 0 || _globalState$eventEmi2.emit('didClose');\n    // instance might have been destroyed already\n    if (instance._destroy) {\n      instance._destroy();\n    }\n  });\n};\n\n/**\n * Shows loader (spinner), this is useful with AJAX requests.\n * By default the loader be shown instead of the \"Confirm\" button.\n *\n * @param {HTMLButtonElement | null} [buttonToReplace]\n */\nconst showLoading = buttonToReplace => {\n  let popup = getPopup();\n  if (!popup) {\n    new Swal();\n  }\n  popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  const loader = getLoader();\n  if (isToast()) {\n    hide(getIcon());\n  } else {\n    replaceButton(popup, buttonToReplace);\n  }\n  show(loader);\n  popup.setAttribute('data-loading', 'true');\n  popup.setAttribute('aria-busy', 'true');\n  popup.focus();\n};\n\n/**\n * @param {HTMLElement} popup\n * @param {HTMLButtonElement | null} [buttonToReplace]\n */\nconst replaceButton = (popup, buttonToReplace) => {\n  const actions = getActions();\n  const loader = getLoader();\n  if (!actions || !loader) {\n    return;\n  }\n  if (!buttonToReplace && isVisible$1(getConfirmButton())) {\n    buttonToReplace = getConfirmButton();\n  }\n  show(actions);\n  if (buttonToReplace) {\n    hide(buttonToReplace);\n    loader.setAttribute('data-button-to-replace', buttonToReplace.className);\n    actions.insertBefore(loader, buttonToReplace);\n  }\n  addClass([popup, actions], swalClasses.loading);\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst handleInputOptionsAndValue = (instance, params) => {\n  if (params.input === 'select' || params.input === 'radio') {\n    handleInputOptions(instance, params);\n  } else if (['text', 'email', 'number', 'tel', 'textarea'].some(i => i === params.input) && (hasToPromiseFn(params.inputValue) || isPromise(params.inputValue))) {\n    showLoading(getConfirmButton());\n    handleInputValue(instance, params);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} innerParams\n * @returns {SweetAlertInputValue}\n */\nconst getInputValue = (instance, innerParams) => {\n  const input = instance.getInput();\n  if (!input) {\n    return null;\n  }\n  switch (innerParams.input) {\n    case 'checkbox':\n      return getCheckboxValue(input);\n    case 'radio':\n      return getRadioValue(input);\n    case 'file':\n      return getFileValue(input);\n    default:\n      return innerParams.inputAutoTrim ? input.value.trim() : input.value;\n  }\n};\n\n/**\n * @param {HTMLInputElement} input\n * @returns {number}\n */\nconst getCheckboxValue = input => input.checked ? 1 : 0;\n\n/**\n * @param {HTMLInputElement} input\n * @returns {string | null}\n */\nconst getRadioValue = input => input.checked ? input.value : null;\n\n/**\n * @param {HTMLInputElement} input\n * @returns {FileList | File | null}\n */\nconst getFileValue = input => input.files && input.files.length ? input.getAttribute('multiple') !== null ? input.files : input.files[0] : null;\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst handleInputOptions = (instance, params) => {\n  const popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  /**\n   * @param {Record<string, any>} inputOptions\n   */\n  const processInputOptions = inputOptions => {\n    if (params.input === 'select') {\n      populateSelectOptions(popup, formatInputOptions(inputOptions), params);\n    } else if (params.input === 'radio') {\n      populateRadioOptions(popup, formatInputOptions(inputOptions), params);\n    }\n  };\n  if (hasToPromiseFn(params.inputOptions) || isPromise(params.inputOptions)) {\n    showLoading(getConfirmButton());\n    asPromise(params.inputOptions).then(inputOptions => {\n      instance.hideLoading();\n      processInputOptions(inputOptions);\n    });\n  } else if (typeof params.inputOptions === 'object') {\n    processInputOptions(params.inputOptions);\n  } else {\n    error(`Unexpected type of inputOptions! Expected object, Map or Promise, got ${typeof params.inputOptions}`);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst handleInputValue = (instance, params) => {\n  const input = instance.getInput();\n  if (!input) {\n    return;\n  }\n  hide(input);\n  asPromise(params.inputValue).then(inputValue => {\n    input.value = params.input === 'number' ? `${parseFloat(inputValue) || 0}` : `${inputValue}`;\n    show(input);\n    input.focus();\n    instance.hideLoading();\n  }).catch(err => {\n    error(`Error in inputValue promise: ${err}`);\n    input.value = '';\n    show(input);\n    input.focus();\n    instance.hideLoading();\n  });\n};\n\n/**\n * @param {HTMLElement} popup\n * @param {InputOptionFlattened[]} inputOptions\n * @param {SweetAlertOptions} params\n */\nfunction populateSelectOptions(popup, inputOptions, params) {\n  const select = getDirectChildByClass(popup, swalClasses.select);\n  if (!select) {\n    return;\n  }\n  /**\n   * @param {HTMLElement} parent\n   * @param {string} optionLabel\n   * @param {string} optionValue\n   */\n  const renderOption = (parent, optionLabel, optionValue) => {\n    const option = document.createElement('option');\n    option.value = optionValue;\n    setInnerHtml(option, optionLabel);\n    option.selected = isSelected(optionValue, params.inputValue);\n    parent.appendChild(option);\n  };\n  inputOptions.forEach(inputOption => {\n    const optionValue = inputOption[0];\n    const optionLabel = inputOption[1];\n    // <optgroup> spec:\n    // https://www.w3.org/TR/html401/interact/forms.html#h-17.6\n    // \"...all OPTGROUP elements must be specified directly within a SELECT element (i.e., groups may not be nested)...\"\n    // check whether this is a <optgroup>\n    if (Array.isArray(optionLabel)) {\n      // if it is an array, then it is an <optgroup>\n      const optgroup = document.createElement('optgroup');\n      optgroup.label = optionValue;\n      optgroup.disabled = false; // not configurable for now\n      select.appendChild(optgroup);\n      optionLabel.forEach(o => renderOption(optgroup, o[1], o[0]));\n    } else {\n      // case of <option>\n      renderOption(select, optionLabel, optionValue);\n    }\n  });\n  select.focus();\n}\n\n/**\n * @param {HTMLElement} popup\n * @param {InputOptionFlattened[]} inputOptions\n * @param {SweetAlertOptions} params\n */\nfunction populateRadioOptions(popup, inputOptions, params) {\n  const radio = getDirectChildByClass(popup, swalClasses.radio);\n  if (!radio) {\n    return;\n  }\n  inputOptions.forEach(inputOption => {\n    const radioValue = inputOption[0];\n    const radioLabel = inputOption[1];\n    const radioInput = document.createElement('input');\n    const radioLabelElement = document.createElement('label');\n    radioInput.type = 'radio';\n    radioInput.name = swalClasses.radio;\n    radioInput.value = radioValue;\n    if (isSelected(radioValue, params.inputValue)) {\n      radioInput.checked = true;\n    }\n    const label = document.createElement('span');\n    setInnerHtml(label, radioLabel);\n    label.className = swalClasses.label;\n    radioLabelElement.appendChild(radioInput);\n    radioLabelElement.appendChild(label);\n    radio.appendChild(radioLabelElement);\n  });\n  const radios = radio.querySelectorAll('input');\n  if (radios.length) {\n    radios[0].focus();\n  }\n}\n\n/**\n * Converts `inputOptions` into an array of `[value, label]`s\n *\n * @param {Record<string, any>} inputOptions\n * @typedef {string[]} InputOptionFlattened\n * @returns {InputOptionFlattened[]}\n */\nconst formatInputOptions = inputOptions => {\n  /** @type {InputOptionFlattened[]} */\n  const result = [];\n  if (inputOptions instanceof Map) {\n    inputOptions.forEach((value, key) => {\n      let valueFormatted = value;\n      if (typeof valueFormatted === 'object') {\n        // case of <optgroup>\n        valueFormatted = formatInputOptions(valueFormatted);\n      }\n      result.push([key, valueFormatted]);\n    });\n  } else {\n    Object.keys(inputOptions).forEach(key => {\n      let valueFormatted = inputOptions[key];\n      if (typeof valueFormatted === 'object') {\n        // case of <optgroup>\n        valueFormatted = formatInputOptions(valueFormatted);\n      }\n      result.push([key, valueFormatted]);\n    });\n  }\n  return result;\n};\n\n/**\n * @param {string} optionValue\n * @param {SweetAlertInputValue} inputValue\n * @returns {boolean}\n */\nconst isSelected = (optionValue, inputValue) => {\n  return !!inputValue && inputValue.toString() === optionValue.toString();\n};\n\n/**\n * @param {SweetAlert} instance\n */\nconst handleConfirmButtonClick = instance => {\n  const innerParams = privateProps.innerParams.get(instance);\n  instance.disableButtons();\n  if (innerParams.input) {\n    handleConfirmOrDenyWithInput(instance, 'confirm');\n  } else {\n    confirm(instance, true);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n */\nconst handleDenyButtonClick = instance => {\n  const innerParams = privateProps.innerParams.get(instance);\n  instance.disableButtons();\n  if (innerParams.returnInputValueOnDeny) {\n    handleConfirmOrDenyWithInput(instance, 'deny');\n  } else {\n    deny(instance, false);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {Function} dismissWith\n */\nconst handleCancelButtonClick = (instance, dismissWith) => {\n  instance.disableButtons();\n  dismissWith(DismissReason.cancel);\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {'confirm' | 'deny'} type\n */\nconst handleConfirmOrDenyWithInput = (instance, type) => {\n  const innerParams = privateProps.innerParams.get(instance);\n  if (!innerParams.input) {\n    error(`The \"input\" parameter is needed to be set when using returnInputValueOn${capitalizeFirstLetter(type)}`);\n    return;\n  }\n  const input = instance.getInput();\n  const inputValue = getInputValue(instance, innerParams);\n  if (innerParams.inputValidator) {\n    handleInputValidator(instance, inputValue, type);\n  } else if (input && !input.checkValidity()) {\n    instance.enableButtons();\n    instance.showValidationMessage(innerParams.validationMessage || input.validationMessage);\n  } else if (type === 'deny') {\n    deny(instance, inputValue);\n  } else {\n    confirm(instance, inputValue);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertInputValue} inputValue\n * @param {'confirm' | 'deny'} type\n */\nconst handleInputValidator = (instance, inputValue, type) => {\n  const innerParams = privateProps.innerParams.get(instance);\n  instance.disableInput();\n  const validationPromise = Promise.resolve().then(() => asPromise(innerParams.inputValidator(inputValue, innerParams.validationMessage)));\n  validationPromise.then(validationMessage => {\n    instance.enableButtons();\n    instance.enableInput();\n    if (validationMessage) {\n      instance.showValidationMessage(validationMessage);\n    } else if (type === 'deny') {\n      deny(instance, inputValue);\n    } else {\n      confirm(instance, inputValue);\n    }\n  });\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {any} value\n */\nconst deny = (instance, value) => {\n  const innerParams = privateProps.innerParams.get(instance || undefined);\n  if (innerParams.showLoaderOnDeny) {\n    showLoading(getDenyButton());\n  }\n  if (innerParams.preDeny) {\n    instance.isAwaitingPromise = true; // Flagging the instance as awaiting a promise so it's own promise's reject/resolve methods doesn't get destroyed until the result from this preDeny's promise is received\n    const preDenyPromise = Promise.resolve().then(() => asPromise(innerParams.preDeny(value, innerParams.validationMessage)));\n    preDenyPromise.then(preDenyValue => {\n      if (preDenyValue === false) {\n        instance.hideLoading();\n        handleAwaitingPromise(instance);\n      } else {\n        instance.close({\n          isDenied: true,\n          value: typeof preDenyValue === 'undefined' ? value : preDenyValue\n        });\n      }\n    }).catch(error => rejectWith(instance || undefined, error));\n  } else {\n    instance.close({\n      isDenied: true,\n      value\n    });\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {any} value\n */\nconst succeedWith = (instance, value) => {\n  instance.close({\n    isConfirmed: true,\n    value\n  });\n};\n\n/**\n *\n * @param {SweetAlert} instance\n * @param {string} error\n */\nconst rejectWith = (instance, error) => {\n  instance.rejectPromise(error);\n};\n\n/**\n *\n * @param {SweetAlert} instance\n * @param {any} value\n */\nconst confirm = (instance, value) => {\n  const innerParams = privateProps.innerParams.get(instance || undefined);\n  if (innerParams.showLoaderOnConfirm) {\n    showLoading();\n  }\n  if (innerParams.preConfirm) {\n    instance.resetValidationMessage();\n    instance.isAwaitingPromise = true; // Flagging the instance as awaiting a promise so it's own promise's reject/resolve methods doesn't get destroyed until the result from this preConfirm's promise is received\n    const preConfirmPromise = Promise.resolve().then(() => asPromise(innerParams.preConfirm(value, innerParams.validationMessage)));\n    preConfirmPromise.then(preConfirmValue => {\n      if (isVisible$1(getValidationMessage()) || preConfirmValue === false) {\n        instance.hideLoading();\n        handleAwaitingPromise(instance);\n      } else {\n        succeedWith(instance, typeof preConfirmValue === 'undefined' ? value : preConfirmValue);\n      }\n    }).catch(error => rejectWith(instance || undefined, error));\n  } else {\n    succeedWith(instance, value);\n  }\n};\n\n/**\n * Hides loader and shows back the button which was hidden by .showLoading()\n */\nfunction hideLoading() {\n  // do nothing if popup is closed\n  const innerParams = privateProps.innerParams.get(this);\n  if (!innerParams) {\n    return;\n  }\n  const domCache = privateProps.domCache.get(this);\n  hide(domCache.loader);\n  if (isToast()) {\n    if (innerParams.icon) {\n      show(getIcon());\n    }\n  } else {\n    showRelatedButton(domCache);\n  }\n  removeClass([domCache.popup, domCache.actions], swalClasses.loading);\n  domCache.popup.removeAttribute('aria-busy');\n  domCache.popup.removeAttribute('data-loading');\n  domCache.confirmButton.disabled = false;\n  domCache.denyButton.disabled = false;\n  domCache.cancelButton.disabled = false;\n}\nconst showRelatedButton = domCache => {\n  const buttonToReplace = domCache.popup.getElementsByClassName(domCache.loader.getAttribute('data-button-to-replace'));\n  if (buttonToReplace.length) {\n    show(buttonToReplace[0], 'inline-block');\n  } else if (allButtonsAreHidden()) {\n    hide(domCache.actions);\n  }\n};\n\n/**\n * Gets the input DOM node, this method works with input parameter.\n *\n * @returns {HTMLInputElement | null}\n */\nfunction getInput() {\n  const innerParams = privateProps.innerParams.get(this);\n  const domCache = privateProps.domCache.get(this);\n  if (!domCache) {\n    return null;\n  }\n  return getInput$1(domCache.popup, innerParams.input);\n}\n\n/**\n * @param {SweetAlert} instance\n * @param {string[]} buttons\n * @param {boolean} disabled\n */\nfunction setButtonsDisabled(instance, buttons, disabled) {\n  const domCache = privateProps.domCache.get(instance);\n  buttons.forEach(button => {\n    domCache[button].disabled = disabled;\n  });\n}\n\n/**\n * @param {HTMLInputElement | null} input\n * @param {boolean} disabled\n */\nfunction setInputDisabled(input, disabled) {\n  const popup = getPopup();\n  if (!popup || !input) {\n    return;\n  }\n  if (input.type === 'radio') {\n    /** @type {NodeListOf<HTMLInputElement>} */\n    const radios = popup.querySelectorAll(`[name=\"${swalClasses.radio}\"]`);\n    for (let i = 0; i < radios.length; i++) {\n      radios[i].disabled = disabled;\n    }\n  } else {\n    input.disabled = disabled;\n  }\n}\n\n/**\n * Enable all the buttons\n * @this {SweetAlert}\n */\nfunction enableButtons() {\n  setButtonsDisabled(this, ['confirmButton', 'denyButton', 'cancelButton'], false);\n}\n\n/**\n * Disable all the buttons\n * @this {SweetAlert}\n */\nfunction disableButtons() {\n  setButtonsDisabled(this, ['confirmButton', 'denyButton', 'cancelButton'], true);\n}\n\n/**\n * Enable the input field\n * @this {SweetAlert}\n */\nfunction enableInput() {\n  setInputDisabled(this.getInput(), false);\n}\n\n/**\n * Disable the input field\n * @this {SweetAlert}\n */\nfunction disableInput() {\n  setInputDisabled(this.getInput(), true);\n}\n\n/**\n * Show block with validation message\n *\n * @param {string} error\n * @this {SweetAlert}\n */\nfunction showValidationMessage(error) {\n  const domCache = privateProps.domCache.get(this);\n  const params = privateProps.innerParams.get(this);\n  setInnerHtml(domCache.validationMessage, error);\n  domCache.validationMessage.className = swalClasses['validation-message'];\n  if (params.customClass && params.customClass.validationMessage) {\n    addClass(domCache.validationMessage, params.customClass.validationMessage);\n  }\n  show(domCache.validationMessage);\n  const input = this.getInput();\n  if (input) {\n    input.setAttribute('aria-invalid', 'true');\n    input.setAttribute('aria-describedby', swalClasses['validation-message']);\n    focusInput(input);\n    addClass(input, swalClasses.inputerror);\n  }\n}\n\n/**\n * Hide block with validation message\n *\n * @this {SweetAlert}\n */\nfunction resetValidationMessage() {\n  const domCache = privateProps.domCache.get(this);\n  if (domCache.validationMessage) {\n    hide(domCache.validationMessage);\n  }\n  const input = this.getInput();\n  if (input) {\n    input.removeAttribute('aria-invalid');\n    input.removeAttribute('aria-describedby');\n    removeClass(input, swalClasses.inputerror);\n  }\n}\n\nconst defaultParams = {\n  title: '',\n  titleText: '',\n  text: '',\n  html: '',\n  footer: '',\n  icon: undefined,\n  iconColor: undefined,\n  iconHtml: undefined,\n  template: undefined,\n  toast: false,\n  draggable: false,\n  animation: true,\n  theme: 'light',\n  showClass: {\n    popup: 'swal2-show',\n    backdrop: 'swal2-backdrop-show',\n    icon: 'swal2-icon-show'\n  },\n  hideClass: {\n    popup: 'swal2-hide',\n    backdrop: 'swal2-backdrop-hide',\n    icon: 'swal2-icon-hide'\n  },\n  customClass: {},\n  target: 'body',\n  color: undefined,\n  backdrop: true,\n  heightAuto: true,\n  allowOutsideClick: true,\n  allowEscapeKey: true,\n  allowEnterKey: true,\n  stopKeydownPropagation: true,\n  keydownListenerCapture: false,\n  showConfirmButton: true,\n  showDenyButton: false,\n  showCancelButton: false,\n  preConfirm: undefined,\n  preDeny: undefined,\n  confirmButtonText: 'OK',\n  confirmButtonAriaLabel: '',\n  confirmButtonColor: undefined,\n  denyButtonText: 'No',\n  denyButtonAriaLabel: '',\n  denyButtonColor: undefined,\n  cancelButtonText: 'Cancel',\n  cancelButtonAriaLabel: '',\n  cancelButtonColor: undefined,\n  buttonsStyling: true,\n  reverseButtons: false,\n  focusConfirm: true,\n  focusDeny: false,\n  focusCancel: false,\n  returnFocus: true,\n  showCloseButton: false,\n  closeButtonHtml: '&times;',\n  closeButtonAriaLabel: 'Close this dialog',\n  loaderHtml: '',\n  showLoaderOnConfirm: false,\n  showLoaderOnDeny: false,\n  imageUrl: undefined,\n  imageWidth: undefined,\n  imageHeight: undefined,\n  imageAlt: '',\n  timer: undefined,\n  timerProgressBar: false,\n  width: undefined,\n  padding: undefined,\n  background: undefined,\n  input: undefined,\n  inputPlaceholder: '',\n  inputLabel: '',\n  inputValue: '',\n  inputOptions: {},\n  inputAutoFocus: true,\n  inputAutoTrim: true,\n  inputAttributes: {},\n  inputValidator: undefined,\n  returnInputValueOnDeny: false,\n  validationMessage: undefined,\n  grow: false,\n  position: 'center',\n  progressSteps: [],\n  currentProgressStep: undefined,\n  progressStepsDistance: undefined,\n  willOpen: undefined,\n  didOpen: undefined,\n  didRender: undefined,\n  willClose: undefined,\n  didClose: undefined,\n  didDestroy: undefined,\n  scrollbarPadding: true,\n  topLayer: false\n};\nconst updatableParams = ['allowEscapeKey', 'allowOutsideClick', 'background', 'buttonsStyling', 'cancelButtonAriaLabel', 'cancelButtonColor', 'cancelButtonText', 'closeButtonAriaLabel', 'closeButtonHtml', 'color', 'confirmButtonAriaLabel', 'confirmButtonColor', 'confirmButtonText', 'currentProgressStep', 'customClass', 'denyButtonAriaLabel', 'denyButtonColor', 'denyButtonText', 'didClose', 'didDestroy', 'draggable', 'footer', 'hideClass', 'html', 'icon', 'iconColor', 'iconHtml', 'imageAlt', 'imageHeight', 'imageUrl', 'imageWidth', 'preConfirm', 'preDeny', 'progressSteps', 'returnFocus', 'reverseButtons', 'showCancelButton', 'showCloseButton', 'showConfirmButton', 'showDenyButton', 'text', 'title', 'titleText', 'theme', 'willClose'];\n\n/** @type {Record<string, string | undefined>} */\nconst deprecatedParams = {\n  allowEnterKey: undefined\n};\nconst toastIncompatibleParams = ['allowOutsideClick', 'allowEnterKey', 'backdrop', 'draggable', 'focusConfirm', 'focusDeny', 'focusCancel', 'returnFocus', 'heightAuto', 'keydownListenerCapture'];\n\n/**\n * Is valid parameter\n *\n * @param {string} paramName\n * @returns {boolean}\n */\nconst isValidParameter = paramName => {\n  return Object.prototype.hasOwnProperty.call(defaultParams, paramName);\n};\n\n/**\n * Is valid parameter for Swal.update() method\n *\n * @param {string} paramName\n * @returns {boolean}\n */\nconst isUpdatableParameter = paramName => {\n  return updatableParams.indexOf(paramName) !== -1;\n};\n\n/**\n * Is deprecated parameter\n *\n * @param {string} paramName\n * @returns {string | undefined}\n */\nconst isDeprecatedParameter = paramName => {\n  return deprecatedParams[paramName];\n};\n\n/**\n * @param {string} param\n */\nconst checkIfParamIsValid = param => {\n  if (!isValidParameter(param)) {\n    warn(`Unknown parameter \"${param}\"`);\n  }\n};\n\n/**\n * @param {string} param\n */\nconst checkIfToastParamIsValid = param => {\n  if (toastIncompatibleParams.includes(param)) {\n    warn(`The parameter \"${param}\" is incompatible with toasts`);\n  }\n};\n\n/**\n * @param {string} param\n */\nconst checkIfParamIsDeprecated = param => {\n  const isDeprecated = isDeprecatedParameter(param);\n  if (isDeprecated) {\n    warnAboutDeprecation(param, isDeprecated);\n  }\n};\n\n/**\n * Show relevant warnings for given params\n *\n * @param {SweetAlertOptions} params\n */\nconst showWarningsForParams = params => {\n  if (params.backdrop === false && params.allowOutsideClick) {\n    warn('\"allowOutsideClick\" parameter requires `backdrop` parameter to be set to `true`');\n  }\n  if (params.theme && !['light', 'dark', 'auto', 'minimal', 'borderless', 'embed-iframe', 'bulma', 'bulma-light', 'bulma-dark'].includes(params.theme)) {\n    warn(`Invalid theme \"${params.theme}\"`);\n  }\n  for (const param in params) {\n    checkIfParamIsValid(param);\n    if (params.toast) {\n      checkIfToastParamIsValid(param);\n    }\n    checkIfParamIsDeprecated(param);\n  }\n};\n\n/**\n * Updates popup parameters.\n *\n * @param {SweetAlertOptions} params\n */\nfunction update(params) {\n  const container = getContainer();\n  const popup = getPopup();\n  const innerParams = privateProps.innerParams.get(this);\n  if (!popup || hasClass(popup, innerParams.hideClass.popup)) {\n    warn(`You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.`);\n    return;\n  }\n  const validUpdatableParams = filterValidParams(params);\n  const updatedParams = Object.assign({}, innerParams, validUpdatableParams);\n  showWarningsForParams(updatedParams);\n  container.dataset['swal2Theme'] = updatedParams.theme;\n  render(this, updatedParams);\n  privateProps.innerParams.set(this, updatedParams);\n  Object.defineProperties(this, {\n    params: {\n      value: Object.assign({}, this.params, params),\n      writable: false,\n      enumerable: true\n    }\n  });\n}\n\n/**\n * @param {SweetAlertOptions} params\n * @returns {SweetAlertOptions}\n */\nconst filterValidParams = params => {\n  const validUpdatableParams = {};\n  Object.keys(params).forEach(param => {\n    if (isUpdatableParameter(param)) {\n      validUpdatableParams[param] = params[param];\n    } else {\n      warn(`Invalid parameter to update: ${param}`);\n    }\n  });\n  return validUpdatableParams;\n};\n\n/**\n * Dispose the current SweetAlert2 instance\n */\nfunction _destroy() {\n  const domCache = privateProps.domCache.get(this);\n  const innerParams = privateProps.innerParams.get(this);\n  if (!innerParams) {\n    disposeWeakMaps(this); // The WeakMaps might have been partly destroyed, we must recall it to dispose any remaining WeakMaps #2335\n    return; // This instance has already been destroyed\n  }\n\n  // Check if there is another Swal closing\n  if (domCache.popup && globalState.swalCloseEventFinishedCallback) {\n    globalState.swalCloseEventFinishedCallback();\n    delete globalState.swalCloseEventFinishedCallback;\n  }\n  if (typeof innerParams.didDestroy === 'function') {\n    innerParams.didDestroy();\n  }\n  globalState.eventEmitter.emit('didDestroy');\n  disposeSwal(this);\n}\n\n/**\n * @param {SweetAlert} instance\n */\nconst disposeSwal = instance => {\n  disposeWeakMaps(instance);\n  // Unset this.params so GC will dispose it (#1569)\n  delete instance.params;\n  // Unset globalState props so GC will dispose globalState (#1569)\n  delete globalState.keydownHandler;\n  delete globalState.keydownTarget;\n  // Unset currentInstance\n  delete globalState.currentInstance;\n};\n\n/**\n * @param {SweetAlert} instance\n */\nconst disposeWeakMaps = instance => {\n  // If the current instance is awaiting a promise result, we keep the privateMethods to call them once the promise result is retrieved #2335\n  if (instance.isAwaitingPromise) {\n    unsetWeakMaps(privateProps, instance);\n    instance.isAwaitingPromise = true;\n  } else {\n    unsetWeakMaps(privateMethods, instance);\n    unsetWeakMaps(privateProps, instance);\n    delete instance.isAwaitingPromise;\n    // Unset instance methods\n    delete instance.disableButtons;\n    delete instance.enableButtons;\n    delete instance.getInput;\n    delete instance.disableInput;\n    delete instance.enableInput;\n    delete instance.hideLoading;\n    delete instance.disableLoading;\n    delete instance.showValidationMessage;\n    delete instance.resetValidationMessage;\n    delete instance.close;\n    delete instance.closePopup;\n    delete instance.closeModal;\n    delete instance.closeToast;\n    delete instance.rejectPromise;\n    delete instance.update;\n    delete instance._destroy;\n  }\n};\n\n/**\n * @param {object} obj\n * @param {SweetAlert} instance\n */\nconst unsetWeakMaps = (obj, instance) => {\n  for (const i in obj) {\n    obj[i].delete(instance);\n  }\n};\n\nvar instanceMethods = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  _destroy: _destroy,\n  close: close,\n  closeModal: close,\n  closePopup: close,\n  closeToast: close,\n  disableButtons: disableButtons,\n  disableInput: disableInput,\n  disableLoading: hideLoading,\n  enableButtons: enableButtons,\n  enableInput: enableInput,\n  getInput: getInput,\n  handleAwaitingPromise: handleAwaitingPromise,\n  hideLoading: hideLoading,\n  rejectPromise: rejectPromise,\n  resetValidationMessage: resetValidationMessage,\n  showValidationMessage: showValidationMessage,\n  update: update\n});\n\n/**\n * @param {SweetAlertOptions} innerParams\n * @param {DomCache} domCache\n * @param {Function} dismissWith\n */\nconst handlePopupClick = (innerParams, domCache, dismissWith) => {\n  if (innerParams.toast) {\n    handleToastClick(innerParams, domCache, dismissWith);\n  } else {\n    // Ignore click events that had mousedown on the popup but mouseup on the container\n    // This can happen when the user drags a slider\n    handleModalMousedown(domCache);\n\n    // Ignore click events that had mousedown on the container but mouseup on the popup\n    handleContainerMousedown(domCache);\n    handleModalClick(innerParams, domCache, dismissWith);\n  }\n};\n\n/**\n * @param {SweetAlertOptions} innerParams\n * @param {DomCache} domCache\n * @param {Function} dismissWith\n */\nconst handleToastClick = (innerParams, domCache, dismissWith) => {\n  // Closing toast by internal click\n  domCache.popup.onclick = () => {\n    if (innerParams && (isAnyButtonShown(innerParams) || innerParams.timer || innerParams.input)) {\n      return;\n    }\n    dismissWith(DismissReason.close);\n  };\n};\n\n/**\n * @param {SweetAlertOptions} innerParams\n * @returns {boolean}\n */\nconst isAnyButtonShown = innerParams => {\n  return !!(innerParams.showConfirmButton || innerParams.showDenyButton || innerParams.showCancelButton || innerParams.showCloseButton);\n};\nlet ignoreOutsideClick = false;\n\n/**\n * @param {DomCache} domCache\n */\nconst handleModalMousedown = domCache => {\n  domCache.popup.onmousedown = () => {\n    domCache.container.onmouseup = function (e) {\n      domCache.container.onmouseup = () => {};\n      // We only check if the mouseup target is the container because usually it doesn't\n      // have any other direct children aside of the popup\n      if (e.target === domCache.container) {\n        ignoreOutsideClick = true;\n      }\n    };\n  };\n};\n\n/**\n * @param {DomCache} domCache\n */\nconst handleContainerMousedown = domCache => {\n  domCache.container.onmousedown = e => {\n    // prevent the modal text from being selected on double click on the container (allowOutsideClick: false)\n    if (e.target === domCache.container) {\n      e.preventDefault();\n    }\n    domCache.popup.onmouseup = function (e) {\n      domCache.popup.onmouseup = () => {};\n      // We also need to check if the mouseup target is a child of the popup\n      if (e.target === domCache.popup || e.target instanceof HTMLElement && domCache.popup.contains(e.target)) {\n        ignoreOutsideClick = true;\n      }\n    };\n  };\n};\n\n/**\n * @param {SweetAlertOptions} innerParams\n * @param {DomCache} domCache\n * @param {Function} dismissWith\n */\nconst handleModalClick = (innerParams, domCache, dismissWith) => {\n  domCache.container.onclick = e => {\n    if (ignoreOutsideClick) {\n      ignoreOutsideClick = false;\n      return;\n    }\n    if (e.target === domCache.container && callIfFunction(innerParams.allowOutsideClick)) {\n      dismissWith(DismissReason.backdrop);\n    }\n  };\n};\n\nconst isJqueryElement = elem => typeof elem === 'object' && elem.jquery;\nconst isElement = elem => elem instanceof Element || isJqueryElement(elem);\nconst argsToParams = args => {\n  const params = {};\n  if (typeof args[0] === 'object' && !isElement(args[0])) {\n    Object.assign(params, args[0]);\n  } else {\n    ['title', 'html', 'icon'].forEach((name, index) => {\n      const arg = args[index];\n      if (typeof arg === 'string' || isElement(arg)) {\n        params[name] = arg;\n      } else if (arg !== undefined) {\n        error(`Unexpected type of ${name}! Expected \"string\" or \"Element\", got ${typeof arg}`);\n      }\n    });\n  }\n  return params;\n};\n\n/**\n * Main method to create a new SweetAlert2 popup\n *\n * @param  {...SweetAlertOptions} args\n * @returns {Promise<SweetAlertResult>}\n */\nfunction fire(...args) {\n  return new this(...args);\n}\n\n/**\n * Returns an extended version of `Swal` containing `params` as defaults.\n * Useful for reusing Swal configuration.\n *\n * For example:\n *\n * Before:\n * const textPromptOptions = { input: 'text', showCancelButton: true }\n * const {value: firstName} = await Swal.fire({ ...textPromptOptions, title: 'What is your first name?' })\n * const {value: lastName} = await Swal.fire({ ...textPromptOptions, title: 'What is your last name?' })\n *\n * After:\n * const TextPrompt = Swal.mixin({ input: 'text', showCancelButton: true })\n * const {value: firstName} = await TextPrompt('What is your first name?')\n * const {value: lastName} = await TextPrompt('What is your last name?')\n *\n * @param {SweetAlertOptions} mixinParams\n * @returns {SweetAlert}\n */\nfunction mixin(mixinParams) {\n  class MixinSwal extends this {\n    _main(params, priorityMixinParams) {\n      return super._main(params, Object.assign({}, mixinParams, priorityMixinParams));\n    }\n  }\n  // @ts-ignore\n  return MixinSwal;\n}\n\n/**\n * If `timer` parameter is set, returns number of milliseconds of timer remained.\n * Otherwise, returns undefined.\n *\n * @returns {number | undefined}\n */\nconst getTimerLeft = () => {\n  return globalState.timeout && globalState.timeout.getTimerLeft();\n};\n\n/**\n * Stop timer. Returns number of milliseconds of timer remained.\n * If `timer` parameter isn't set, returns undefined.\n *\n * @returns {number | undefined}\n */\nconst stopTimer = () => {\n  if (globalState.timeout) {\n    stopTimerProgressBar();\n    return globalState.timeout.stop();\n  }\n};\n\n/**\n * Resume timer. Returns number of milliseconds of timer remained.\n * If `timer` parameter isn't set, returns undefined.\n *\n * @returns {number | undefined}\n */\nconst resumeTimer = () => {\n  if (globalState.timeout) {\n    const remaining = globalState.timeout.start();\n    animateTimerProgressBar(remaining);\n    return remaining;\n  }\n};\n\n/**\n * Resume timer. Returns number of milliseconds of timer remained.\n * If `timer` parameter isn't set, returns undefined.\n *\n * @returns {number | undefined}\n */\nconst toggleTimer = () => {\n  const timer = globalState.timeout;\n  return timer && (timer.running ? stopTimer() : resumeTimer());\n};\n\n/**\n * Increase timer. Returns number of milliseconds of an updated timer.\n * If `timer` parameter isn't set, returns undefined.\n *\n * @param {number} ms\n * @returns {number | undefined}\n */\nconst increaseTimer = ms => {\n  if (globalState.timeout) {\n    const remaining = globalState.timeout.increase(ms);\n    animateTimerProgressBar(remaining, true);\n    return remaining;\n  }\n};\n\n/**\n * Check if timer is running. Returns true if timer is running\n * or false if timer is paused or stopped.\n * If `timer` parameter isn't set, returns undefined\n *\n * @returns {boolean}\n */\nconst isTimerRunning = () => {\n  return !!(globalState.timeout && globalState.timeout.isRunning());\n};\n\nlet bodyClickListenerAdded = false;\nconst clickHandlers = {};\n\n/**\n * @param {string} attr\n */\nfunction bindClickHandler(attr = 'data-swal-template') {\n  clickHandlers[attr] = this;\n  if (!bodyClickListenerAdded) {\n    document.body.addEventListener('click', bodyClickListener);\n    bodyClickListenerAdded = true;\n  }\n}\nconst bodyClickListener = event => {\n  for (let el = event.target; el && el !== document; el = el.parentNode) {\n    for (const attr in clickHandlers) {\n      const template = el.getAttribute(attr);\n      if (template) {\n        clickHandlers[attr].fire({\n          template\n        });\n        return;\n      }\n    }\n  }\n};\n\n// Source: https://gist.github.com/mudge/5830382?permalink_comment_id=2691957#gistcomment-2691957\n\nclass EventEmitter {\n  constructor() {\n    /** @type {Events} */\n    this.events = {};\n  }\n\n  /**\n   * @param {string} eventName\n   * @returns {EventHandlers}\n   */\n  _getHandlersByEventName(eventName) {\n    if (typeof this.events[eventName] === 'undefined') {\n      // not Set because we need to keep the FIFO order\n      // https://github.com/sweetalert2/sweetalert2/pull/2763#discussion_r1748990334\n      this.events[eventName] = [];\n    }\n    return this.events[eventName];\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  on(eventName, eventHandler) {\n    const currentHandlers = this._getHandlersByEventName(eventName);\n    if (!currentHandlers.includes(eventHandler)) {\n      currentHandlers.push(eventHandler);\n    }\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  once(eventName, eventHandler) {\n    /**\n     * @param {Array} args\n     */\n    const onceFn = (...args) => {\n      this.removeListener(eventName, onceFn);\n      eventHandler.apply(this, args);\n    };\n    this.on(eventName, onceFn);\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {Array} args\n   */\n  emit(eventName, ...args) {\n    this._getHandlersByEventName(eventName).forEach(\n    /**\n     * @param {EventHandler} eventHandler\n     */\n    eventHandler => {\n      try {\n        eventHandler.apply(this, args);\n      } catch (error) {\n        console.error(error);\n      }\n    });\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  removeListener(eventName, eventHandler) {\n    const currentHandlers = this._getHandlersByEventName(eventName);\n    const index = currentHandlers.indexOf(eventHandler);\n    if (index > -1) {\n      currentHandlers.splice(index, 1);\n    }\n  }\n\n  /**\n   * @param {string} eventName\n   */\n  removeAllListeners(eventName) {\n    if (this.events[eventName] !== undefined) {\n      // https://github.com/sweetalert2/sweetalert2/pull/2763#discussion_r1749239222\n      this.events[eventName].length = 0;\n    }\n  }\n  reset() {\n    this.events = {};\n  }\n}\n\nglobalState.eventEmitter = new EventEmitter();\n\n/**\n * @param {string} eventName\n * @param {EventHandler} eventHandler\n */\nconst on = (eventName, eventHandler) => {\n  globalState.eventEmitter.on(eventName, eventHandler);\n};\n\n/**\n * @param {string} eventName\n * @param {EventHandler} eventHandler\n */\nconst once = (eventName, eventHandler) => {\n  globalState.eventEmitter.once(eventName, eventHandler);\n};\n\n/**\n * @param {string} [eventName]\n * @param {EventHandler} [eventHandler]\n */\nconst off = (eventName, eventHandler) => {\n  // Remove all handlers for all events\n  if (!eventName) {\n    globalState.eventEmitter.reset();\n    return;\n  }\n  if (eventHandler) {\n    // Remove a specific handler\n    globalState.eventEmitter.removeListener(eventName, eventHandler);\n  } else {\n    // Remove all handlers for a specific event\n    globalState.eventEmitter.removeAllListeners(eventName);\n  }\n};\n\nvar staticMethods = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  argsToParams: argsToParams,\n  bindClickHandler: bindClickHandler,\n  clickCancel: clickCancel,\n  clickConfirm: clickConfirm,\n  clickDeny: clickDeny,\n  enableLoading: showLoading,\n  fire: fire,\n  getActions: getActions,\n  getCancelButton: getCancelButton,\n  getCloseButton: getCloseButton,\n  getConfirmButton: getConfirmButton,\n  getContainer: getContainer,\n  getDenyButton: getDenyButton,\n  getFocusableElements: getFocusableElements,\n  getFooter: getFooter,\n  getHtmlContainer: getHtmlContainer,\n  getIcon: getIcon,\n  getIconContent: getIconContent,\n  getImage: getImage,\n  getInputLabel: getInputLabel,\n  getLoader: getLoader,\n  getPopup: getPopup,\n  getProgressSteps: getProgressSteps,\n  getTimerLeft: getTimerLeft,\n  getTimerProgressBar: getTimerProgressBar,\n  getTitle: getTitle,\n  getValidationMessage: getValidationMessage,\n  increaseTimer: increaseTimer,\n  isDeprecatedParameter: isDeprecatedParameter,\n  isLoading: isLoading,\n  isTimerRunning: isTimerRunning,\n  isUpdatableParameter: isUpdatableParameter,\n  isValidParameter: isValidParameter,\n  isVisible: isVisible,\n  mixin: mixin,\n  off: off,\n  on: on,\n  once: once,\n  resumeTimer: resumeTimer,\n  showLoading: showLoading,\n  stopTimer: stopTimer,\n  toggleTimer: toggleTimer\n});\n\nclass Timer {\n  /**\n   * @param {Function} callback\n   * @param {number} delay\n   */\n  constructor(callback, delay) {\n    this.callback = callback;\n    this.remaining = delay;\n    this.running = false;\n    this.start();\n  }\n\n  /**\n   * @returns {number}\n   */\n  start() {\n    if (!this.running) {\n      this.running = true;\n      this.started = new Date();\n      this.id = setTimeout(this.callback, this.remaining);\n    }\n    return this.remaining;\n  }\n\n  /**\n   * @returns {number}\n   */\n  stop() {\n    if (this.started && this.running) {\n      this.running = false;\n      clearTimeout(this.id);\n      this.remaining -= new Date().getTime() - this.started.getTime();\n    }\n    return this.remaining;\n  }\n\n  /**\n   * @param {number} n\n   * @returns {number}\n   */\n  increase(n) {\n    const running = this.running;\n    if (running) {\n      this.stop();\n    }\n    this.remaining += n;\n    if (running) {\n      this.start();\n    }\n    return this.remaining;\n  }\n\n  /**\n   * @returns {number}\n   */\n  getTimerLeft() {\n    if (this.running) {\n      this.stop();\n      this.start();\n    }\n    return this.remaining;\n  }\n\n  /**\n   * @returns {boolean}\n   */\n  isRunning() {\n    return this.running;\n  }\n}\n\nconst swalStringParams = ['swal-title', 'swal-html', 'swal-footer'];\n\n/**\n * @param {SweetAlertOptions} params\n * @returns {SweetAlertOptions}\n */\nconst getTemplateParams = params => {\n  const template = typeof params.template === 'string' ? (/** @type {HTMLTemplateElement} */document.querySelector(params.template)) : params.template;\n  if (!template) {\n    return {};\n  }\n  /** @type {DocumentFragment} */\n  const templateContent = template.content;\n  showWarningsForElements(templateContent);\n  const result = Object.assign(getSwalParams(templateContent), getSwalFunctionParams(templateContent), getSwalButtons(templateContent), getSwalImage(templateContent), getSwalIcon(templateContent), getSwalInput(templateContent), getSwalStringParams(templateContent, swalStringParams));\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Record<string, any>}\n */\nconst getSwalParams = templateContent => {\n  /** @type {Record<string, any>} */\n  const result = {};\n  /** @type {HTMLElement[]} */\n  const swalParams = Array.from(templateContent.querySelectorAll('swal-param'));\n  swalParams.forEach(param => {\n    showWarningsForAttributes(param, ['name', 'value']);\n    const paramName = /** @type {keyof SweetAlertOptions} */param.getAttribute('name');\n    const value = param.getAttribute('value');\n    if (!paramName || !value) {\n      return;\n    }\n    if (typeof defaultParams[paramName] === 'boolean') {\n      result[paramName] = value !== 'false';\n    } else if (typeof defaultParams[paramName] === 'object') {\n      result[paramName] = JSON.parse(value);\n    } else {\n      result[paramName] = value;\n    }\n  });\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Record<string, any>}\n */\nconst getSwalFunctionParams = templateContent => {\n  /** @type {Record<string, any>} */\n  const result = {};\n  /** @type {HTMLElement[]} */\n  const swalFunctions = Array.from(templateContent.querySelectorAll('swal-function-param'));\n  swalFunctions.forEach(param => {\n    const paramName = /** @type {keyof SweetAlertOptions} */param.getAttribute('name');\n    const value = param.getAttribute('value');\n    if (!paramName || !value) {\n      return;\n    }\n    result[paramName] = new Function(`return ${value}`)();\n  });\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Record<string, any>}\n */\nconst getSwalButtons = templateContent => {\n  /** @type {Record<string, any>} */\n  const result = {};\n  /** @type {HTMLElement[]} */\n  const swalButtons = Array.from(templateContent.querySelectorAll('swal-button'));\n  swalButtons.forEach(button => {\n    showWarningsForAttributes(button, ['type', 'color', 'aria-label']);\n    const type = button.getAttribute('type');\n    if (!type || !['confirm', 'cancel', 'deny'].includes(type)) {\n      return;\n    }\n    result[`${type}ButtonText`] = button.innerHTML;\n    result[`show${capitalizeFirstLetter(type)}Button`] = true;\n    if (button.hasAttribute('color')) {\n      result[`${type}ButtonColor`] = button.getAttribute('color');\n    }\n    if (button.hasAttribute('aria-label')) {\n      result[`${type}ButtonAriaLabel`] = button.getAttribute('aria-label');\n    }\n  });\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Pick<SweetAlertOptions, 'imageUrl' | 'imageWidth' | 'imageHeight' | 'imageAlt'>}\n */\nconst getSwalImage = templateContent => {\n  const result = {};\n  /** @type {HTMLElement | null} */\n  const image = templateContent.querySelector('swal-image');\n  if (image) {\n    showWarningsForAttributes(image, ['src', 'width', 'height', 'alt']);\n    if (image.hasAttribute('src')) {\n      result.imageUrl = image.getAttribute('src') || undefined;\n    }\n    if (image.hasAttribute('width')) {\n      result.imageWidth = image.getAttribute('width') || undefined;\n    }\n    if (image.hasAttribute('height')) {\n      result.imageHeight = image.getAttribute('height') || undefined;\n    }\n    if (image.hasAttribute('alt')) {\n      result.imageAlt = image.getAttribute('alt') || undefined;\n    }\n  }\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Record<string, any>}\n */\nconst getSwalIcon = templateContent => {\n  const result = {};\n  /** @type {HTMLElement | null} */\n  const icon = templateContent.querySelector('swal-icon');\n  if (icon) {\n    showWarningsForAttributes(icon, ['type', 'color']);\n    if (icon.hasAttribute('type')) {\n      result.icon = icon.getAttribute('type');\n    }\n    if (icon.hasAttribute('color')) {\n      result.iconColor = icon.getAttribute('color');\n    }\n    result.iconHtml = icon.innerHTML;\n  }\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Record<string, any>}\n */\nconst getSwalInput = templateContent => {\n  /** @type {Record<string, any>} */\n  const result = {};\n  /** @type {HTMLElement | null} */\n  const input = templateContent.querySelector('swal-input');\n  if (input) {\n    showWarningsForAttributes(input, ['type', 'label', 'placeholder', 'value']);\n    result.input = input.getAttribute('type') || 'text';\n    if (input.hasAttribute('label')) {\n      result.inputLabel = input.getAttribute('label');\n    }\n    if (input.hasAttribute('placeholder')) {\n      result.inputPlaceholder = input.getAttribute('placeholder');\n    }\n    if (input.hasAttribute('value')) {\n      result.inputValue = input.getAttribute('value');\n    }\n  }\n  /** @type {HTMLElement[]} */\n  const inputOptions = Array.from(templateContent.querySelectorAll('swal-input-option'));\n  if (inputOptions.length) {\n    result.inputOptions = {};\n    inputOptions.forEach(option => {\n      showWarningsForAttributes(option, ['value']);\n      const optionValue = option.getAttribute('value');\n      if (!optionValue) {\n        return;\n      }\n      const optionName = option.innerHTML;\n      result.inputOptions[optionValue] = optionName;\n    });\n  }\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @param {string[]} paramNames\n * @returns {Record<string, any>}\n */\nconst getSwalStringParams = (templateContent, paramNames) => {\n  /** @type {Record<string, any>} */\n  const result = {};\n  for (const i in paramNames) {\n    const paramName = paramNames[i];\n    /** @type {HTMLElement | null} */\n    const tag = templateContent.querySelector(paramName);\n    if (tag) {\n      showWarningsForAttributes(tag, []);\n      result[paramName.replace(/^swal-/, '')] = tag.innerHTML.trim();\n    }\n  }\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n */\nconst showWarningsForElements = templateContent => {\n  const allowedElements = swalStringParams.concat(['swal-param', 'swal-function-param', 'swal-button', 'swal-image', 'swal-icon', 'swal-input', 'swal-input-option']);\n  Array.from(templateContent.children).forEach(el => {\n    const tagName = el.tagName.toLowerCase();\n    if (!allowedElements.includes(tagName)) {\n      warn(`Unrecognized element <${tagName}>`);\n    }\n  });\n};\n\n/**\n * @param {HTMLElement} el\n * @param {string[]} allowedAttributes\n */\nconst showWarningsForAttributes = (el, allowedAttributes) => {\n  Array.from(el.attributes).forEach(attribute => {\n    if (allowedAttributes.indexOf(attribute.name) === -1) {\n      warn([`Unrecognized attribute \"${attribute.name}\" on <${el.tagName.toLowerCase()}>.`, `${allowedAttributes.length ? `Allowed attributes are: ${allowedAttributes.join(', ')}` : 'To set the value, use HTML within the element.'}`]);\n    }\n  });\n};\n\nconst SHOW_CLASS_TIMEOUT = 10;\n\n/**\n * Open popup, add necessary classes and styles, fix scrollbar\n *\n * @param {SweetAlertOptions} params\n */\nconst openPopup = params => {\n  const container = getContainer();\n  const popup = getPopup();\n  if (typeof params.willOpen === 'function') {\n    params.willOpen(popup);\n  }\n  globalState.eventEmitter.emit('willOpen', popup);\n  const bodyStyles = window.getComputedStyle(document.body);\n  const initialBodyOverflow = bodyStyles.overflowY;\n  addClasses(container, popup, params);\n\n  // scrolling is 'hidden' until animation is done, after that 'auto'\n  setTimeout(() => {\n    setScrollingVisibility(container, popup);\n  }, SHOW_CLASS_TIMEOUT);\n  if (isModal()) {\n    fixScrollContainer(container, params.scrollbarPadding, initialBodyOverflow);\n    setAriaHidden();\n  }\n  if (!isToast() && !globalState.previousActiveElement) {\n    globalState.previousActiveElement = document.activeElement;\n  }\n  if (typeof params.didOpen === 'function') {\n    setTimeout(() => params.didOpen(popup));\n  }\n  globalState.eventEmitter.emit('didOpen', popup);\n  removeClass(container, swalClasses['no-transition']);\n};\n\n/**\n * @param {AnimationEvent} event\n */\nconst swalOpenAnimationFinished = event => {\n  const popup = getPopup();\n  if (event.target !== popup) {\n    return;\n  }\n  const container = getContainer();\n  popup.removeEventListener('animationend', swalOpenAnimationFinished);\n  popup.removeEventListener('transitionend', swalOpenAnimationFinished);\n  container.style.overflowY = 'auto';\n};\n\n/**\n * @param {HTMLElement} container\n * @param {HTMLElement} popup\n */\nconst setScrollingVisibility = (container, popup) => {\n  if (hasCssAnimation(popup)) {\n    container.style.overflowY = 'hidden';\n    popup.addEventListener('animationend', swalOpenAnimationFinished);\n    popup.addEventListener('transitionend', swalOpenAnimationFinished);\n  } else {\n    container.style.overflowY = 'auto';\n  }\n};\n\n/**\n * @param {HTMLElement} container\n * @param {boolean} scrollbarPadding\n * @param {string} initialBodyOverflow\n */\nconst fixScrollContainer = (container, scrollbarPadding, initialBodyOverflow) => {\n  iOSfix();\n  if (scrollbarPadding && initialBodyOverflow !== 'hidden') {\n    replaceScrollbarWithPadding(initialBodyOverflow);\n  }\n\n  // sweetalert2/issues/1247\n  setTimeout(() => {\n    container.scrollTop = 0;\n  });\n};\n\n/**\n * @param {HTMLElement} container\n * @param {HTMLElement} popup\n * @param {SweetAlertOptions} params\n */\nconst addClasses = (container, popup, params) => {\n  addClass(container, params.showClass.backdrop);\n  if (params.animation) {\n    // this workaround with opacity is needed for https://github.com/sweetalert2/sweetalert2/issues/2059\n    popup.style.setProperty('opacity', '0', 'important');\n    show(popup, 'grid');\n    setTimeout(() => {\n      // Animate popup right after showing it\n      addClass(popup, params.showClass.popup);\n      // and remove the opacity workaround\n      popup.style.removeProperty('opacity');\n    }, SHOW_CLASS_TIMEOUT); // 10ms in order to fix #2062\n  } else {\n    show(popup, 'grid');\n  }\n  addClass([document.documentElement, document.body], swalClasses.shown);\n  if (params.heightAuto && params.backdrop && !params.toast) {\n    addClass([document.documentElement, document.body], swalClasses['height-auto']);\n  }\n};\n\nvar defaultInputValidators = {\n  /**\n   * @param {string} string\n   * @param {string} [validationMessage]\n   * @returns {Promise<string | void>}\n   */\n  email: (string, validationMessage) => {\n    return /^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z0-9-]+$/.test(string) ? Promise.resolve() : Promise.resolve(validationMessage || 'Invalid email address');\n  },\n  /**\n   * @param {string} string\n   * @param {string} [validationMessage]\n   * @returns {Promise<string | void>}\n   */\n  url: (string, validationMessage) => {\n    // taken from https://stackoverflow.com/a/3809435 with a small change from #1306 and #2013\n    return /^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-z]{2,63}\\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(string) ? Promise.resolve() : Promise.resolve(validationMessage || 'Invalid URL');\n  }\n};\n\n/**\n * @param {SweetAlertOptions} params\n */\nfunction setDefaultInputValidators(params) {\n  // Use default `inputValidator` for supported input types if not provided\n  if (params.inputValidator) {\n    return;\n  }\n  if (params.input === 'email') {\n    params.inputValidator = defaultInputValidators['email'];\n  }\n  if (params.input === 'url') {\n    params.inputValidator = defaultInputValidators['url'];\n  }\n}\n\n/**\n * @param {SweetAlertOptions} params\n */\nfunction validateCustomTargetElement(params) {\n  // Determine if the custom target element is valid\n  if (!params.target || typeof params.target === 'string' && !document.querySelector(params.target) || typeof params.target !== 'string' && !params.target.appendChild) {\n    warn('Target parameter is not valid, defaulting to \"body\"');\n    params.target = 'body';\n  }\n}\n\n/**\n * Set type, text and actions on popup\n *\n * @param {SweetAlertOptions} params\n */\nfunction setParameters(params) {\n  setDefaultInputValidators(params);\n\n  // showLoaderOnConfirm && preConfirm\n  if (params.showLoaderOnConfirm && !params.preConfirm) {\n    warn('showLoaderOnConfirm is set to true, but preConfirm is not defined.\\n' + 'showLoaderOnConfirm should be used together with preConfirm, see usage example:\\n' + 'https://sweetalert2.github.io/#ajax-request');\n  }\n  validateCustomTargetElement(params);\n\n  // Replace newlines with <br> in title\n  if (typeof params.title === 'string') {\n    params.title = params.title.split('\\n').join('<br />');\n  }\n  init(params);\n}\n\n/** @type {SweetAlert} */\nlet currentInstance;\nvar _promise = /*#__PURE__*/new WeakMap();\nclass SweetAlert {\n  /**\n   * @param {...any} args\n   * @this {SweetAlert}\n   */\n  constructor(...args) {\n    /**\n     * @type {Promise<SweetAlertResult>}\n     */\n    _classPrivateFieldInitSpec(this, _promise, void 0);\n    // Prevent run in Node env\n    if (typeof window === 'undefined') {\n      return;\n    }\n    currentInstance = this;\n\n    // @ts-ignore\n    const outerParams = Object.freeze(this.constructor.argsToParams(args));\n\n    /** @type {Readonly<SweetAlertOptions>} */\n    this.params = outerParams;\n\n    /** @type {boolean} */\n    this.isAwaitingPromise = false;\n    _classPrivateFieldSet2(_promise, this, this._main(currentInstance.params));\n  }\n  _main(userParams, mixinParams = {}) {\n    showWarningsForParams(Object.assign({}, mixinParams, userParams));\n    if (globalState.currentInstance) {\n      const swalPromiseResolve = privateMethods.swalPromiseResolve.get(globalState.currentInstance);\n      const {\n        isAwaitingPromise\n      } = globalState.currentInstance;\n      globalState.currentInstance._destroy();\n      if (!isAwaitingPromise) {\n        swalPromiseResolve({\n          isDismissed: true\n        });\n      }\n      if (isModal()) {\n        unsetAriaHidden();\n      }\n    }\n    globalState.currentInstance = currentInstance;\n    const innerParams = prepareParams(userParams, mixinParams);\n    setParameters(innerParams);\n    Object.freeze(innerParams);\n\n    // clear the previous timer\n    if (globalState.timeout) {\n      globalState.timeout.stop();\n      delete globalState.timeout;\n    }\n\n    // clear the restore focus timeout\n    clearTimeout(globalState.restoreFocusTimeout);\n    const domCache = populateDomCache(currentInstance);\n    render(currentInstance, innerParams);\n    privateProps.innerParams.set(currentInstance, innerParams);\n    return swalPromise(currentInstance, domCache, innerParams);\n  }\n\n  // `catch` cannot be the name of a module export, so we define our thenable methods here instead\n  then(onFulfilled) {\n    return _classPrivateFieldGet2(_promise, this).then(onFulfilled);\n  }\n  finally(onFinally) {\n    return _classPrivateFieldGet2(_promise, this).finally(onFinally);\n  }\n}\n\n/**\n * @param {SweetAlert} instance\n * @param {DomCache} domCache\n * @param {SweetAlertOptions} innerParams\n * @returns {Promise}\n */\nconst swalPromise = (instance, domCache, innerParams) => {\n  return new Promise((resolve, reject) => {\n    // functions to handle all closings/dismissals\n    /**\n     * @param {DismissReason} dismiss\n     */\n    const dismissWith = dismiss => {\n      instance.close({\n        isDismissed: true,\n        dismiss\n      });\n    };\n    privateMethods.swalPromiseResolve.set(instance, resolve);\n    privateMethods.swalPromiseReject.set(instance, reject);\n    domCache.confirmButton.onclick = () => {\n      handleConfirmButtonClick(instance);\n    };\n    domCache.denyButton.onclick = () => {\n      handleDenyButtonClick(instance);\n    };\n    domCache.cancelButton.onclick = () => {\n      handleCancelButtonClick(instance, dismissWith);\n    };\n    domCache.closeButton.onclick = () => {\n      dismissWith(DismissReason.close);\n    };\n    handlePopupClick(innerParams, domCache, dismissWith);\n    addKeydownHandler(globalState, innerParams, dismissWith);\n    handleInputOptionsAndValue(instance, innerParams);\n    openPopup(innerParams);\n    setupTimer(globalState, innerParams, dismissWith);\n    initFocus(domCache, innerParams);\n\n    // Scroll container to top on open (#1247, #1946)\n    setTimeout(() => {\n      domCache.container.scrollTop = 0;\n    });\n  });\n};\n\n/**\n * @param {SweetAlertOptions} userParams\n * @param {SweetAlertOptions} mixinParams\n * @returns {SweetAlertOptions}\n */\nconst prepareParams = (userParams, mixinParams) => {\n  const templateParams = getTemplateParams(userParams);\n  const params = Object.assign({}, defaultParams, mixinParams, templateParams, userParams); // precedence is described in #2131\n  params.showClass = Object.assign({}, defaultParams.showClass, params.showClass);\n  params.hideClass = Object.assign({}, defaultParams.hideClass, params.hideClass);\n  if (params.animation === false) {\n    params.showClass = {\n      backdrop: 'swal2-noanimation'\n    };\n    params.hideClass = {};\n  }\n  return params;\n};\n\n/**\n * @param {SweetAlert} instance\n * @returns {DomCache}\n */\nconst populateDomCache = instance => {\n  const domCache = {\n    popup: getPopup(),\n    container: getContainer(),\n    actions: getActions(),\n    confirmButton: getConfirmButton(),\n    denyButton: getDenyButton(),\n    cancelButton: getCancelButton(),\n    loader: getLoader(),\n    closeButton: getCloseButton(),\n    validationMessage: getValidationMessage(),\n    progressSteps: getProgressSteps()\n  };\n  privateProps.domCache.set(instance, domCache);\n  return domCache;\n};\n\n/**\n * @param {GlobalState} globalState\n * @param {SweetAlertOptions} innerParams\n * @param {Function} dismissWith\n */\nconst setupTimer = (globalState, innerParams, dismissWith) => {\n  const timerProgressBar = getTimerProgressBar();\n  hide(timerProgressBar);\n  if (innerParams.timer) {\n    globalState.timeout = new Timer(() => {\n      dismissWith('timer');\n      delete globalState.timeout;\n    }, innerParams.timer);\n    if (innerParams.timerProgressBar) {\n      show(timerProgressBar);\n      applyCustomClass(timerProgressBar, innerParams, 'timerProgressBar');\n      setTimeout(() => {\n        if (globalState.timeout && globalState.timeout.running) {\n          // timer can be already stopped or unset at this point\n          animateTimerProgressBar(innerParams.timer);\n        }\n      });\n    }\n  }\n};\n\n/**\n * Initialize focus in the popup:\n *\n * 1. If `toast` is `true`, don't steal focus from the document.\n * 2. Else if there is an [autofocus] element, focus it.\n * 3. Else if `focusConfirm` is `true` and confirm button is visible, focus it.\n * 4. Else if `focusDeny` is `true` and deny button is visible, focus it.\n * 5. Else if `focusCancel` is `true` and cancel button is visible, focus it.\n * 6. Else focus the first focusable element in a popup (if any).\n *\n * @param {DomCache} domCache\n * @param {SweetAlertOptions} innerParams\n */\nconst initFocus = (domCache, innerParams) => {\n  if (innerParams.toast) {\n    return;\n  }\n  // TODO: this is dumb, remove `allowEnterKey` param in the next major version\n  if (!callIfFunction(innerParams.allowEnterKey)) {\n    warnAboutDeprecation('allowEnterKey');\n    blurActiveElement();\n    return;\n  }\n  if (focusAutofocus(domCache)) {\n    return;\n  }\n  if (focusButton(domCache, innerParams)) {\n    return;\n  }\n  setFocus(-1, 1);\n};\n\n/**\n * @param {DomCache} domCache\n * @returns {boolean}\n */\nconst focusAutofocus = domCache => {\n  const autofocusElements = Array.from(domCache.popup.querySelectorAll('[autofocus]'));\n  for (const autofocusElement of autofocusElements) {\n    if (autofocusElement instanceof HTMLElement && isVisible$1(autofocusElement)) {\n      autofocusElement.focus();\n      return true;\n    }\n  }\n  return false;\n};\n\n/**\n * @param {DomCache} domCache\n * @param {SweetAlertOptions} innerParams\n * @returns {boolean}\n */\nconst focusButton = (domCache, innerParams) => {\n  if (innerParams.focusDeny && isVisible$1(domCache.denyButton)) {\n    domCache.denyButton.focus();\n    return true;\n  }\n  if (innerParams.focusCancel && isVisible$1(domCache.cancelButton)) {\n    domCache.cancelButton.focus();\n    return true;\n  }\n  if (innerParams.focusConfirm && isVisible$1(domCache.confirmButton)) {\n    domCache.confirmButton.focus();\n    return true;\n  }\n  return false;\n};\nconst blurActiveElement = () => {\n  if (document.activeElement instanceof HTMLElement && typeof document.activeElement.blur === 'function') {\n    document.activeElement.blur();\n  }\n};\n\n// Dear russian users visiting russian sites. Let's have fun.\nif (typeof window !== 'undefined' && /^ru\\b/.test(navigator.language) && location.host.match(/\\.(ru|su|by|xn--p1ai)$/)) {\n  const now = new Date();\n  const initiationDate = localStorage.getItem('swal-initiation');\n  if (!initiationDate) {\n    localStorage.setItem('swal-initiation', `${now}`);\n  } else if ((now.getTime() - Date.parse(initiationDate)) / (1000 * 60 * 60 * 24) > 3) {\n    setTimeout(() => {\n      document.body.style.pointerEvents = 'none';\n      const ukrainianAnthem = document.createElement('audio');\n      ukrainianAnthem.src = 'https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3';\n      ukrainianAnthem.loop = true;\n      document.body.appendChild(ukrainianAnthem);\n      setTimeout(() => {\n        ukrainianAnthem.play().catch(() => {\n          // ignore\n        });\n      }, 2500);\n    }, 500);\n  }\n}\n\n// Assign instance methods from src/instanceMethods/*.js to prototype\nSweetAlert.prototype.disableButtons = disableButtons;\nSweetAlert.prototype.enableButtons = enableButtons;\nSweetAlert.prototype.getInput = getInput;\nSweetAlert.prototype.disableInput = disableInput;\nSweetAlert.prototype.enableInput = enableInput;\nSweetAlert.prototype.hideLoading = hideLoading;\nSweetAlert.prototype.disableLoading = hideLoading;\nSweetAlert.prototype.showValidationMessage = showValidationMessage;\nSweetAlert.prototype.resetValidationMessage = resetValidationMessage;\nSweetAlert.prototype.close = close;\nSweetAlert.prototype.closePopup = close;\nSweetAlert.prototype.closeModal = close;\nSweetAlert.prototype.closeToast = close;\nSweetAlert.prototype.rejectPromise = rejectPromise;\nSweetAlert.prototype.update = update;\nSweetAlert.prototype._destroy = _destroy;\n\n// Assign static methods from src/staticMethods/*.js to constructor\nObject.assign(SweetAlert, staticMethods);\n\n// Proxy to instance methods to constructor, for now, for backwards compatibility\nObject.keys(instanceMethods).forEach(key => {\n  /**\n   * @param {...any} args\n   * @returns {any | undefined}\n   */\n  SweetAlert[key] = function (...args) {\n    if (currentInstance && currentInstance[key]) {\n      return currentInstance[key](...args);\n    }\n    return null;\n  };\n});\nSweetAlert.DismissReason = DismissReason;\nSweetAlert.version = '11.22.0';\n\nconst Swal = SweetAlert;\n// @ts-ignore\nSwal.default = Swal;\n\n\n\"undefined\"!=typeof document&&function(e,t){var n=e.createElement(\"style\");if(e.getElementsByTagName(\"head\")[0].appendChild(n),n.styleSheet)n.styleSheet.disabled||(n.styleSheet.cssText=t);else try{n.innerHTML=t}catch(e){n.innerText=t}}(document,\":root{--swal2-outline: 0 0 0 3px rgba(100, 150, 200, 0.5);--swal2-container-padding: 0.625em;--swal2-backdrop: rgba(0, 0, 0, 0.4);--swal2-backdrop-transition: background-color 0.1s;--swal2-width: 32em;--swal2-padding: 0 0 1.25em;--swal2-border: none;--swal2-border-radius: 0.3125rem;--swal2-background: white;--swal2-color: #545454;--swal2-show-animation: swal2-show 0.3s;--swal2-hide-animation: swal2-hide 0.15s forwards;--swal2-icon-zoom: 1;--swal2-icon-animations: true;--swal2-title-padding: 0.8em 1em 0;--swal2-html-container-padding: 1em 1.6em 0.3em;--swal2-input-border: 1px solid #d9d9d9;--swal2-input-border-radius: 0.1875em;--swal2-input-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-background: transparent;--swal2-input-transition: border-color 0.2s, box-shadow 0.2s;--swal2-input-hover-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-focus-border: 1px solid #b4dbed;--swal2-input-focus-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px $swal2-outline-color;--swal2-progress-step-background: #add8e6;--swal2-validation-message-background: #f0f0f0;--swal2-validation-message-color: #666;--swal2-footer-border-color: #eee;--swal2-footer-background: transparent;--swal2-footer-color: inherit;--swal2-close-button-position: initial;--swal2-close-button-inset: auto;--swal2-close-button-font-size: 2.5em;--swal2-close-button-color: #ccc;--swal2-close-button-transition: color 0.2s, box-shadow 0.2s;--swal2-close-button-outline: initial;--swal2-close-button-box-shadow: inset 0 0 0 3px transparent;--swal2-close-button-focus-box-shadow: inset var(--swal2-outline);--swal2-close-button-hover-transform: none;--swal2-actions-justify-content: center;--swal2-actions-width: auto;--swal2-actions-margin: 1.25em auto 0;--swal2-actions-padding: 0;--swal2-actions-border-radius: 0;--swal2-actions-background: transparent;--swal2-action-button-transition: background-color 0.2s, box-shadow 0.2s;--swal2-action-button-hover: black 10%;--swal2-action-button-active: black 10%;--swal2-confirm-button-box-shadow: none;--swal2-confirm-button-border-radius: 0.25em;--swal2-confirm-button-background-color: #7066e0;--swal2-confirm-button-color: #fff;--swal2-deny-button-box-shadow: none;--swal2-deny-button-border-radius: 0.25em;--swal2-deny-button-background-color: #dc3741;--swal2-deny-button-color: #fff;--swal2-cancel-button-box-shadow: none;--swal2-cancel-button-border-radius: 0.25em;--swal2-cancel-button-background-color: #6e7881;--swal2-cancel-button-color: #fff;--swal2-toast-show-animation: swal2-toast-show 0.5s;--swal2-toast-hide-animation: swal2-toast-hide 0.1s forwards;--swal2-toast-border: none;--swal2-toast-box-shadow: 0 0 1px hsl(0deg 0% 0% / 0.075), 0 1px 2px hsl(0deg 0% 0% / 0.075), 1px 2px 4px hsl(0deg 0% 0% / 0.075), 1px 3px 8px hsl(0deg 0% 0% / 0.075), 2px 4px 16px hsl(0deg 0% 0% / 0.075)}[data-swal2-theme=dark]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}@media(prefers-color-scheme: dark){[data-swal2-theme=auto]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px var(--swal2-backdrop)}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}@media print{body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown) .swal2-container{position:static !important}}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:\\\"top-start     top            top-end\\\" \\\"center-start  center         center-end\\\" \\\"bottom-start  bottom-center  bottom-end\\\";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:var(--swal2-container-padding);overflow-x:hidden;transition:var(--swal2-backdrop-transition);-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:var(--swal2-backdrop)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container)[popover]{width:auto;border:0}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:var(--swal2-width);max-width:100%;padding:var(--swal2-padding);border:var(--swal2-border);border-radius:var(--swal2-border-radius);background:var(--swal2-background);color:var(--swal2-color);font-family:inherit;font-size:1rem;container-name:swal2-popup}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable{cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable div:where(.swal2-icon){cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging{cursor:grabbing}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging div:where(.swal2-icon){cursor:grabbing}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:var(--swal2-title-padding);color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word;cursor:initial}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:var(--swal2-actions-justify-content);width:var(--swal2-actions-width);margin:var(--swal2-actions-margin);padding:var(--swal2-actions-padding);border-radius:var(--swal2-actions-border-radius);background:var(--swal2-actions-background)}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:var(--swal2-action-button-transition);border:none;box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm){border-radius:var(--swal2-confirm-button-border-radius);background:initial;background-color:var(--swal2-confirm-button-background-color);box-shadow:var(--swal2-confirm-button-box-shadow);color:var(--swal2-confirm-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):hover{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):active{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny){border-radius:var(--swal2-deny-button-border-radius);background:initial;background-color:var(--swal2-deny-button-background-color);box-shadow:var(--swal2-deny-button-box-shadow);color:var(--swal2-deny-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):hover{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):active{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel){border-radius:var(--swal2-cancel-button-border-radius);background:initial;background-color:var(--swal2-cancel-button-background-color);box-shadow:var(--swal2-cancel-button-box-shadow);color:var(--swal2-cancel-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):hover{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):active{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):focus-visible{outline:none;box-shadow:var(--swal2-action-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-styled)[disabled]:not(.swal2-loading){opacity:.4}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid var(--swal2-footer-border-color);background:var(--swal2-footer-background);color:var(--swal2-footer-color);font-size:1em;text-align:center;cursor:initial}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:var(--swal2-border-radius);border-bottom-left-radius:var(--swal2-border-radius)}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em;cursor:initial}div:where(.swal2-container) button:where(.swal2-close){position:var(--swal2-close-button-position);inset:var(--swal2-close-button-inset);z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:var(--swal2-close-button-transition);border:none;border-radius:var(--swal2-border-radius);outline:var(--swal2-close-button-outline);background:rgba(0,0,0,0);color:var(--swal2-close-button-color);font-family:monospace;font-size:var(--swal2-close-button-font-size);cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:var(--swal2-close-button-hover-transform);background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus-visible{outline:none;box-shadow:var(--swal2-close-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-html-container){z-index:1;justify-content:center;margin:0;padding:var(--swal2-html-container-padding);overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word;cursor:initial}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:var(--swal2-input-transition);border:var(--swal2-input-border);border-radius:var(--swal2-input-border-radius);background:var(--swal2-input-background);box-shadow:var(--swal2-input-box-shadow);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):hover,div:where(.swal2-container) input:where(.swal2-file):hover,div:where(.swal2-container) textarea:where(.swal2-textarea):hover{box-shadow:var(--swal2-input-hover-box-shadow)}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:var(--swal2-input-focus-border);outline:none;box-shadow:var(--swal2-input-focus-box-shadow)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:var(--swal2-background)}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:var(--swal2-input-background);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:var(--swal2-input-background);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:var(--swal2-background);color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:var(--swal2-validation-message-background);color:var(--swal2-validation-message-color);font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:\\\"!\\\";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:var(--swal2-progress-step-background);color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:var(--swal2-progress-step-background)}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;zoom:var(--swal2-icon-zoom);border:.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}}div:where(.swal2-icon).swal2-warning{border-color:#f8bb86;color:#f8bb86}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}}div:where(.swal2-icon).swal2-info{border-color:#3fc3ee;color:#3fc3ee}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}}div:where(.swal2-icon).swal2-question{border-color:#87adbd;color:#87adbd}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:var(--swal2-show-animation)}.swal2-hide{animation:var(--swal2-hide-animation)}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;border:var(--swal2-toast-border);background:var(--swal2-background);box-shadow:var(--swal2-toast-box-shadow);pointer-events:all}.swal2-toast>*{grid-column:2}.swal2-toast h2:where(.swal2-title){margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-toast .swal2-loading{justify-content:center}.swal2-toast input:where(.swal2-input){height:2em;margin:.5em;font-size:1em}.swal2-toast .swal2-validation-message{font-size:1em}.swal2-toast div:where(.swal2-footer){margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-toast button:where(.swal2-close){grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-toast div:where(.swal2-html-container){margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-toast div:where(.swal2-html-container):empty{padding:0}.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-toast div:where(.swal2-actions){justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-toast button:where(.swal2-styled){margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}@container swal2-popup style(--swal2-icon-animations:true){.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}}.swal2-toast.swal2-show{animation:var(--swal2-toast-show-animation)}.swal2-toast.swal2-hide{animation:var(--swal2-toast-hide-animation)}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/sweetalert2/dist/sweetalert2.esm.all.js\n");

/***/ })

};
;