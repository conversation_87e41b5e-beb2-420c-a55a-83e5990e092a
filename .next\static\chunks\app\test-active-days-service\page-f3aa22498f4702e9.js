(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2439,7410],{1420:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(5155),r=s(2115),c=s(6681),i=s(2439);function l(){let{user:e,loading:t}=(0,c.Nu)(),[s,l]=(0,r.useState)(""),[o,n]=(0,r.useState)(null),[d,u]=(0,r.useState)(!1),y=async()=>{if(!s.trim())return void alert("Please enter a user ID");u(!0),n(null);try{console.log("\uD83E\uDDEA Testing active days service for user: ".concat(s)),console.log("1. Testing getUserActiveDays...");let e=await (0,i.nd)(s.trim());console.log("✅ getUserActiveDays result:",e),console.log("2. Testing calculateActiveDays...");let t=await (0,i.i7)(s.trim());console.log("✅ calculateActiveDays result:",t),console.log("3. Testing updateUserActiveDays...");let a=await (0,i.updateUserActiveDays)(s.trim());console.log("✅ updateUserActiveDays result:",a),n({success:!0,currentActiveDays:e,calculation:t,updatedActiveDays:a,message:"Single user test completed successfully"})}catch(e){console.error("❌ Error in single user test:",e),n({success:!1,error:e.message,stack:e.stack,message:"Single user test failed"})}finally{u(!1)}},p=async()=>{u(!0),n(null);try{console.log("\uD83E\uDDEA Testing processAllUsersActiveDays...");let e=await (0,i.mH)();console.log("✅ processAllUsersActiveDays result:",e),n({success:!0,allUsersResult:e,message:"All users test completed successfully"})}catch(e){console.error("❌ Error in all users test:",e),n({success:!1,error:e.message,stack:e.stack,message:"All users test failed"})}finally{u(!1)}};return t?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,a.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Active Days Service"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white mb-2",children:"User ID (for single user test):"}),(0,a.jsx)("input",{type:"text",value:s,onChange:e=>l(e.target.value),className:"w-full p-3 rounded-lg bg-white/10 text-white border border-white/20",placeholder:"Enter user ID"})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("button",{onClick:y,disabled:d,className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:d?"Testing...":"Test Single User"}),(0,a.jsx)("button",{onClick:p,disabled:d,className:"flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:d?"Testing...":"Test All Users"})]}),o&&(0,a.jsxs)("div",{className:"p-4 rounded-lg ".concat(o.success?"bg-green-500/20 border border-green-500/30":"bg-red-500/20 border border-red-500/30"),children:[(0,a.jsx)("h3",{className:"font-bold ".concat(o.success?"text-green-400":"text-red-400"),children:o.success?"Success!":"Failed"}),(0,a.jsx)("p",{className:"text-white mt-2",children:o.message}),void 0!==o.currentActiveDays&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Current Active Days:"}),(0,a.jsx)("p",{className:"text-white/80 text-sm mt-1",children:o.currentActiveDays})]}),o.calculation&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Calculation Result:"}),(0,a.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(o.calculation,null,2)})]}),void 0!==o.updatedActiveDays&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Updated Active Days:"}),(0,a.jsx)("p",{className:"text-white/80 text-sm mt-1",children:o.updatedActiveDays})]}),o.allUsersResult&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"All Users Result:"}),(0,a.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(o.allUsersResult,null,2)})]}),o.error&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-red-500/20 rounded",children:[(0,a.jsx)("h4",{className:"text-red-400 font-semibold",children:"Error Details:"}),(0,a.jsx)("p",{className:"text-white/80 text-sm mt-1",children:o.error}),o.stack&&(0,a.jsx)("pre",{className:"text-white/60 text-xs mt-2 overflow-auto",children:o.stack})]})]})]}),(0,a.jsxs)("div",{className:"mt-8 p-4 bg-white/5 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,a.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Enter a user ID to test single user functions"}),(0,a.jsx)("li",{children:'• Click "Test Single User" to test calculation and update functions'}),(0,a.jsx)("li",{children:'• Click "Test All Users" to test the batch processing function'}),(0,a.jsx)("li",{children:"• Check browser console for detailed logs"})]})]})]})})})}},2439:(e,t,s)=>{"use strict";s.d(t,{ActiveDaysService:()=>l,S3:()=>y,i7:()=>o,isUserPlanExpired:()=>p,mH:()=>d,nd:()=>u,updateUserActiveDays:()=>n});var a=s(6104),r=s(5317);let c={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},i={users:"users"};class l{static async calculateActiveDays(e){try{var t,s;let c,o=await (0,r.x7)((0,r.H9)(a.db,i.users,e));if(!o.exists())return console.error("User ".concat(e," not found")),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let n=o.data(),d=(null==(t=n.joinedDate)?void 0:t.toDate())||new Date,u=null==(s=n.lastActiveDayUpdate)?void 0:s.toDate(),y=n.activeDays||0,p=n.plan||"Trial",D=new Date,v=D.toDateString(),g=u?u.toDateString():null;if(console.log("\uD83D\uDCC5 Calculating active days for user ".concat(e,":")),console.log("   - Joined: ".concat(d.toDateString())),console.log("   - Current active days: ".concat(y)),console.log("   - Last update: ".concat(g||"Never")),console.log("   - Today: ".concat(v)),console.log("   - Plan: ".concat(p)),console.log("   - Is new day: ".concat(g!==v)),g===v)return console.log("✅ Already updated today for user ".concat(e)),{activeDays:y,shouldUpdate:!1,isNewDay:!1};if("Admin"===p)return console.log("⏭️ Skipping active days increment for admin user ".concat(e)),await l.updateLastActiveDayUpdate(e),{activeDays:y,shouldUpdate:!1,isNewDay:!0};if(await l.isUserOnLeaveToday(e))return console.log("\uD83C\uDFD6️ User ".concat(e," is on leave today, not incrementing active days")),await l.updateLastActiveDayUpdate(e),{activeDays:y,shouldUpdate:!1,isNewDay:!0};return c="Trial"===p?Math.floor((D.getTime()-d.getTime())/864e5)+1:y+1,console.log("\uD83D\uDCC8 New active days calculated: ".concat(y," → ").concat(c)),{activeDays:c,shouldUpdate:c!==y,isNewDay:!0}}catch(t){return console.error("Error calculating active days for user ".concat(e,":"),t),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(e){try{let t=await l.calculateActiveDays(e);if(t.shouldUpdate){let s=(0,r.H9)(a.db,i.users,e);await (0,r.mZ)(s,{[c.activeDays]:t.activeDays,[c.lastActiveDayUpdate]:r.Dc.now()}),console.log("✅ Updated active days for user ".concat(e,": ").concat(t.activeDays))}else t.isNewDay&&await l.updateLastActiveDayUpdate(e);return t.activeDays}catch(t){throw console.error("Error updating active days for user ".concat(e,":"),t),t}}static async updateLastActiveDayUpdate(e){try{let t=(0,r.H9)(a.db,i.users,e);await (0,r.mZ)(t,{[c.lastActiveDayUpdate]:r.Dc.now()})}catch(t){console.error("Error updating last active day timestamp for user ".concat(e,":"),t)}}static async isUserOnLeaveToday(e){try{let{isUserOnLeave:t}=await s.e(9567).then(s.bind(s,9567));return await t(e,new Date)}catch(t){return console.error("Error checking leave status for user ".concat(e,":"),t),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let e=await (0,r.getDocs)((0,r.collection)(a.db,i.users)),t=0,s=0,c=0;for(let a of e.docs)try{t++;let e=await l.calculateActiveDays(a.id);(e.shouldUpdate||e.isNewDay)&&(await l.updateUserActiveDays(a.id),e.shouldUpdate&&s++)}catch(e){c++,console.error("Error processing active days for user ".concat(a.id,":"),e)}return console.log("✅ Daily active days processing complete:"),console.log("   - Processed: ".concat(t," users")),console.log("   - Updated: ".concat(s," users")),console.log("   - Errors: ".concat(c," users")),{processed:t,updated:s,errors:c}}catch(e){throw console.error("Error in daily active days processing:",e),e}}static async getUserActiveDays(e){try{let t=await (0,r.x7)((0,r.H9)(a.db,i.users,e));if(!t.exists())return 0;return t.data().activeDays||0}catch(t){return console.error("Error getting active days for user ".concat(e,":"),t),0}}static async initializeActiveDaysForNewUser(e){try{let t=(0,r.H9)(a.db,i.users,e);await (0,r.mZ)(t,{[c.activeDays]:1,[c.lastActiveDayUpdate]:r.Dc.now()}),console.log("✅ Initialized active days for new user ".concat(e,": Day 1"))}catch(t){throw console.error("Error initializing active days for user ".concat(e,":"),t),t}}static async getActiveDaysDisplay(e){try{let t,s=await (0,r.x7)((0,r.H9)(a.db,i.users,e));if(!s.exists())return{current:0,total:2,displayText:"0/2"};let c=s.data(),l=c.plan||"Trial",o=c.activeDays||0;return t="Trial"===l?2:30,{current:o,total:t,displayText:"".concat(o,"/").concat(t)}}catch(t){return console.error("Error getting active days display for user ".concat(e,":"),t),{current:0,total:2,displayText:"0/2"}}}static async isUserPlanExpired(e){try{let t=await (0,r.x7)((0,r.H9)(a.db,i.users,e));if(!t.exists())return{expired:!0,reason:"User not found"};let s=t.data(),c=s.plan||"Trial",l=s.activeDays||0,o=s.planExpiry;if(console.log("\uD83D\uDCC5 Checking plan expiry for user ".concat(e,":"),{plan:c,activeDays:l,hasPlanExpiry:!!o,planExpiryDate:o?o.toDate():null}),"Admin"===c){let t={expired:!1,activeDays:l};return console.log("\uD83D\uDCC5 Plan expiry result for admin user ".concat(e,":"),t),t}if("Trial"===c){let t=Math.max(0,2-l),s={expired:t<=0,reason:t<=0?"Trial period expired":void 0,daysLeft:t,activeDays:l};return console.log("\uD83D\uDCC5 Plan expiry result for trial user ".concat(e,":"),s),s}if(o){let t=new Date,s=o.toDate(),a=t>s,r=a?0:Math.ceil((s.getTime()-t.getTime())/864e5),c={expired:a,reason:a?"Plan subscription expired":void 0,daysLeft:r,activeDays:l};return console.log("\uD83D\uDCC5 Plan expiry result for user ".concat(e," (using planExpiry field):"),c),c}let n=Math.max(0,30-l),d=l>30,u={expired:d,reason:d?"Plan expired - You have used ".concat(l," days out of 30 allowed days"):void 0,daysLeft:n,activeDays:l};return console.log("\uD83D\uDCC5 Plan expiry result for user ".concat(e,":"),u),u}catch(t){return console.error("Error checking plan expiry for user ".concat(e,":"),t),{expired:!0,reason:"Error checking plan status"}}}static async setPlanExpiryForUser(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30;try{var s;let c=(0,r.H9)(a.db,i.users,e),l=await (0,r.x7)(c);if(!l.exists())return console.error("User ".concat(e," not found")),!1;let o=l.data(),n=o.plan||"Trial";if("Trial"===n||"Admin"===n)return console.log("Skipping plan expiry setup for ".concat(n," user: ").concat(e)),!1;if(o.planExpiry)return console.log("User ".concat(e," already has plan expiry set: ").concat(o.planExpiry.toDate())),!1;let d=(null==(s=o.joinedDate)?void 0:s.toDate())||new Date,u=new Date(d);return u.setDate(u.getDate()+t),await (0,r.mZ)(c,{planExpiry:r.Dc.fromDate(u),planExpirySetDate:r.Dc.now()}),console.log("✅ Set plan expiry for user ".concat(e,": ").concat(u)),!0}catch(t){return console.error("Error setting plan expiry for user ".concat(e,":"),t),!1}}static async forceUpdateActiveDays(e,t,s){try{let l=(0,r.H9)(a.db,i.users,e);await (0,r.mZ)(l,{[c.activeDays]:t,[c.lastActiveDayUpdate]:r.Dc.now()}),console.log("\uD83D\uDD27 Admin ".concat(s," force updated active days for user ").concat(e,": ").concat(t))}catch(t){throw console.error("Error force updating active days for user ".concat(e,":"),t),t}}static async getActiveDaysStatistics(){try{let t=await (0,r.getDocs)((0,r.collection)(a.db,i.users)),s=0,c=0,l=0,o=0,n=0,d=0,u=new Date().toDateString();for(let a of t.docs){var e;let t=a.data(),r=t.plan||"Trial",i=t.activeDays||0,y=null==(e=t.lastActiveDayUpdate)?void 0:e.toDate();s++,n+=i,"Trial"===r?c++:"Admin"===r?o++:l++,y&&y.toDateString()===u&&d++}return{totalUsers:s,trialUsers:c,paidUsers:l,adminUsers:o,averageActiveDays:s>0?Math.round(n/s*100)/100:0,usersUpdatedToday:d}}catch(e){throw console.error("Error getting active days statistics:",e),e}}}let o=l.calculateActiveDays,n=l.updateUserActiveDays,d=l.processAllUsersActiveDays,u=l.getUserActiveDays,y=l.initializeActiveDaysForNewUser;l.getActiveDaysDisplay;let p=l.isUserPlanExpired;l.forceUpdateActiveDays,l.getActiveDaysStatistics},7935:(e,t,s)=>{Promise.resolve().then(s.bind(s,1420))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6681,8441,1684,7358],()=>t(7935)),_N_E=e.O()}]);