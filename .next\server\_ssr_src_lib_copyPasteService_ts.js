"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_copyPasteService_ts";
exports.ids = ["_ssr_src_lib_copyPasteService_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/copyPasteService.ts":
/*!*************************************!*\
  !*** ./src/lib/copyPasteService.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CopyPasteService: () => (/* binding */ CopyPasteService),\n/* harmony export */   checkCopyPastePermission: () => (/* binding */ checkCopyPastePermission),\n/* harmony export */   getCopyPasteStatus: () => (/* binding */ getCopyPasteStatus),\n/* harmony export */   grantCopyPastePermission: () => (/* binding */ grantCopyPastePermission),\n/* harmony export */   processAllUsersCopyPasteReduction: () => (/* binding */ processAllUsersCopyPasteReduction),\n/* harmony export */   reduceCopyPasteDays: () => (/* binding */ reduceCopyPasteDays),\n/* harmony export */   removeCopyPastePermission: () => (/* binding */ removeCopyPastePermission)\n/* harmony export */ });\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n\n\n// Field names and collections\nconst FIELD_NAMES = {\n    quickTranslationAdvantageExpiry: 'quickTranslationAdvantageExpiry',\n    lastCopyPasteReduction: 'lastCopyPasteReduction',\n    plan: 'plan',\n    name: 'name'\n};\nconst COLLECTIONS = {\n    users: 'users'\n};\n// Copy-Paste Permission Management Service\nclass CopyPasteService {\n    /**\n   * Check if user has copy-paste permission\n   */ static async checkCopyPastePermission(userId) {\n        try {\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId));\n            if (!userDoc.exists()) {\n                return {\n                    hasPermission: false,\n                    daysRemaining: 0,\n                    expiryDate: null\n                };\n            }\n            const userData = userDoc.data();\n            const expiryTimestamp = userData[FIELD_NAMES.quickTranslationAdvantageExpiry];\n            if (!expiryTimestamp) {\n                return {\n                    hasPermission: false,\n                    daysRemaining: 0,\n                    expiryDate: null\n                };\n            }\n            const expiryDate = expiryTimestamp.toDate();\n            const now = new Date();\n            const hasPermission = expiryDate > now;\n            const daysRemaining = hasPermission ? Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)) : 0;\n            return {\n                hasPermission,\n                daysRemaining,\n                expiryDate\n            };\n        } catch (error) {\n            console.error(`Error checking copy-paste permission for user ${userId}:`, error);\n            return {\n                hasPermission: false,\n                daysRemaining: 0,\n                expiryDate: null\n            };\n        }\n    }\n    /**\n   * Grant copy-paste permission for specified days\n   */ static async grantCopyPastePermission(userId, days) {\n        try {\n            const expiryDate = new Date();\n            expiryDate.setDate(expiryDate.getDate() + days);\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(userRef, {\n                [FIELD_NAMES.quickTranslationAdvantageExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(expiryDate),\n                [FIELD_NAMES.lastCopyPasteReduction]: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n            });\n            console.log(`✅ Granted copy-paste permission to user ${userId} for ${days} days (expires: ${expiryDate.toDateString()})`);\n        } catch (error) {\n            console.error(`Error granting copy-paste permission to user ${userId}:`, error);\n            throw error;\n        }\n    }\n    /**\n   * Remove copy-paste permission\n   */ static async removeCopyPastePermission(userId) {\n        try {\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(userRef, {\n                [FIELD_NAMES.quickTranslationAdvantageExpiry]: null\n            });\n            console.log(`✅ Removed copy-paste permission from user ${userId}`);\n        } catch (error) {\n            console.error(`Error removing copy-paste permission from user ${userId}:`, error);\n            throw error;\n        }\n    }\n    /**\n   * Reduce copy-paste days by 1 (daily reduction)\n   */ static async reduceCopyPasteDays(userId) {\n        try {\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId));\n            if (!userDoc.exists()) {\n                return {\n                    reduced: false,\n                    daysRemaining: 0,\n                    expired: false\n                };\n            }\n            const userData = userDoc.data();\n            const expiryTimestamp = userData[FIELD_NAMES.quickTranslationAdvantageExpiry];\n            const lastReduction = userData[FIELD_NAMES.lastCopyPasteReduction];\n            if (!expiryTimestamp) {\n                return {\n                    reduced: false,\n                    daysRemaining: 0,\n                    expired: false\n                };\n            }\n            // Check if we already reduced today\n            const today = new Date().toDateString();\n            const lastReductionDate = lastReduction ? lastReduction.toDate().toDateString() : null;\n            if (lastReductionDate === today) {\n                // Already reduced today\n                const expiryDate = expiryTimestamp.toDate();\n                const now = new Date();\n                const daysRemaining = Math.max(0, Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));\n                return {\n                    reduced: false,\n                    daysRemaining,\n                    expired: daysRemaining === 0\n                };\n            }\n            // Reduce by 1 day\n            const currentExpiry = expiryTimestamp.toDate();\n            const newExpiry = new Date(currentExpiry);\n            newExpiry.setDate(newExpiry.getDate() - 1);\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId);\n            // If expired, remove permission entirely\n            if (newExpiry <= new Date()) {\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(userRef, {\n                    [FIELD_NAMES.quickTranslationAdvantageExpiry]: null,\n                    [FIELD_NAMES.lastCopyPasteReduction]: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n                });\n                console.log(`📅 Copy-paste permission expired for user ${userId}`);\n                return {\n                    reduced: true,\n                    daysRemaining: 0,\n                    expired: true\n                };\n            } else {\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(userRef, {\n                    [FIELD_NAMES.quickTranslationAdvantageExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(newExpiry),\n                    [FIELD_NAMES.lastCopyPasteReduction]: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n                });\n                const daysRemaining = Math.ceil((newExpiry.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));\n                console.log(`📅 Reduced copy-paste days for user ${userId}: ${daysRemaining} days remaining`);\n                return {\n                    reduced: true,\n                    daysRemaining,\n                    expired: false\n                };\n            }\n        } catch (error) {\n            console.error(`Error reducing copy-paste days for user ${userId}:`, error);\n            return {\n                reduced: false,\n                daysRemaining: 0,\n                expired: false\n            };\n        }\n    }\n    /**\n   * Process daily copy-paste reduction for all users\n   */ static async processAllUsersCopyPasteReduction() {\n        try {\n            console.log('🔄 Starting daily copy-paste reduction for all users...');\n            const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users));\n            let processed = 0;\n            let reduced = 0;\n            let expired = 0;\n            let errors = 0;\n            for (const userDoc of usersSnapshot.docs){\n                try {\n                    processed++;\n                    const result = await CopyPasteService.reduceCopyPasteDays(userDoc.id);\n                    if (result.reduced) {\n                        reduced++;\n                        if (result.expired) {\n                            expired++;\n                        }\n                    }\n                } catch (error) {\n                    errors++;\n                    console.error(`Error processing copy-paste reduction for user ${userDoc.id}:`, error);\n                }\n            }\n            console.log(`✅ Daily copy-paste reduction complete:`);\n            console.log(`   - Processed: ${processed} users`);\n            console.log(`   - Reduced: ${reduced} users`);\n            console.log(`   - Expired: ${expired} users`);\n            console.log(`   - Errors: ${errors} users`);\n            return {\n                processed,\n                reduced,\n                expired,\n                errors\n            };\n        } catch (error) {\n            console.error('Error in daily copy-paste reduction processing:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get copy-paste status for a user (read-only)\n   */ static async getCopyPasteStatus(userId) {\n        try {\n            const permission = await CopyPasteService.checkCopyPastePermission(userId);\n            return {\n                hasPermission: permission.hasPermission,\n                daysRemaining: permission.daysRemaining,\n                expiryDate: permission.expiryDate ? permission.expiryDate.toDateString() : null\n            };\n        } catch (error) {\n            console.error(`Error getting copy-paste status for user ${userId}:`, error);\n            return {\n                hasPermission: false,\n                daysRemaining: 0,\n                expiryDate: null\n            };\n        }\n    }\n}\n// Export convenience functions\nconst checkCopyPastePermission = CopyPasteService.checkCopyPastePermission;\nconst grantCopyPastePermission = CopyPasteService.grantCopyPastePermission;\nconst removeCopyPastePermission = CopyPasteService.removeCopyPastePermission;\nconst reduceCopyPasteDays = CopyPasteService.reduceCopyPasteDays;\nconst processAllUsersCopyPasteReduction = CopyPasteService.processAllUsersCopyPasteReduction;\nconst getCopyPasteStatus = CopyPasteService.getCopyPasteStatus;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/copyPasteService.ts\n");

/***/ })

};
;