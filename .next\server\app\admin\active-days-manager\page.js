(()=>{var e={};e.id=8297,e.ids=[7878,8297],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1775:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\admin\\\\active-days-manager\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\active-days-manager\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14488:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>l});var r=t(65239),i=t(48088),a=t(88170),n=t.n(a),o=t(30893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);t.d(s,c);let l={children:["",{children:["admin",{children:["active-days-manager",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1775)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\active-days-manager\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\active-days-manager\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/active-days-manager/page",pathname:"/admin/active-days-manager",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27878:(e,s,t)=>{"use strict";t.d(s,{Mk:()=>m,checkCopyPastePermission:()=>c,grantCopyPastePermission:()=>l,i7:()=>u,removeCopyPastePermission:()=>d});var r=t(33784),i=t(75535);let a={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},n={users:"users"};class o{static async checkCopyPastePermission(e){try{let s=await (0,i.x7)((0,i.H9)(r.db,n.users,e));if(!s.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let t=s.data()[a.quickTranslationAdvantageExpiry];if(!t)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let o=t.toDate(),c=new Date,l=o>c,d=l?Math.ceil((o.getTime()-c.getTime())/864e5):0;return{hasPermission:l,daysRemaining:d,expiryDate:o}}catch(s){return console.error(`Error checking copy-paste permission for user ${e}:`,s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,s){try{let t=new Date;t.setDate(t.getDate()+s);let o=(0,i.H9)(r.db,n.users,e);await (0,i.mZ)(o,{[a.quickTranslationAdvantageExpiry]:i.Dc.fromDate(t),[a.lastCopyPasteReduction]:i.Dc.now()}),console.log(`✅ Granted copy-paste permission to user ${e} for ${s} days (expires: ${t.toDateString()})`)}catch(s){throw console.error(`Error granting copy-paste permission to user ${e}:`,s),s}}static async removeCopyPastePermission(e){try{let s=(0,i.H9)(r.db,n.users,e);await (0,i.mZ)(s,{[a.quickTranslationAdvantageExpiry]:null}),console.log(`✅ Removed copy-paste permission from user ${e}`)}catch(s){throw console.error(`Error removing copy-paste permission from user ${e}:`,s),s}}static async reduceCopyPasteDays(e){try{let s=await (0,i.x7)((0,i.H9)(r.db,n.users,e));if(!s.exists())return{reduced:!1,daysRemaining:0,expired:!1};let t=s.data(),o=t[a.quickTranslationAdvantageExpiry],c=t[a.lastCopyPasteReduction];if(!o)return{reduced:!1,daysRemaining:0,expired:!1};let l=new Date().toDateString();if((c?c.toDate().toDateString():null)===l){let e=o.toDate(),s=new Date,t=Math.max(0,Math.ceil((e.getTime()-s.getTime())/864e5));return{reduced:!1,daysRemaining:t,expired:0===t}}let d=o.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let m=(0,i.H9)(r.db,n.users,e);if(u<=new Date)return await (0,i.mZ)(m,{[a.quickTranslationAdvantageExpiry]:null,[a.lastCopyPasteReduction]:i.Dc.now()}),console.log(`📅 Copy-paste permission expired for user ${e}`),{reduced:!0,daysRemaining:0,expired:!0};{await (0,i.mZ)(m,{[a.quickTranslationAdvantageExpiry]:i.Dc.fromDate(u),[a.lastCopyPasteReduction]:i.Dc.now()});let s=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log(`📅 Reduced copy-paste days for user ${e}: ${s} days remaining`),{reduced:!0,daysRemaining:s,expired:!1}}}catch(s){return console.error(`Error reducing copy-paste days for user ${e}:`,s),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,i.getDocs)((0,i.collection)(r.db,n.users)),s=0,t=0,a=0,c=0;for(let r of e.docs)try{s++;let e=await o.reduceCopyPasteDays(r.id);e.reduced&&(t++,e.expired&&a++)}catch(e){c++,console.error(`Error processing copy-paste reduction for user ${r.id}:`,e)}return console.log(`✅ Daily copy-paste reduction complete:`),console.log(`   - Processed: ${s} users`),console.log(`   - Reduced: ${t} users`),console.log(`   - Expired: ${a} users`),console.log(`   - Errors: ${c} users`),{processed:s,reduced:t,expired:a,errors:c}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let s=await o.checkCopyPastePermission(e);return{hasPermission:s.hasPermission,daysRemaining:s.daysRemaining,expiryDate:s.expiryDate?s.expiryDate.toDateString():null}}catch(s){return console.error(`Error getting copy-paste status for user ${e}:`,s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let c=o.checkCopyPastePermission,l=o.grantCopyPastePermission,d=o.removeCopyPastePermission,u=o.reduceCopyPasteDays,m=o.processAllUsersCopyPasteReduction;o.getCopyPasteStatus},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},42069:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var r=t(60687),i=t(43210),a=t(85814),n=t.n(a),o=t(87979),c=t(95871),l=t(27878),d=t(77567);function u(){let{user:e,loading:s,isAdmin:t}=(0,o.wC)(),[a,u]=(0,i.useState)(!1),[m,p]=(0,i.useState)(null),h=async()=>{if(e&&(await d.A.fire({icon:"warning",title:"Manual Active Days Increment",text:"This will increment active days for all eligible users. Are you sure?",showCancelButton:!0,confirmButtonText:"Yes, Proceed",cancelButtonText:"Cancel"})).isConfirmed)try{u(!0),console.log(`🔧 Manual active days increment triggered by admin: ${e.uid}`),console.log("\uD83D\uDCC5 Processing active days increment via Firebase Function...");let s=await (0,c.e5)();console.log("\uD83D\uDCCB Processing copy-paste reduction...");let t=await (0,l.Mk)(),r={success:!0,totalUsers:s.processed,updatedUsers:s.updated,skippedUsers:0,errors:s.errors,activeDaysResult:s,copyPasteResult:t};p(r),d.A.fire({icon:"success",title:"Process Completed",html:`
          <div class="text-left">
            <p><strong>Active Days Results:</strong></p>
            <ul>
              <li>Processed: ${s.processed} users</li>
              <li>Updated: ${s.updated} users</li>
              <li>Errors: ${s.errors} users</li>
            </ul>
            <br>
            <p><strong>Copy-Paste Results:</strong></p>
            <ul>
              <li>Processed: ${t.processed} users</li>
              <li>Reduced: ${t.reduced} users</li>
              <li>Expired: ${t.expired} users</li>
              <li>Errors: ${t.errors} users</li>
            </ul>
          </div>
        `,confirmButtonText:"OK"})}catch(e){console.error("Error triggering manual increment:",e),d.A.fire({icon:"error",title:"Process Failed",text:"An error occurred while processing active days increment."})}finally{u(!1)}};if(s)return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:"Loading active days manager..."})]})});let x=(()=>{let e=new Date(new Date);return e.setDate(e.getDate()+1),e.setHours(0,0,0,0),e})();return(0,r.jsxs)("div",{className:"min-h-screen p-4",children:[(0,r.jsx)("div",{className:"glass-card p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold text-white mb-2",children:[(0,r.jsx)("i",{className:"fas fa-calendar-plus mr-3"}),"Active Days Manager"]}),(0,r.jsx)("p",{className:"text-white/80",children:"Manage daily active days increment system"})]}),(0,r.jsxs)(n(),{href:"/admin",className:"btn-secondary",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]})]})}),(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,r.jsx)("i",{className:"fas fa-info-circle mr-2"}),"System Status"]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-clock mr-2"}),"Next Scheduled Run"]}),(0,r.jsx)("p",{className:"text-white/80 text-lg",children:x.toLocaleString()}),(0,r.jsx)("p",{className:"text-white/60 text-sm mt-1",children:"Runs automatically at midnight (12:00 AM) daily"})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-cogs mr-2"}),"System Rules"]}),(0,r.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• Increments active days for all active users"}),(0,r.jsx)("li",{children:"• Skips admin users automatically"}),(0,r.jsx)("li",{children:"• Skips users on leave"}),(0,r.jsx)("li",{children:"• Runs once per day at midnight"})]})]})]})]}),(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,r.jsx)("i",{className:"fas fa-hand-pointer mr-2"}),"Manual Controls"]}),(0,r.jsx)("div",{className:"bg-yellow-500/20 rounded-lg p-4 border border-yellow-500/30 mb-4",children:(0,r.jsxs)("div",{className:"flex items-start text-yellow-300",children:[(0,r.jsx)("i",{className:"fas fa-exclamation-triangle mr-2 mt-1"}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("p",{className:"font-semibold mb-1",children:"Warning:"}),(0,r.jsx)("p",{children:"Manual trigger will increment active days for all eligible users immediately. This should only be used for testing or emergency situations."})]})]})}),(0,r.jsx)("button",{onClick:h,disabled:a,className:"btn-primary",children:a?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Processing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-play mr-2"}),"Manual Trigger Active Days Increment"]})})]}),m&&(0,r.jsxs)("div",{className:"glass-card p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,r.jsx)("i",{className:"fas fa-chart-bar mr-2"}),"Last Execution Result"]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"bg-blue-500/20 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-400",children:m.totalUsers}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Total Users"})]}),(0,r.jsxs)("div",{className:"bg-green-500/20 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-400",children:m.updatedUsers}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Successfully Updated"})]}),(0,r.jsxs)("div",{className:"bg-yellow-500/20 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:m.skippedUsers}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Skipped Users"})]}),(0,r.jsxs)("div",{className:"bg-red-500/20 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-400",children:m.errors}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Errors"})]})]}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${m.success?"bg-green-500/20 text-green-400":"bg-red-500/20 text-red-400"}`,children:m.success?"Success":"Failed"})})]}),(0,r.jsxs)("div",{className:"glass-card p-6 mt-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,r.jsx)("i",{className:"fas fa-question-circle mr-2"}),"How It Works"]}),(0,r.jsxs)("div",{className:"space-y-4 text-white/80",children:[(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1",children:"1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-white",children:"Automatic Scheduling"}),(0,r.jsx)("p",{className:"text-sm",children:"System runs automatically every day at midnight (12:00 AM)"})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1",children:"2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-white",children:"User Filtering"}),(0,r.jsx)("p",{className:"text-sm",children:"Identifies eligible users (active, non-admin, not on leave)"})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1",children:"3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-white",children:"Increment Process"}),(0,r.jsx)("p",{className:"text-sm",children:"Increases active days by 1 for each eligible user"})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1",children:"4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-white",children:"Logging"}),(0,r.jsx)("p",{className:"text-sm",children:"Records all actions and results for audit purposes"})]})]})]})]})]})}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},53719:(e,s,t)=>{Promise.resolve().then(t.bind(t,42069))},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},59295:(e,s,t)=>{Promise.resolve().then(t.bind(t,1775))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95871:(e,s,t)=>{"use strict";t.d(s,{Ou:()=>p,Ov:()=>w,PY:()=>j,ck:()=>f,e5:()=>v,iM:()=>x,lA:()=>y,rB:()=>h,tv:()=>b,wh:()=>g});var r=t(24791),i=t(33784);async function a(){let{auth:e}=await Promise.resolve().then(t.bind(t,33784)),s=e.currentUser;if(!s)throw Error("User not authenticated");try{await s.getIdToken(!0),console.log("✅ User token refreshed and validated")}catch(e){throw console.error("❌ Error refreshing user token:",e),Error("Authentication token expired. Please refresh the page.")}}let n=(0,r.Qg)(i.Cn,"getUserWorkData"),o=(0,r.Qg)(i.Cn,"submitTranslationBatch"),c=(0,r.Qg)(i.Cn,"getUserDashboardData"),l=((0,r.Qg)(i.Cn,"getAdminDashboardData"),(0,r.Qg)(i.Cn,"getUserTransactions")),d=(0,r.Qg)(i.Cn,"processWithdrawalRequest"),u=(0,r.Qg)(i.Cn,"processDailyActiveDays"),m=((0,r.Qg)(i.Cn,"processDailyCopyPasteReduction"),(0,r.Qg)(i.Cn,"grantCopyPastePermission"),(0,r.Qg)(i.Cn,"updateUserPlan"),(0,r.Qg)(i.Cn,"getPlatformStats")),p={functionsUsed:0,firestoreReadsAvoided:0,firestoreWritesOptimized:0,incrementFunctionUsage(){this.functionsUsed++,console.log(`🚀 Firebase Functions used: ${this.functionsUsed}`)},addReadsAvoided(e){this.firestoreReadsAvoided+=e,console.log(`💰 Firestore reads avoided: ${e} (Total: ${this.firestoreReadsAvoided})`)},addWritesOptimized(e){this.firestoreWritesOptimized+=e,console.log(`⚡ Firestore writes optimized: ${e} (Total: ${this.firestoreWritesOptimized})`)},getStats(){return{functionsUsed:this.functionsUsed,firestoreReadsAvoided:this.firestoreReadsAvoided,firestoreWritesOptimized:this.firestoreWritesOptimized,estimatedCostSavings:(36e-5*this.firestoreReadsAvoided+.00108*this.firestoreWritesOptimized).toFixed(4)}}};async function h(){try{console.log("\uD83D\uDE80 Fetching user work data via Firebase Function..."),await a();let e=(await n()).data;return p.incrementFunctionUsage(),p.addReadsAvoided(4),console.log("✅ User work data fetched successfully:",e),e}catch(e){if(console.error("❌ Error fetching user work data:",e),"unauthenticated"===e.code||e.message?.includes("User must be authenticated"))throw Error("Authentication required. Please refresh the page and try again.");throw Error("Failed to fetch user work data")}}async function x(e=50){try{console.log(`🚀 Submitting translation batch via Firebase Function: ${e} translations`),await a();let s=(await o({batchSize:e})).data;return p.incrementFunctionUsage(),p.addWritesOptimized(3),console.log("✅ Translation batch submitted successfully:",s),s}catch(e){if(console.error("❌ Error submitting translation batch:",e),"unauthenticated"===e.code||e.message?.includes("User must be authenticated"))throw Error("Authentication required. Please refresh the page and try again.");if("permission-denied"===e.code)throw Error("Permission denied. Please check your account status.");throw Error("Failed to submit translation batch")}}async function g(){try{console.log("\uD83D\uDE80 Fetching user dashboard data via Firebase Function..."),await a();let e=(await c()).data;return p.incrementFunctionUsage(),p.addReadsAvoided(3),console.log("✅ User dashboard data fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching user dashboard data:",e),Error("Failed to fetch user dashboard data")}}async function y(e=20,s){try{console.log(`🚀 Fetching user transactions via Firebase Function: limit=${e}`);let t=(await l({limit:e,startAfter:s})).data;return p.incrementFunctionUsage(),p.addReadsAvoided(Math.max(0,100-e)),console.log("✅ User transactions fetched successfully:",t),t}catch(e){throw console.error("❌ Error fetching user transactions:",e),Error("Failed to fetch user transactions")}}async function f(e,s){try{console.log(`🚀 Processing withdrawal request via Firebase Function: ₹${e}`);let t=(await d({amount:e,upiId:s})).data;return p.incrementFunctionUsage(),p.addWritesOptimized(2),console.log("✅ Withdrawal request processed successfully:",t),t}catch(e){throw console.error("❌ Error processing withdrawal request:",e),Error("Failed to process withdrawal request")}}async function v(){try{console.log("\uD83D\uDE80 Processing daily active days via Firebase Function...");let e=(await u()).data;return p.incrementFunctionUsage(),p.addWritesOptimized(e.updated||0),console.log("✅ Daily active days processed successfully:",e),e}catch(e){throw console.error("❌ Error processing daily active days:",e),Error("Failed to process daily active days")}}async function b(){try{console.log("\uD83D\uDE80 Fetching platform stats via Firebase Function...");let e=(await m()).data;return p.incrementFunctionUsage(),p.addReadsAvoided(10),console.log("✅ Platform stats fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching platform stats:",e),Error("Failed to fetch platform stats")}}async function w(){try{console.log("\uD83D\uDE80 Getting optimized user work data..."),await a();let e=await n();return console.log("✅ Optimized user work data retrieved"),e.data}catch(e){throw console.error("❌ Error getting optimized user work data:",e),e}}async function j(e=50){try{console.log(`🚀 Submitting optimized translation batch: ${e} translations`);let s=await o({batchSize:e});return console.log("✅ Optimized translation batch submitted"),s.data}catch(e){throw console.error("❌ Error submitting optimized translation batch:",e),e}}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4573,6803],()=>t(14488));module.exports=r})();