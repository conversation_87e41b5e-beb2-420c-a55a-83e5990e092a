"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3592],{3592:(t,e,a)=>{a.d(e,{$T:()=>y,AX:()=>C,COLLECTIONS:()=>i,HY:()=>w,I0:()=>g,II:()=>v,IK:()=>A,QD:()=>R,Ss:()=>N,Yr:()=>o,_I:()=>k,_f:()=>I,addTransaction:()=>u,b6:()=>d,bA:()=>q,fP:()=>U,getPlanValidityDays:()=>b,getUserData:()=>s,getUserVideoSettings:()=>E,getVideoCountData:()=>l,getWalletData:()=>c,gj:()=>p,i8:()=>F,iA:()=>_,mm:()=>h,mv:()=>x,pl:()=>f,ul:()=>P,updateWalletBalance:()=>m,w1:()=>T,wT:()=>S,x4:()=>G,xj:()=>H,z8:()=>B,zb:()=>D});var r=a(5317),n=a(6104);let o={name:"name",email:"email",mobile:"mobile",referralCode:"referralCode",referredBy:"referredBy",referralBonusCredited:"referralBonusCredited",plan:"plan",planExpiry:"planExpiry",activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate",joinedDate:"joinedDate",wallet:"wallet",bankAccountHolderName:"bankAccountHolderName",bankAccountNumber:"bankAccountNumber",bankIfscCode:"bankIfscCode",bankName:"bankName",bankDetailsUpdated:"bankDetailsUpdated",totalTranslations:"totalTranslations",todayTranslations:"todayTranslations",lastTranslationDate:"lastTranslationDate",translationDuration:"translationDuration",quickTranslationAdvantage:"quickTranslationAdvantage",quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",quickTranslationAdvantageDays:"quickTranslationAdvantageDays",quickTranslationAdvantageSeconds:"quickTranslationAdvantageSeconds",quickTranslationAdvantageGrantedBy:"quickTranslationAdvantageGrantedBy",quickTranslationAdvantageGrantedAt:"quickTranslationAdvantageGrantedAt",type:"type",amount:"amount",date:"date",status:"status",description:"description",userId:"userId"},i={users:"users",transactions:"transactions",withdrawals:"withdrawals",plans:"plans",settings:"settings",notifications:"notifications",adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function s(t){try{if(!t||"string"!=typeof t)return console.error("Invalid userId provided to getUserData:",t),null;let d=await (0,r.x7)((0,r.H9)(n.db,i.users,t));if(d.exists()){var e,a,s,c,l;let t=d.data(),r={name:String(t[o.name]||""),email:String(t[o.email]||""),mobile:String(t[o.mobile]||""),referralCode:String(t[o.referralCode]||""),referredBy:String(t[o.referredBy]||""),referralBonusCredited:!!t[o.referralBonusCredited],plan:String(t[o.plan]||"Trial"),planExpiry:(null==(e=t[o.planExpiry])?void 0:e.toDate())||null,activeDays:Number(t[o.activeDays]||0),lastActiveDayUpdate:(null==(a=t[o.lastActiveDayUpdate])?void 0:a.toDate())||null,joinedDate:(null==(s=t[o.joinedDate])?void 0:s.toDate())||new Date,translationDuration:Number(t[o.translationDuration]||("Trial"===t[o.plan]?30:300)),quickTranslationAdvantage:!!t[o.quickTranslationAdvantage],quickTranslationAdvantageExpiry:(null==(c=t[o.quickTranslationAdvantageExpiry])?void 0:c.toDate())||null,quickTranslationAdvantageDays:Number(t[o.quickTranslationAdvantageDays]||0),quickTranslationAdvantageSeconds:Number(t[o.quickTranslationAdvantageSeconds]||30),quickTranslationAdvantageGrantedBy:String(t[o.quickTranslationAdvantageGrantedBy]||""),quickTranslationAdvantageGrantedAt:(null==(l=t[o.quickTranslationAdvantageGrantedAt])?void 0:l.toDate())||null};return console.log("getUserData result:",r),r}return null}catch(t){return console.error("Error getting user data:",t),null}}async function c(t){try{if(!t||"string"!=typeof t)return console.error("Invalid userId provided to getWalletData:",t),{wallet:0};let e=await (0,r.x7)((0,r.H9)(n.db,i.users,t));if(e.exists()){let t=e.data(),a={wallet:Number(t[o.wallet]||0)};return console.log("getWalletData result:",a),a}return{wallet:0}}catch(t){return console.error("Error getting wallet data:",t),{wallet:0}}}async function l(t){try{let a=(0,r.H9)(n.db,i.users,t),s=await (0,r.x7)(a);if(s.exists()){var e;let n=s.data(),i=n[o.totalTranslations]||0,c=n[o.todayTranslations]||0,l=null==(e=n[o.lastTranslationDate])?void 0:e.toDate(),d=new Date;return(!l||l.toDateString()!==d.toDateString())&&c>0&&(console.log("\uD83D\uDD04 Resetting daily translation count for user ".concat(t," (was ").concat(c,")")),await (0,r.mZ)(a,{[o.todayTranslations]:0}),c=0,console.log("\uD83D\uDCC5 Active days will be updated by centralized scheduler")),{totalTranslations:i,todayTranslations:c,remainingTranslations:Math.max(0,50-c)}}return{totalTranslations:0,todayTranslations:0,remainingTranslations:50}}catch(t){throw console.error("Error getting video count data:",t),t}}async function d(t,e){try{await (0,r.mZ)((0,r.H9)(n.db,i.users,t),e)}catch(t){throw console.error("Error updating user data:",t),t}}async function u(t,e){try{let a={[o.userId]:t,[o.type]:e.type,[o.amount]:e.amount,[o.description]:e.description,[o.status]:e.status||"completed",[o.date]:r.Dc.now()};await (0,r.gS)((0,r.collection)(n.db,i.transactions),a)}catch(t){throw console.error("Error adding transaction:",t),t}}async function g(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{if(!t||"string"!=typeof t)return console.error("Invalid userId provided to getTransactions:",t),[];let a=(0,r.P)((0,r.collection)(n.db,i.transactions),(0,r._M)(o.userId,"==",t),(0,r.AB)(e)),s=(await (0,r.getDocs)(a)).docs.map(t=>{var e;return{id:t.id,...t.data(),date:null==(e=t.data()[o.date])?void 0:e.toDate()}});return s.sort((t,e)=>{let a=t.date||new Date(0);return(e.date||new Date(0)).getTime()-a.getTime()}),s}catch(t){return console.error("Error getting transactions:",t),[]}}async function f(t){try{let e=(0,r.P)((0,r.collection)(n.db,i.users),(0,r._M)(o.referredBy,"==",t));return(await (0,r.getDocs)(e)).docs.map(t=>{var e;return{id:t.id,...t.data(),joinedDate:null==(e=t.data()[o.joinedDate])?void 0:e.toDate()}})}catch(t){throw console.error("Error getting referrals:",t),t}}async function y(t){try{if(!t)return 0;let e=(0,r.P)((0,r.collection)(n.db,i.users),(0,r._M)(o.referredBy,"==",t));return(await (0,r.getDocs)(e)).size}catch(t){return console.error("Error getting referral count:",t),0}}async function w(t){try{let e=(0,r.H9)(n.db,i.users,t);await (0,r.mZ)(e,{[o.todayTranslations]:0}),console.log("✅ Reset daily translation count for user ".concat(t))}catch(t){throw console.error("Error resetting daily translation count:",t),t}}async function p(){try{console.log("\uD83D\uDD27 Starting to fix all users active days...");let{ActiveDaysService:t}=await a.e(2439).then(a.bind(a,2439)),e=await (0,r.getDocs)((0,r.collection)(n.db,i.users)),o=0,s=0;for(let a of e.docs)try{await t.updateUserActiveDays(a.id),o++}catch(t){console.error("Error fixing active days for user ".concat(a.id,":"),t),s++}return console.log("✅ Fixed active days for ".concat(o," users, ").concat(s," errors")),{fixedCount:o,errorCount:s}}catch(t){throw console.error("Error fixing all users active days:",t),t}}async function m(t,e){try{let a=(0,r.H9)(n.db,i.users,t);await (0,r.mZ)(a,{[o.wallet]:(0,r.GV)(e)})}catch(t){throw console.error("Error updating wallet balance:",t),t}}async function h(t,e){try{if(!t||"string"!=typeof t)throw Error("Invalid userId provided");var a=e;let{accountHolderName:s,accountNumber:c,ifscCode:l,bankName:d}=a;if(!s||s.trim().length<2)throw Error("Account holder name must be at least 2 characters long");if(!c||!/^\d{9,18}$/.test(c.trim()))throw Error("Account number must be 9-18 digits");if(!l||!/^[A-Z]{4}0[A-Z0-9]{6}$/.test(l.trim().toUpperCase()))throw Error("Invalid IFSC code format (e.g., SBIN0001234)");if(!d||d.trim().length<2)throw Error("Bank name must be at least 2 characters long");let u=(0,r.H9)(n.db,i.users,t);await (0,r.mZ)(u,{[o.bankAccountHolderName]:e.accountHolderName.trim(),[o.bankAccountNumber]:e.accountNumber.trim(),[o.bankIfscCode]:e.ifscCode.trim().toUpperCase(),[o.bankName]:e.bankName.trim(),[o.bankDetailsUpdated]:r.Dc.now()}),console.log("Bank details saved successfully for user:",t)}catch(t){throw console.error("Error saving bank details:",t),t}}async function D(t){try{if(!t||"string"!=typeof t)return console.error("Invalid userId provided to getBankDetails:",t),null;let e=await (0,r.x7)((0,r.H9)(n.db,i.users,t));if(e.exists()){let t=e.data();if(t[o.bankAccountNumber]){let e={accountHolderName:String(t[o.bankAccountHolderName]||""),accountNumber:String(t[o.bankAccountNumber]||""),ifscCode:String(t[o.bankIfscCode]||""),bankName:String(t[o.bankName]||"")};return console.log("getBankDetails result found"),e}}return console.log("No bank details found for user"),null}catch(t){return console.error("Error getting bank details:",t),null}}function b(t){return({Trial:2,Junior:30,Senior:30,Expert:30,Starter:30,Basic:30,Premium:30,Gold:30,Platinum:30,Diamond:30,499:30,1499:30,2999:30,3999:30,5999:30,9999:30})[t]||2}async function v(t,e,a){try{let s=(0,r.H9)(n.db,i.users,t);if("Trial"===e)await (0,r.mZ)(s,{[o.planExpiry]:null});else{let n;if(a)n=a;else{let t=b(e),a=new Date;n=new Date(a.getTime()+24*t*36e5)}await (0,r.mZ)(s,{[o.planExpiry]:r.Dc.fromDate(n)}),console.log("Updated plan expiry for user ".concat(t," to ").concat(n.toDateString()))}}catch(t){throw console.error("Error updating plan expiry:",t),t}}async function A(t,e,a){try{if(console.log("\uD83C\uDFAF REFERRAL BONUS DEBUG: Starting process for user ".concat(t)),console.log("\uD83C\uDFAF Plan change: ".concat(e," → ").concat(a)),"Trial"!==e||"Trial"===a){console.log("❌ Referral bonus only applies when upgrading from Trial to paid plan"),console.log("❌ Current conditions: oldPlan=".concat(e,", newPlan=").concat(a));return}console.log("✅ Processing referral bonus for user ".concat(t," upgrading from ").concat(e," to ").concat(a));let s=await (0,r.x7)((0,r.H9)(n.db,i.users,t));if(!s.exists())return void console.log("❌ User not found in database");let c=s.data(),l=c[o.referredBy],d=c[o.referralBonusCredited];if(console.log("\uD83D\uDD0D User referral data:"),console.log("   - Referred by: ".concat(l||"None")),console.log("   - Already credited: ".concat(d)),console.log("   - User name: ".concat(c[o.name])),!l)return void console.log("❌ User was not referred by anyone, skipping bonus processing");if(d)return void console.log("❌ Referral bonus already credited for this user, skipping");console.log("\uD83D\uDD0D Finding referrer with code: ".concat(l));let g=(0,r.P)((0,r.collection)(n.db,i.users),(0,r._M)(o.referralCode,"==",l),(0,r.AB)(1)),f=await (0,r.getDocs)(g);if(f.empty){console.log("❌ Referral code not found: ".concat(l)),console.log("❌ No referrer found with this code in database");return}let y=f.docs[0],w=y.id,p=y.data(),h={Trial:0,Junior:300,Senior:700,Expert:1200,499:50,1499:150,2999:300,3999:400,5999:700,9999:1200,Starter:50,Basic:150,Premium:300,Gold:400,Platinum:700,Diamond:1200}[a]||0;if(console.log("✅ Found referrer:"),console.log("   - Referrer ID: ".concat(w)),console.log("   - Referrer name: ".concat(p[o.name])),console.log("   - Bonus amount: ₹".concat(h)),console.log("   - Plan: ".concat(a)),h>0){console.log("\uD83D\uDCB0 Processing bonus payment..."),await m(w,h),console.log("✅ Added ₹".concat(h," to referrer's wallet"));let e=(0,r.H9)(n.db,i.users,w);await (0,r.mZ)(e,{[o.totalTranslations]:(0,r.GV)(50)}),console.log("✅ Added 50 bonus translations to referrer");let s=(0,r.H9)(n.db,i.users,t);await (0,r.mZ)(s,{[o.referralBonusCredited]:!0}),console.log("✅ Marked referral bonus as credited for user ".concat(t)),await u(w,{type:"referral_bonus",amount:h,description:"Referral bonus for ".concat(a," plan upgrade + 50 bonus translations (User: ").concat(c[o.name],")")}),console.log("✅ Added transaction record for referral bonus"),console.log("\uD83C\uDF89 REFERRAL BONUS COMPLETED: ₹".concat(h," + 50 translations for referrer ").concat(w))}else console.log("❌ No bonus amount calculated for plan: ".concat(a)),console.log("❌ Available plans:",["Trial","Junior","Senior","Expert"])}catch(t){console.error("❌ Error processing referral bonus:",t),console.error("❌ Error details:",t)}}async function k(t){try{console.log("\uD83D\uDD27 MANUAL REFERRAL BONUS: Processing for user ".concat(t));let e=await s(t);if(!e)return console.log("❌ User not found"),{success:!1,message:"User not found"};if(console.log("\uD83D\uDD0D User data:"),console.log("   - Name: ".concat(e.name)),console.log("   - Plan: ".concat(e.plan)),console.log("   - Referred by: ".concat(e.referredBy||"None")),console.log("   - Bonus credited: ".concat(e.referralBonusCredited)),"Trial"===e.plan)return{success:!1,message:"User is still on Trial plan. Upgrade to paid plan first."};if(!e.referredBy)return{success:!1,message:"User was not referred by anyone"};if(e.referralBonusCredited)return{success:!1,message:"Referral bonus already credited"};return await A(t,"Trial",e.plan),{success:!0,message:"Referral bonus processed for ".concat(e.plan," plan")}}catch(t){return console.error("❌ Error in manual referral bonus processing:",t),{success:!1,message:"Error: ".concat((null==t?void 0:t.message)||"Unknown error")}}}async function E(t){try{var e;let a=await s(t);if(!a)return{translationDuration:30,earningPerBatch:25,plan:"Trial",hasQuickAdvantage:!1};let r=!!(e=a).quickTranslationAdvantage&&!!e.quickTranslationAdvantageExpiry&&new Date<e.quickTranslationAdvantageExpiry,n=a.translationDuration;return r?n=a.quickTranslationAdvantageSeconds||30:n&&"Trial"!==a.plan||(n=({Trial:30,Starter:300,Basic:300,Premium:300,Gold:180,Platinum:120,Diamond:60})[a.plan]||30),{translationDuration:n,earningPerBatch:({Trial:25,Junior:150,Senior:250,Expert:400,Starter:25,Basic:75,Premium:150,Gold:200,Platinum:250,Diamond:400})[a.plan]||25,plan:a.plan,hasQuickAdvantage:r,quickAdvantageExpiry:a.quickTranslationAdvantageExpiry}}catch(t){return console.error("Error getting user translation settings:",t),{translationDuration:30,earningPerBatch:25,plan:"Trial",hasQuickAdvantage:!1}}}async function T(t,e,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:30;try{if(e<=0||e>365)throw Error("Days must be between 1 and 365");if(n<1||n>420)throw Error("Seconds must be between 1 and 420 (7 minutes)");let{grantCopyPastePermission:o}=await a.e(7718).then(a.bind(a,7718));return await o(t,e),console.log("Granted copy-paste permission to user ".concat(t," for ").concat(e," days by ").concat(r)),await u(t,{type:"quick_advantage_granted",amount:0,description:"Copy-paste permission granted for ".concat(e," days by ").concat(r)}),{success:!0}}catch(t){throw console.error("Error granting quick video advantage:",t),t}}async function S(t,e){try{let{removeCopyPastePermission:r}=await a.e(7718).then(a.bind(a,7718));return await r(t),console.log("Removed copy-paste permission from user ".concat(t," by ").concat(e)),await u(t,{type:"quick_advantage_removed",amount:0,description:"Copy-paste permission removed by ".concat(e)}),{success:!0}}catch(t){throw console.error("Error removing quick video advantage:",t),t}}async function B(t){try{let e={title:t.title,message:t.message,type:t.type,targetUsers:t.targetUsers,userIds:t.userIds||[],createdAt:r.Dc.now(),createdBy:t.createdBy};console.log("Adding notification to Firestore:",e);let a=await (0,r.gS)((0,r.collection)(n.db,i.notifications),e);console.log("Notification added successfully with ID:",a.id);let o=await (0,r.x7)(a);return o.exists()?console.log("Notification verified in database:",o.data()):console.warn("Notification not found after adding"),a.id}catch(t){throw console.error("Error adding notification:",t),t}}async function N(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20;try{let a,o;if(!t||"string"!=typeof t)return console.error("Invalid userId provided to getUserNotifications:",t),[];console.log("Loading notifications for user: ".concat(t));try{let t=(0,r.P)((0,r.collection)(n.db,i.notifications),(0,r._M)("targetUsers","==","all"),(0,r.My)("createdAt","desc"),(0,r.AB)(e));a=await (0,r.getDocs)(t),console.log("Found ".concat(a.docs.length," notifications for all users"))}catch(o){console.warn("Error querying all users notifications, trying without orderBy:",o);let t=(0,r.P)((0,r.collection)(n.db,i.notifications),(0,r._M)("targetUsers","==","all"),(0,r.AB)(e));a=await (0,r.getDocs)(t)}try{let a=(0,r.P)((0,r.collection)(n.db,i.notifications),(0,r._M)("targetUsers","==","specific"),(0,r._M)("userIds","array-contains",t),(0,r.My)("createdAt","desc"),(0,r.AB)(e));o=await (0,r.getDocs)(a),console.log("Found ".concat(o.docs.length," notifications for specific user"))}catch(s){console.warn("Error querying specific user notifications, trying without orderBy:",s);let a=(0,r.P)((0,r.collection)(n.db,i.notifications),(0,r._M)("targetUsers","==","specific"),(0,r._M)("userIds","array-contains",t),(0,r.AB)(e));o=await (0,r.getDocs)(a)}let s=[];a.docs.forEach(t=>{var e;s.push({id:t.id,...t.data(),createdAt:(null==(e=t.data().createdAt)?void 0:e.toDate())||new Date})}),o.docs.forEach(t=>{var e;s.push({id:t.id,...t.data(),createdAt:(null==(e=t.data().createdAt)?void 0:e.toDate())||new Date})}),s.sort((t,e)=>e.createdAt.getTime()-t.createdAt.getTime());let c=s.slice(0,e);return console.log("Returning ".concat(c.length," total notifications for user")),c}catch(t){return console.error("Error getting user notifications:",t),[]}}async function I(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;try{let e=(0,r.P)((0,r.collection)(n.db,i.notifications),(0,r.My)("createdAt","desc"),(0,r.AB)(t));return(await (0,r.getDocs)(e)).docs.map(t=>{var e;return{id:t.id,...t.data(),createdAt:(null==(e=t.data().createdAt)?void 0:e.toDate())||new Date}})}catch(t){return console.error("Error getting all notifications:",t),[]}}async function U(t){try{if(!t||"string"!=typeof t)throw Error("Invalid notification ID provided");console.log("Deleting notification:",t),await (0,r.kd)((0,r.H9)(n.db,i.notifications,t)),console.log("Notification deleted successfully")}catch(t){throw console.error("Error deleting notification:",t),t}}async function q(t,e){try{let a=JSON.parse(localStorage.getItem("read_notifications_".concat(e))||"[]");a.includes(t)||(a.push(t),localStorage.setItem("read_notifications_".concat(e),JSON.stringify(a)))}catch(t){console.error("Error marking notification as read:",t)}}function x(t,e){try{return JSON.parse(localStorage.getItem("read_notifications_".concat(e))||"[]").includes(t)}catch(t){return console.error("Error checking notification read status:",t),!1}}function P(t,e){try{let a=JSON.parse(localStorage.getItem("read_notifications_".concat(e))||"[]");return t.filter(t=>!a.includes(t.id)).length}catch(t){return console.error("Error getting unread notification count:",t),0}}async function C(t){try{if(!t||"string"!=typeof t)return console.error("Invalid userId provided to getUnreadNotifications:",t),[];console.log("Loading unread notifications for user: ".concat(t));let e=await N(t,50),a=JSON.parse(localStorage.getItem("read_notifications_".concat(t))||"[]"),r=e.filter(t=>t.id&&!a.includes(t.id));return console.log("Found ".concat(r.length," unread notifications")),r}catch(t){return console.error("Error getting unread notifications:",t),[]}}async function _(t){try{return(await C(t)).length>0}catch(t){return console.error("Error checking for unread notifications:",t),!1}}async function M(t){try{let e=(0,r.P)((0,r.collection)(n.db,i.withdrawals),(0,r._M)("userId","==",t),(0,r._M)("status","==","pending"),(0,r.AB)(1));return!(await (0,r.getDocs)(e)).empty}catch(t){return console.error("Error checking pending withdrawals:",t),!1}}async function R(t){try{let e=await s(t);if(!e)return{allowed:!1,reason:"Unable to verify user information. Please try again."};if("Trial"===e.plan)return{allowed:!1,reason:"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals."};if(await M(t))return{allowed:!1,reason:"You have a pending withdrawal request. Please wait for it to be processed before submitting a new request."};let r=new Date,n=r.getHours();if(n<10||n>=18)return{allowed:!1,reason:"Withdrawals are only allowed between 10:00 AM to 6:00 PM"};let{isAdminLeaveDay:o}=await a.e(9567).then(a.bind(a,9567));if(await o(r))return{allowed:!1,reason:"Withdrawals are not allowed on admin leave/holiday days"};let{isUserOnLeave:i}=await a.e(9567).then(a.bind(a,9567));if(await i(t,r))return{allowed:!1,reason:"Withdrawals are not allowed on your leave days"};return{allowed:!0}}catch(t){return console.error("Error checking withdrawal allowed:",t),{allowed:!1,reason:"Unable to verify withdrawal eligibility. Please try again."}}}async function H(t,e,a){try{if(e<50)throw Error("Minimum withdrawal amount is ₹50");let o=await R(t);if(!o.allowed)throw Error(o.reason);if((await c(t)).wallet<e)throw Error("Insufficient wallet balance");await m(t,-e),await u(t,{type:"withdrawal_request",amount:-e,description:"Withdrawal request submitted - ₹".concat(e," debited from wallet")});let s={userId:t,amount:e,bankDetails:a,status:"pending",date:r.Dc.now(),createdAt:r.Dc.now()};return(await (0,r.gS)((0,r.collection)(n.db,i.withdrawals),s)).id}catch(t){throw console.error("Error creating withdrawal request:",t),t}}async function F(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20;try{let a=(0,r.P)((0,r.collection)(n.db,i.withdrawals),(0,r._M)("userId","==",t),(0,r.My)("date","desc"),(0,r.AB)(e));return(await (0,r.getDocs)(a)).docs.map(t=>{var e;return{id:t.id,...t.data(),date:null==(e=t.data().date)?void 0:e.toDate()}})}catch(t){return console.error("Error getting user withdrawals:",t),[]}}async function G(){try{try{let t=(0,r.collection)(n.db,i.users),e=((await (0,r.d_)(t)).data().count+1).toString().padStart(4,"0");return"TN".concat(e)}catch(a){console.warn("Failed to get count from server, using fallback method:",a);let t=Date.now().toString().slice(-4),e=Math.random().toString(36).substring(2,4).toUpperCase();return"TN".concat(t).concat(e)}}catch(e){console.error("Error generating unique referral code:",e);let t=Date.now().toString().slice(-4);return"TN".concat(t)}}}}]);