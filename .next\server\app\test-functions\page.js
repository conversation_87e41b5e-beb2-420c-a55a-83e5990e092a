(()=>{var e={};e.id=1879,e.ids=[1879],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8410:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var r=s(65239),i=s(48088),o=s(88170),a=s.n(o),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let c={children:["",{children:["test-functions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,78435)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-functions\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-functions\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-functions/page",pathname:"/test-functions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14769:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(60687),i=s(43210),o=s(87979),a=s(95871);function n(){let{user:e,loading:t}=(0,o.Nu)(),[s,n]=(0,i.useState)([]),[d,c]=(0,i.useState)(!1),l=(e,t,s,r)=>{n(i=>[...i,{functionName:e,success:t,data:s,error:r?.message||r?.code||r,timestamp:new Date().toISOString()}])},u=async(e,t)=>{try{c(!0),console.log(`🧪 Testing ${e}...`);let s=await t();l(e,!0,s),console.log(`✅ ${e} test passed:`,s)}catch(t){l(e,!1,null,t),console.error(`❌ ${e} test failed:`,t)}finally{c(!1)}},p=async()=>{n([]),await u("getUserWorkData",()=>(0,a.rB)()),await u("getUserDashboardData",()=>(0,a.wh)()),await u("getUserTransactions",()=>(0,a.lA)(5)),await u("getPlatformStats",()=>(0,a.tv)()),console.log("\uD83D\uDCB0 Final cost optimization stats:",a.Ou.getStats())},h=async()=>{await u("submitTranslationBatch",()=>(0,a.iM)(1))},g=async()=>{await u("processWithdrawalRequest",()=>(0,a.ck)(1,"test@paytm"))};return t?(0,r.jsx)("div",{className:"p-8",children:"Loading..."}):e?(0,r.jsx)("div",{className:"min-h-screen p-8 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-8",children:"\uD83E\uDDEA Firebase Functions Test Page"}),(0,r.jsxs)("div",{className:"bg-blue-600/20 border border-blue-500 rounded-lg p-4 mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-bold text-white mb-2",children:"\uD83D\uDCCB Test Information"}),(0,r.jsxs)("div",{className:"text-white/80 text-sm space-y-1",children:[(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Safe Tests:"})," Read-only functions that don't modify data"]}),(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Submit Batch:"})," May fail if daily limit (50) already reached"]}),(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Withdrawal:"})," May fail if insufficient wallet balance"]}),(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Errors are expected"})," for some functions based on user state"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:"Test Controls"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("button",{onClick:p,disabled:d,className:"w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:d?"Testing...":"Run All Safe Tests"}),(0,r.jsx)("button",{onClick:h,disabled:d,className:"w-full bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:"Test Submit Batch (⚠️ Will add 1 translation)"}),(0,r.jsx)("button",{onClick:g,disabled:d,className:"w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:"Test Withdrawal (⚠️ Will create ₹1 withdrawal)"}),(0,r.jsx)("button",{onClick:()=>n([]),className:"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg",children:"Clear Results"})]})]}),(0,r.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:"Cost Optimization Stats"}),(0,r.jsxs)("div",{className:"text-white space-y-2",children:[(0,r.jsxs)("p",{children:["Functions Used: ",a.Ou.functionsUsed]}),(0,r.jsxs)("p",{children:["Reads Avoided: ",a.Ou.firestoreReadsAvoided]}),(0,r.jsxs)("p",{children:["Writes Optimized: ",a.Ou.firestoreWritesOptimized]}),(0,r.jsxs)("p",{className:"text-green-400 font-bold",children:["Estimated Savings: $",a.Ou.getStats().estimatedCostSavings]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:"Test Results"}),(0,r.jsx)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:0===s.length?(0,r.jsx)("p",{className:"text-white/70",children:'No tests run yet. Click "Run All Safe Tests" to start.'}):s.map((e,t)=>(0,r.jsxs)("div",{className:`p-4 rounded-lg ${e.success?"bg-green-600/20 border border-green-500":"bg-red-600/20 border border-red-500"}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("h3",{className:"font-bold text-white",children:e.functionName}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-sm ${e.success?"bg-green-600 text-white":"bg-red-600 text-white"}`,children:e.success?"✅ Success":"❌ Failed"})]}),(0,r.jsxs)("div",{className:"text-sm text-white/80",children:[(0,r.jsxs)("p",{className:"mb-2",children:["Time: ",new Date(e.timestamp).toLocaleTimeString()]}),e.success?(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold mb-1",children:"Result:"}),(0,r.jsx)("pre",{className:"bg-black/20 p-2 rounded text-xs overflow-x-auto",children:JSON.stringify(e.data,null,2)})]}):(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold mb-1 text-red-300",children:"Error:"}),(0,r.jsx)("pre",{className:"bg-black/20 p-2 rounded text-xs overflow-x-auto text-red-200",children:e.error?.message||JSON.stringify(e.error,null,2)})]})]})]},t))})]})]})}):(0,r.jsx)("div",{className:"p-8",children:"Please log in to test Firebase Functions"})}},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22070:(e,t,s)=>{Promise.resolve().then(s.bind(s,78435))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68926:(e,t,s)=>{Promise.resolve().then(s.bind(s,14769))},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78435:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-functions\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-functions\\page.tsx","default")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95871:(e,t,s)=>{"use strict";s.d(t,{Ou:()=>h,Ov:()=>y,PY:()=>j,ck:()=>b,e5:()=>w,iM:()=>x,lA:()=>f,rB:()=>g,tv:()=>v,wh:()=>m});var r=s(24791),i=s(33784);async function o(){let{auth:e}=await Promise.resolve().then(s.bind(s,33784)),t=e.currentUser;if(!t)throw Error("User not authenticated");try{await t.getIdToken(!0),console.log("✅ User token refreshed and validated")}catch(e){throw console.error("❌ Error refreshing user token:",e),Error("Authentication token expired. Please refresh the page.")}}let a=(0,r.Qg)(i.Cn,"getUserWorkData"),n=(0,r.Qg)(i.Cn,"submitTranslationBatch"),d=(0,r.Qg)(i.Cn,"getUserDashboardData"),c=((0,r.Qg)(i.Cn,"getAdminDashboardData"),(0,r.Qg)(i.Cn,"getUserTransactions")),l=(0,r.Qg)(i.Cn,"processWithdrawalRequest"),u=(0,r.Qg)(i.Cn,"processDailyActiveDays"),p=((0,r.Qg)(i.Cn,"processDailyCopyPasteReduction"),(0,r.Qg)(i.Cn,"grantCopyPastePermission"),(0,r.Qg)(i.Cn,"updateUserPlan"),(0,r.Qg)(i.Cn,"getPlatformStats")),h={functionsUsed:0,firestoreReadsAvoided:0,firestoreWritesOptimized:0,incrementFunctionUsage(){this.functionsUsed++,console.log(`🚀 Firebase Functions used: ${this.functionsUsed}`)},addReadsAvoided(e){this.firestoreReadsAvoided+=e,console.log(`💰 Firestore reads avoided: ${e} (Total: ${this.firestoreReadsAvoided})`)},addWritesOptimized(e){this.firestoreWritesOptimized+=e,console.log(`⚡ Firestore writes optimized: ${e} (Total: ${this.firestoreWritesOptimized})`)},getStats(){return{functionsUsed:this.functionsUsed,firestoreReadsAvoided:this.firestoreReadsAvoided,firestoreWritesOptimized:this.firestoreWritesOptimized,estimatedCostSavings:(36e-5*this.firestoreReadsAvoided+.00108*this.firestoreWritesOptimized).toFixed(4)}}};async function g(){try{console.log("\uD83D\uDE80 Fetching user work data via Firebase Function..."),await o();let e=(await a()).data;return h.incrementFunctionUsage(),h.addReadsAvoided(4),console.log("✅ User work data fetched successfully:",e),e}catch(e){if(console.error("❌ Error fetching user work data:",e),"unauthenticated"===e.code||e.message?.includes("User must be authenticated"))throw Error("Authentication required. Please refresh the page and try again.");throw Error("Failed to fetch user work data")}}async function x(e=50){try{console.log(`🚀 Submitting translation batch via Firebase Function: ${e} translations`),await o();let t=(await n({batchSize:e})).data;return h.incrementFunctionUsage(),h.addWritesOptimized(3),console.log("✅ Translation batch submitted successfully:",t),t}catch(e){if(console.error("❌ Error submitting translation batch:",e),"unauthenticated"===e.code||e.message?.includes("User must be authenticated"))throw Error("Authentication required. Please refresh the page and try again.");if("permission-denied"===e.code)throw Error("Permission denied. Please check your account status.");throw Error("Failed to submit translation batch")}}async function m(){try{console.log("\uD83D\uDE80 Fetching user dashboard data via Firebase Function..."),await o();let e=(await d()).data;return h.incrementFunctionUsage(),h.addReadsAvoided(3),console.log("✅ User dashboard data fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching user dashboard data:",e),Error("Failed to fetch user dashboard data")}}async function f(e=20,t){try{console.log(`🚀 Fetching user transactions via Firebase Function: limit=${e}`);let s=(await c({limit:e,startAfter:t})).data;return h.incrementFunctionUsage(),h.addReadsAvoided(Math.max(0,100-e)),console.log("✅ User transactions fetched successfully:",s),s}catch(e){throw console.error("❌ Error fetching user transactions:",e),Error("Failed to fetch user transactions")}}async function b(e,t){try{console.log(`🚀 Processing withdrawal request via Firebase Function: ₹${e}`);let s=(await l({amount:e,upiId:t})).data;return h.incrementFunctionUsage(),h.addWritesOptimized(2),console.log("✅ Withdrawal request processed successfully:",s),s}catch(e){throw console.error("❌ Error processing withdrawal request:",e),Error("Failed to process withdrawal request")}}async function w(){try{console.log("\uD83D\uDE80 Processing daily active days via Firebase Function...");let e=(await u()).data;return h.incrementFunctionUsage(),h.addWritesOptimized(e.updated||0),console.log("✅ Daily active days processed successfully:",e),e}catch(e){throw console.error("❌ Error processing daily active days:",e),Error("Failed to process daily active days")}}async function v(){try{console.log("\uD83D\uDE80 Fetching platform stats via Firebase Function...");let e=(await p()).data;return h.incrementFunctionUsage(),h.addReadsAvoided(10),console.log("✅ Platform stats fetched successfully:",e),e}catch(e){throw console.error("❌ Error fetching platform stats:",e),Error("Failed to fetch platform stats")}}async function y(){try{console.log("\uD83D\uDE80 Getting optimized user work data..."),await o();let e=await a();return console.log("✅ Optimized user work data retrieved"),e.data}catch(e){throw console.error("❌ Error getting optimized user work data:",e),e}}async function j(e=50){try{console.log(`🚀 Submitting optimized translation batch: ${e} translations`);let t=await n({batchSize:e});return console.log("✅ Optimized translation batch submitted"),t.data}catch(e){throw console.error("❌ Error submitting optimized translation batch:",e),e}}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4573,6803],()=>s(8410));module.exports=r})();