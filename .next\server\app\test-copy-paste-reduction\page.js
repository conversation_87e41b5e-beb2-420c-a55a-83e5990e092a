(()=>{var e={};e.id=7477,e.ids=[7477,7878],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22715:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-copy-paste-reduction\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-reduction\\page.tsx","default")},27878:(e,s,t)=>{"use strict";t.d(s,{Mk:()=>p,checkCopyPastePermission:()=>l,grantCopyPastePermission:()=>c,i7:()=>u,removeCopyPastePermission:()=>d});var r=t(33784),i=t(75535);let o={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},a={users:"users"};class n{static async checkCopyPastePermission(e){try{let s=await (0,i.x7)((0,i.H9)(r.db,a.users,e));if(!s.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let t=s.data()[o.quickTranslationAdvantageExpiry];if(!t)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let n=t.toDate(),l=new Date,c=n>l,d=c?Math.ceil((n.getTime()-l.getTime())/864e5):0;return{hasPermission:c,daysRemaining:d,expiryDate:n}}catch(s){return console.error(`Error checking copy-paste permission for user ${e}:`,s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,s){try{let t=new Date;t.setDate(t.getDate()+s);let n=(0,i.H9)(r.db,a.users,e);await (0,i.mZ)(n,{[o.quickTranslationAdvantageExpiry]:i.Dc.fromDate(t),[o.lastCopyPasteReduction]:i.Dc.now()}),console.log(`✅ Granted copy-paste permission to user ${e} for ${s} days (expires: ${t.toDateString()})`)}catch(s){throw console.error(`Error granting copy-paste permission to user ${e}:`,s),s}}static async removeCopyPastePermission(e){try{let s=(0,i.H9)(r.db,a.users,e);await (0,i.mZ)(s,{[o.quickTranslationAdvantageExpiry]:null}),console.log(`✅ Removed copy-paste permission from user ${e}`)}catch(s){throw console.error(`Error removing copy-paste permission from user ${e}:`,s),s}}static async reduceCopyPasteDays(e){try{let s=await (0,i.x7)((0,i.H9)(r.db,a.users,e));if(!s.exists())return{reduced:!1,daysRemaining:0,expired:!1};let t=s.data(),n=t[o.quickTranslationAdvantageExpiry],l=t[o.lastCopyPasteReduction];if(!n)return{reduced:!1,daysRemaining:0,expired:!1};let c=new Date().toDateString();if((l?l.toDate().toDateString():null)===c){let e=n.toDate(),s=new Date,t=Math.max(0,Math.ceil((e.getTime()-s.getTime())/864e5));return{reduced:!1,daysRemaining:t,expired:0===t}}let d=n.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let p=(0,i.H9)(r.db,a.users,e);if(u<=new Date)return await (0,i.mZ)(p,{[o.quickTranslationAdvantageExpiry]:null,[o.lastCopyPasteReduction]:i.Dc.now()}),console.log(`📅 Copy-paste permission expired for user ${e}`),{reduced:!0,daysRemaining:0,expired:!0};{await (0,i.mZ)(p,{[o.quickTranslationAdvantageExpiry]:i.Dc.fromDate(u),[o.lastCopyPasteReduction]:i.Dc.now()});let s=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log(`📅 Reduced copy-paste days for user ${e}: ${s} days remaining`),{reduced:!0,daysRemaining:s,expired:!1}}}catch(s){return console.error(`Error reducing copy-paste days for user ${e}:`,s),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,i.getDocs)((0,i.collection)(r.db,a.users)),s=0,t=0,o=0,l=0;for(let r of e.docs)try{s++;let e=await n.reduceCopyPasteDays(r.id);e.reduced&&(t++,e.expired&&o++)}catch(e){l++,console.error(`Error processing copy-paste reduction for user ${r.id}:`,e)}return console.log(`✅ Daily copy-paste reduction complete:`),console.log(`   - Processed: ${s} users`),console.log(`   - Reduced: ${t} users`),console.log(`   - Expired: ${o} users`),console.log(`   - Errors: ${l} users`),{processed:s,reduced:t,expired:o,errors:l}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let s=await n.checkCopyPastePermission(e);return{hasPermission:s.hasPermission,daysRemaining:s.daysRemaining,expiryDate:s.expiryDate?s.expiryDate.toDateString():null}}catch(s){return console.error(`Error getting copy-paste status for user ${e}:`,s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let l=n.checkCopyPastePermission,c=n.grantCopyPastePermission,d=n.removeCopyPastePermission,u=n.reduceCopyPasteDays,p=n.processAllUsersCopyPasteReduction;n.getCopyPasteStatus},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28624:(e,s,t)=>{Promise.resolve().then(t.bind(t,88193))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88193:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(60687),i=t(43210),o=t(87979),a=t(27878);function n(){let{user:e,loading:s}=(0,o.Nu)(),[t,n]=(0,i.useState)(""),[l,c]=(0,i.useState)(null),[d,u]=(0,i.useState)(!1),p=async()=>{if(!t.trim())return void alert("Please enter a user ID");u(!0),c(null);try{console.log(`🧪 Testing copy-paste reduction for user: ${t}`),console.log("1. Checking current copy-paste status...");let e=await (0,a.checkCopyPastePermission)(t.trim());console.log("✅ Before reduction:",e),console.log("2. Performing daily reduction...");let s=await (0,a.i7)(t.trim());console.log("✅ Reduction result:",s),console.log("3. Checking status after reduction...");let r=await (0,a.checkCopyPastePermission)(t.trim());console.log("✅ After reduction:",r),c({success:!0,beforeStatus:e,reductionResult:s,afterStatus:r,message:"Single user reduction test completed successfully"})}catch(e){console.error("❌ Error in single user reduction test:",e),c({success:!1,error:e.message,stack:e.stack,message:"Single user reduction test failed"})}finally{u(!1)}},m=async()=>{u(!0),c(null);try{console.log("\uD83E\uDDEA Testing copy-paste reduction for all users...");let e=await (0,a.Mk)();console.log("✅ All users reduction result:",e),c({success:!0,allUsersResult:e,message:"All users reduction test completed successfully"})}catch(e){console.error("❌ Error in all users reduction test:",e),c({success:!1,error:e.message,stack:e.stack,message:"All users reduction test failed"})}finally{u(!1)}},x=async()=>{if(!t.trim())return void alert("Please enter a user ID");u(!0);try{console.log(`🔧 Granting 3-day copy-paste permission to user: ${t}`),await (0,a.grantCopyPastePermission)(t.trim(),3);let e=await (0,a.checkCopyPastePermission)(t.trim());c({success:!0,status:e,message:"3-day copy-paste permission granted for testing"})}catch(e){c({success:!1,error:e.message,message:"Failed to grant test permission"})}finally{u(!1)}};return s?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,r.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,r.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,r.jsxs)("div",{className:"glass-card p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Copy-Paste Daily Reduction"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white mb-2",children:"User ID (for single user test):"}),(0,r.jsx)("input",{type:"text",value:t,onChange:e=>n(e.target.value),className:"w-full p-3 rounded-lg bg-white/10 text-white border border-white/20",placeholder:"Enter user ID"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[(0,r.jsx)("button",{onClick:x,disabled:d,className:"bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:d?"Processing...":"Grant 3-Day Permission"}),(0,r.jsx)("button",{onClick:p,disabled:d,className:"bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:d?"Testing...":"Test Single User"}),(0,r.jsx)("button",{onClick:m,disabled:d,className:"bg-orange-600 hover:bg-orange-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:d?"Testing...":"Test All Users"})]}),l&&(0,r.jsxs)("div",{className:`p-4 rounded-lg ${l.success?"bg-green-500/20 border border-green-500/30":"bg-red-500/20 border border-red-500/30"}`,children:[(0,r.jsx)("h3",{className:`font-bold ${l.success?"text-green-400":"text-red-400"}`,children:l.success?"Success!":"Failed"}),(0,r.jsx)("p",{className:"text-white mt-2",children:l.message}),l.beforeStatus&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Before Reduction:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(l.beforeStatus,null,2)})]}),l.reductionResult&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Reduction Result:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(l.reductionResult,null,2)})]}),l.afterStatus&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"After Reduction:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(l.afterStatus,null,2)})]}),l.allUsersResult&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"All Users Result:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(l.allUsersResult,null,2)})]}),l.status&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Permission Status:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(l.status,null,2)})]}),l.error&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-red-500/20 rounded",children:[(0,r.jsx)("h4",{className:"text-red-400 font-semibold",children:"Error Details:"}),(0,r.jsx)("p",{className:"text-white/80 text-sm mt-1",children:l.error}),l.stack&&(0,r.jsx)("pre",{className:"text-white/60 text-xs mt-2 overflow-auto",children:l.stack})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 p-4 bg-white/5 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-2",children:"How to Test:"}),(0,r.jsxs)("ol",{className:"text-white/80 text-sm space-y-1 list-decimal list-inside",children:[(0,r.jsx)("li",{children:"Enter a user ID in the input field"}),(0,r.jsx)("li",{children:'Click "Grant 3-Day Permission" to give test permission'}),(0,r.jsx)("li",{children:'Click "Test Single User" to test reduction for that user'}),(0,r.jsx)("li",{children:'Click "Test All Users" to test reduction for all users'}),(0,r.jsx)("li",{children:"Check browser console for detailed logs"}),(0,r.jsx)("li",{children:"Verify that days are reduced by 1 each time"})]})]}),(0,r.jsxs)("div",{className:"mt-4 p-4 bg-blue-500/10 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-blue-400 font-bold mb-2",children:"Daily Reduction Logic:"}),(0,r.jsxs)("ul",{className:"text-blue-300 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• Runs automatically every day via DailyActiveDaysScheduler"}),(0,r.jsx)("li",{children:"• Reduces expiry date by 1 day for each user"}),(0,r.jsx)("li",{children:"• Only reduces once per day per user"}),(0,r.jsx)("li",{children:"• Removes permission when expiry date is reached"}),(0,r.jsx)("li",{children:"• Tracks last reduction to prevent duplicates"})]})]})]})})})}},91645:e=>{"use strict";e.exports=require("net")},93776:(e,s,t)=>{Promise.resolve().then(t.bind(t,22715))},94735:e=>{"use strict";e.exports=require("events")},96136:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=t(65239),i=t(48088),o=t(88170),a=t.n(o),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(s,l);let c={children:["",{children:["test-copy-paste-reduction",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,22715)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-reduction\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-reduction\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-copy-paste-reduction/page",pathname:"/test-copy-paste-reduction",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4573,6803],()=>t(96136));module.exports=r})();