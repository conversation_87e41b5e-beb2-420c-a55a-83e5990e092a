(()=>{var e={};e.id=8363,e.ids=[8363],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},5633:(e,r,s)=>{Promise.resolve().then(s.bind(s,74813))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},50071:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var t=s(60687),i=s(43210),n=s(63385),o=s(75535),a=s(33784),d=s(77567);function u(){let[e,r]=(0,i.useState)(!1),[s,u]=(0,i.useState)("<EMAIL>"),[l,c]=(0,i.useState)("123456"),p=async()=>{if(!s||!l)return void d.A.fire({icon:"error",title:"Error",text:"Please enter admin email and password"});r(!0);try{let e=(await (0,n.x9)(a.auth,s,l)).user,r={email:e.email,name:"MyTube Admin",role:"super_admin",permissions:["all"],createdAt:o.Dc.now(),isActive:!0,uid:e.uid};await (0,o.BN)((0,o.H9)(a.db,"admins",e.uid),r),d.A.fire({icon:"success",title:"Permissions Fixed!",text:"Admin permissions have been successfully fixed. You can now perform admin actions.",confirmButtonText:"Go to Admin Dashboard"}).then(()=>{window.location.href="/admin"})}catch(r){console.error("Error fixing admin permissions:",r);let e="Failed to fix admin permissions";switch(r.code){case"auth/user-not-found":e="Admin account not found";break;case"auth/wrong-password":e="Incorrect password";break;case"auth/invalid-email":e="Invalid email address";break;default:e=r.message||"Failed to fix admin permissions"}d.A.fire({icon:"error",title:"Error",text:e})}finally{r(!1)}};return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900",children:(0,t.jsxs)("div",{className:"glass-card p-8 w-full max-w-md",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Fix Admin Permissions"}),(0,t.jsx)("p",{className:"text-white/70",children:"This will create the required admin document in Firestore to fix permission issues."})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Admin Email"}),(0,t.jsx)("input",{type:"email",value:s,onChange:e=>u(e.target.value),className:"input-field",placeholder:"<EMAIL>",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Admin Password"}),(0,t.jsx)("input",{type:"password",value:l,onChange:e=>c(e.target.value),className:"input-field",placeholder:"Enter admin password",required:!0})]}),(0,t.jsx)("button",{onClick:p,disabled:e,className:`w-full py-3 px-4 rounded-lg font-medium transition-all ${e?"bg-gray-600 cursor-not-allowed":"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"} text-white`,children:e?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Fixing Permissions..."]}):"Fix Admin Permissions"}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("a",{href:"/admin/login",className:"text-blue-400 hover:text-blue-300 text-sm",children:"Back to Admin Login"})})]}),(0,t.jsx)("div",{className:"mt-8 p-4 bg-yellow-500/20 border border-yellow-400/30 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("i",{className:"fas fa-exclamation-triangle text-yellow-400 mr-2 mt-1"}),(0,t.jsxs)("div",{className:"text-yellow-200 text-sm",children:[(0,t.jsx)("p",{className:"font-medium mb-1",children:"Important:"}),(0,t.jsx)("p",{children:"This page fixes the admin permissions issue by creating the required admin document in Firestore. Run this once if you're getting permission errors."})]})]})})]})})}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58433:(e,r,s)=>{Promise.resolve().then(s.bind(s,50071))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74813:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\admin\\\\fix-permissions\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\fix-permissions\\page.tsx","default")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},81820:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>p,tree:()=>u});var t=s(65239),i=s(48088),n=s(88170),o=s.n(n),a=s(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);s.d(r,d);let u={children:["",{children:["admin",{children:["fix-permissions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,74813)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\fix-permissions\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\fix-permissions\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/fix-permissions/page",pathname:"/admin/fix-permissions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4573,6803],()=>s(81820));module.exports=t})();