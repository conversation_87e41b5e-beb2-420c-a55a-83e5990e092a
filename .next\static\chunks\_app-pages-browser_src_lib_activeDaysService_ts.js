"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_activeDaysService_ts"],{

/***/ "(app-pages-browser)/./src/lib/activeDaysService.ts":
/*!**************************************!*\
  !*** ./src/lib/activeDaysService.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActiveDaysService: () => (/* binding */ ActiveDaysService),\n/* harmony export */   calculateActiveDays: () => (/* binding */ calculateActiveDays),\n/* harmony export */   forceUpdateActiveDays: () => (/* binding */ forceUpdateActiveDays),\n/* harmony export */   getActiveDaysDisplay: () => (/* binding */ getActiveDaysDisplay),\n/* harmony export */   getActiveDaysStatistics: () => (/* binding */ getActiveDaysStatistics),\n/* harmony export */   getUserActiveDays: () => (/* binding */ getUserActiveDays),\n/* harmony export */   initializeActiveDaysForNewUser: () => (/* binding */ initializeActiveDaysForNewUser),\n/* harmony export */   isUserPlanExpired: () => (/* binding */ isUserPlanExpired),\n/* harmony export */   processAllUsersActiveDays: () => (/* binding */ processAllUsersActiveDays),\n/* harmony export */   updateUserActiveDays: () => (/* binding */ updateUserActiveDays)\n/* harmony export */ });\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n\n\n// Field names and collections\nconst FIELD_NAMES = {\n    activeDays: 'activeDays',\n    lastActiveDayUpdate: 'lastActiveDayUpdate',\n    joinedDate: 'joinedDate',\n    plan: 'plan',\n    name: 'name',\n    email: 'email'\n};\nconst COLLECTIONS = {\n    users: 'users'\n};\n/**\n * UNIFIED ACTIVE DAYS SERVICE\n *\n * This is the SINGLE source of truth for all active days calculations.\n * All pages, admin functions, and services should use this service.\n *\n * Key Features:\n * - Ensures active days increment by 1 only once per day\n * - Handles all user types: Trial, Paid Plans, Admin\n * - Respects user leave days\n * - Provides comprehensive logging and error handling\n * - Thread-safe with timestamp-based deduplication\n */ class ActiveDaysService {\n    /**\n   * Calculate active days for a user based on their registration and leave history\n   */ static async calculateActiveDays(userId) {\n        try {\n            var _userData_joinedDate, _userData_lastActiveDayUpdate;\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId));\n            if (!userDoc.exists()) {\n                console.error(\"User \".concat(userId, \" not found\"));\n                return {\n                    activeDays: 0,\n                    shouldUpdate: false,\n                    isNewDay: false\n                };\n            }\n            const userData = userDoc.data();\n            const joinedDate = ((_userData_joinedDate = userData.joinedDate) === null || _userData_joinedDate === void 0 ? void 0 : _userData_joinedDate.toDate()) || new Date();\n            const lastUpdate = (_userData_lastActiveDayUpdate = userData.lastActiveDayUpdate) === null || _userData_lastActiveDayUpdate === void 0 ? void 0 : _userData_lastActiveDayUpdate.toDate();\n            const currentActiveDays = userData.activeDays || 0;\n            const plan = userData.plan || 'Trial';\n            const today = new Date();\n            const todayString = today.toDateString();\n            const lastUpdateString = lastUpdate ? lastUpdate.toDateString() : null;\n            console.log(\"\\uD83D\\uDCC5 Calculating active days for user \".concat(userId, \":\"));\n            console.log(\"   - Joined: \".concat(joinedDate.toDateString()));\n            console.log(\"   - Current active days: \".concat(currentActiveDays));\n            console.log(\"   - Last update: \".concat(lastUpdateString || 'Never'));\n            console.log(\"   - Today: \".concat(todayString));\n            console.log(\"   - Plan: \".concat(plan));\n            console.log(\"   - Is new day: \".concat(lastUpdateString !== todayString));\n            // Check if it's a new day\n            const isNewDay = lastUpdateString !== todayString;\n            if (!isNewDay) {\n                console.log(\"✅ Already updated today for user \".concat(userId));\n                return {\n                    activeDays: currentActiveDays,\n                    shouldUpdate: false,\n                    isNewDay: false\n                };\n            }\n            // Skip active days increment for admins\n            if (plan === 'Admin') {\n                console.log(\"⏭️ Skipping active days increment for admin user \".concat(userId));\n                await ActiveDaysService.updateLastActiveDayUpdate(userId);\n                return {\n                    activeDays: currentActiveDays,\n                    shouldUpdate: false,\n                    isNewDay: true\n                };\n            }\n            // Check if user is on leave today\n            const isOnLeave = await ActiveDaysService.isUserOnLeaveToday(userId);\n            if (isOnLeave) {\n                console.log(\"\\uD83C\\uDFD6️ User \".concat(userId, \" is on leave today, not incrementing active days\"));\n                await ActiveDaysService.updateLastActiveDayUpdate(userId);\n                return {\n                    activeDays: currentActiveDays,\n                    shouldUpdate: false,\n                    isNewDay: true\n                };\n            }\n            // Calculate new active days\n            let newActiveDays;\n            if (plan === 'Trial') {\n                // For trial users: calculate based on days since joining\n                const daysSinceJoining = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24));\n                newActiveDays = daysSinceJoining + 1 // Day 1 starts on registration day\n                ;\n            } else {\n                // For paid plans: increment from current active days\n                newActiveDays = currentActiveDays + 1;\n            }\n            console.log(\"\\uD83D\\uDCC8 New active days calculated: \".concat(currentActiveDays, \" → \").concat(newActiveDays));\n            return {\n                activeDays: newActiveDays,\n                shouldUpdate: newActiveDays !== currentActiveDays,\n                isNewDay: true\n            };\n        } catch (error) {\n            console.error(\"Error calculating active days for user \".concat(userId, \":\"), error);\n            return {\n                activeDays: 0,\n                shouldUpdate: false,\n                isNewDay: false\n            };\n        }\n    }\n    /**\n   * Update user's active days\n   */ static async updateUserActiveDays(userId) {\n        try {\n            const calculation = await ActiveDaysService.calculateActiveDays(userId);\n            if (calculation.shouldUpdate) {\n                const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId);\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(userRef, {\n                    [FIELD_NAMES.activeDays]: calculation.activeDays,\n                    [FIELD_NAMES.lastActiveDayUpdate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n                });\n                console.log(\"✅ Updated active days for user \".concat(userId, \": \").concat(calculation.activeDays));\n            } else if (calculation.isNewDay) {\n                // Update timestamp even if active days didn't change\n                await ActiveDaysService.updateLastActiveDayUpdate(userId);\n            }\n            return calculation.activeDays;\n        } catch (error) {\n            console.error(\"Error updating active days for user \".concat(userId, \":\"), error);\n            throw error;\n        }\n    }\n    /**\n   * Update only the last active day update timestamp\n   */ static async updateLastActiveDayUpdate(userId) {\n        try {\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(userRef, {\n                [FIELD_NAMES.lastActiveDayUpdate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n            });\n        } catch (error) {\n            console.error(\"Error updating last active day timestamp for user \".concat(userId, \":\"), error);\n        }\n    }\n    /**\n   * Check if user is on leave today\n   */ static async isUserOnLeaveToday(userId) {\n        try {\n            const { isUserOnLeave } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_leaveService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(app-pages-browser)/./src/lib/leaveService.ts\"));\n            return await isUserOnLeave(userId, new Date());\n        } catch (error) {\n            console.error(\"Error checking leave status for user \".concat(userId, \":\"), error);\n            return false // Default to not on leave to avoid blocking work\n            ;\n        }\n    }\n    /**\n   * Process all users' active days (daily scheduler)\n   */ static async processAllUsersActiveDays() {\n        try {\n            console.log('🔄 Starting daily active days processing for all users...');\n            const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users));\n            let processed = 0;\n            let updated = 0;\n            let errors = 0;\n            for (const userDoc of usersSnapshot.docs){\n                try {\n                    processed++;\n                    const calculation = await ActiveDaysService.calculateActiveDays(userDoc.id);\n                    if (calculation.shouldUpdate || calculation.isNewDay) {\n                        await ActiveDaysService.updateUserActiveDays(userDoc.id);\n                        if (calculation.shouldUpdate) updated++;\n                    }\n                } catch (error) {\n                    errors++;\n                    console.error(\"Error processing active days for user \".concat(userDoc.id, \":\"), error);\n                }\n            }\n            console.log(\"✅ Daily active days processing complete:\");\n            console.log(\"   - Processed: \".concat(processed, \" users\"));\n            console.log(\"   - Updated: \".concat(updated, \" users\"));\n            console.log(\"   - Errors: \".concat(errors, \" users\"));\n            return {\n                processed,\n                updated,\n                errors\n            };\n        } catch (error) {\n            console.error('Error in daily active days processing:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get active days for a user (read-only)\n   */ static async getUserActiveDays(userId) {\n        try {\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId));\n            if (!userDoc.exists()) {\n                return 0;\n            }\n            const userData = userDoc.data();\n            return userData.activeDays || 0;\n        } catch (error) {\n            console.error(\"Error getting active days for user \".concat(userId, \":\"), error);\n            return 0;\n        }\n    }\n    /**\n   * Initialize active days for new user (called during registration)\n   */ static async initializeActiveDaysForNewUser(userId) {\n        try {\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(userRef, {\n                [FIELD_NAMES.activeDays]: 1,\n                [FIELD_NAMES.lastActiveDayUpdate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n            });\n            console.log(\"✅ Initialized active days for new user \".concat(userId, \": Day 1\"));\n        } catch (error) {\n            console.error(\"Error initializing active days for user \".concat(userId, \":\"), error);\n            throw error;\n        }\n    }\n    /**\n   * Get active days display format for UI (current/total)\n   */ static async getActiveDaysDisplay(userId) {\n        try {\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId));\n            if (!userDoc.exists()) {\n                return {\n                    current: 0,\n                    total: 2,\n                    displayText: '0/2'\n                };\n            }\n            const userData = userDoc.data();\n            const plan = userData.plan || 'Trial';\n            const activeDays = userData.activeDays || 0;\n            let total;\n            if (plan === 'Trial') {\n                total = 2;\n            } else {\n                total = 30 // Standard plan duration (expires on 31st day)\n                ;\n            }\n            return {\n                current: activeDays,\n                total,\n                displayText: \"\".concat(activeDays, \"/\").concat(total)\n            };\n        } catch (error) {\n            console.error(\"Error getting active days display for user \".concat(userId, \":\"), error);\n            return {\n                current: 0,\n                total: 2,\n                displayText: '0/2'\n            };\n        }\n    }\n    /**\n   * Check if user's plan is expired based on active days\n   */ static async isUserPlanExpired(userId) {\n        try {\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId));\n            if (!userDoc.exists()) {\n                return {\n                    expired: true,\n                    reason: 'User not found'\n                };\n            }\n            const userData = userDoc.data();\n            const plan = userData.plan || 'Trial';\n            const activeDays = userData.activeDays || 0;\n            const planExpiry = userData.planExpiry;\n            console.log(\"\\uD83D\\uDCC5 Checking plan expiry for user \".concat(userId, \":\"), {\n                plan,\n                activeDays,\n                hasPlanExpiry: !!planExpiry,\n                planExpiryDate: planExpiry ? planExpiry.toDate() : null\n            });\n            if (plan === 'Admin') {\n                const result = {\n                    expired: false,\n                    activeDays\n                };\n                console.log(\"\\uD83D\\uDCC5 Plan expiry result for admin user \".concat(userId, \":\"), result);\n                return result;\n            }\n            if (plan === 'Trial') {\n                const daysLeft = Math.max(0, 2 - activeDays);\n                const result = {\n                    expired: daysLeft <= 0,\n                    reason: daysLeft <= 0 ? 'Trial period expired' : undefined,\n                    daysLeft,\n                    activeDays\n                };\n                console.log(\"\\uD83D\\uDCC5 Plan expiry result for trial user \".concat(userId, \":\"), result);\n                return result;\n            }\n            // For paid plans, check if planExpiry is set\n            if (planExpiry) {\n                const today = new Date();\n                const expiryDate = planExpiry.toDate();\n                const expired = today > expiryDate;\n                const daysLeft = expired ? 0 : Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                const result = {\n                    expired,\n                    reason: expired ? 'Plan subscription expired' : undefined,\n                    daysLeft,\n                    activeDays\n                };\n                console.log(\"\\uD83D\\uDCC5 Plan expiry result for user \".concat(userId, \" (using planExpiry field):\"), result);\n                return result;\n            }\n            // If planExpiry field is empty, use active days to determine expiry\n            // Paid plans (Junior, Senior, Expert) expire after 30 active days\n            const daysLeft = Math.max(0, 30 - activeDays);\n            const expired = activeDays > 30 // Expire if user has more than 30 active days\n            ;\n            const result = {\n                expired,\n                reason: expired ? \"Plan expired - You have used \".concat(activeDays, \" days out of 30 allowed days\") : undefined,\n                daysLeft,\n                activeDays\n            };\n            console.log(\"\\uD83D\\uDCC5 Plan expiry result for user \".concat(userId, \":\"), result);\n            return result;\n        } catch (error) {\n            console.error(\"Error checking plan expiry for user \".concat(userId, \":\"), error);\n            return {\n                expired: true,\n                reason: 'Error checking plan status'\n            };\n        }\n    }\n    /**\n   * Set plan expiry date for users who don't have it set\n   * This is useful for migrating existing users\n   */ static async setPlanExpiryForUser(userId) {\n        let planDurationDays = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 30;\n        try {\n            var _userData_joinedDate;\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId);\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(userDocRef);\n            if (!userDoc.exists()) {\n                console.error(\"User \".concat(userId, \" not found\"));\n                return false;\n            }\n            const userData = userDoc.data();\n            const plan = userData.plan || 'Trial';\n            // Don't set expiry for Trial or Admin users\n            if (plan === 'Trial' || plan === 'Admin') {\n                console.log(\"Skipping plan expiry setup for \".concat(plan, \" user: \").concat(userId));\n                return false;\n            }\n            // Don't overwrite existing planExpiry\n            if (userData.planExpiry) {\n                console.log(\"User \".concat(userId, \" already has plan expiry set: \").concat(userData.planExpiry.toDate()));\n                return false;\n            }\n            // Calculate expiry date based on join date + plan duration\n            const joinedDate = ((_userData_joinedDate = userData.joinedDate) === null || _userData_joinedDate === void 0 ? void 0 : _userData_joinedDate.toDate()) || new Date();\n            const expiryDate = new Date(joinedDate);\n            expiryDate.setDate(expiryDate.getDate() + planDurationDays);\n            // Update user document with plan expiry\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(userDocRef, {\n                planExpiry: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.fromDate(expiryDate),\n                planExpirySetDate: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n            });\n            console.log(\"✅ Set plan expiry for user \".concat(userId, \": \").concat(expiryDate));\n            return true;\n        } catch (error) {\n            console.error(\"Error setting plan expiry for user \".concat(userId, \":\"), error);\n            return false;\n        }\n    }\n    /**\n   * Force update active days for a specific user (admin use only)\n   */ static async forceUpdateActiveDays(userId, newActiveDays, adminId) {\n        try {\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(userRef, {\n                [FIELD_NAMES.activeDays]: newActiveDays,\n                [FIELD_NAMES.lastActiveDayUpdate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.Timestamp.now()\n            });\n            console.log(\"\\uD83D\\uDD27 Admin \".concat(adminId, \" force updated active days for user \").concat(userId, \": \").concat(newActiveDays));\n        } catch (error) {\n            console.error(\"Error force updating active days for user \".concat(userId, \":\"), error);\n            throw error;\n        }\n    }\n    /**\n   * Get comprehensive active days statistics for admin dashboard\n   */ static async getActiveDaysStatistics() {\n        try {\n            const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_0__.db, COLLECTIONS.users));\n            let totalUsers = 0;\n            let trialUsers = 0;\n            let paidUsers = 0;\n            let adminUsers = 0;\n            let totalActiveDays = 0;\n            let usersUpdatedToday = 0;\n            const today = new Date().toDateString();\n            for (const userDoc of usersSnapshot.docs){\n                var _userData_lastActiveDayUpdate;\n                const userData = userDoc.data();\n                const plan = userData.plan || 'Trial';\n                const activeDays = userData.activeDays || 0;\n                const lastUpdate = (_userData_lastActiveDayUpdate = userData.lastActiveDayUpdate) === null || _userData_lastActiveDayUpdate === void 0 ? void 0 : _userData_lastActiveDayUpdate.toDate();\n                totalUsers++;\n                totalActiveDays += activeDays;\n                if (plan === 'Trial') trialUsers++;\n                else if (plan === 'Admin') adminUsers++;\n                else paidUsers++;\n                if (lastUpdate && lastUpdate.toDateString() === today) {\n                    usersUpdatedToday++;\n                }\n            }\n            return {\n                totalUsers,\n                trialUsers,\n                paidUsers,\n                adminUsers,\n                averageActiveDays: totalUsers > 0 ? Math.round(totalActiveDays / totalUsers * 100) / 100 : 0,\n                usersUpdatedToday\n            };\n        } catch (error) {\n            console.error('Error getting active days statistics:', error);\n            throw error;\n        }\n    }\n}\n// Export convenience functions for backward compatibility\nconst calculateActiveDays = ActiveDaysService.calculateActiveDays;\nconst updateUserActiveDays = ActiveDaysService.updateUserActiveDays;\nconst processAllUsersActiveDays = ActiveDaysService.processAllUsersActiveDays;\nconst getUserActiveDays = ActiveDaysService.getUserActiveDays;\nconst initializeActiveDaysForNewUser = ActiveDaysService.initializeActiveDaysForNewUser;\n// Export new unified functions\nconst getActiveDaysDisplay = ActiveDaysService.getActiveDaysDisplay;\nconst isUserPlanExpired = ActiveDaysService.isUserPlanExpired;\nconst forceUpdateActiveDays = ActiveDaysService.forceUpdateActiveDays;\nconst getActiveDaysStatistics = ActiveDaysService.getActiveDaysStatistics;\n// Export the main class for direct usage (recommended)\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/activeDaysService.ts\n"));

/***/ })

}]);