(()=>{var e={};e.id=7646,e.ids=[1391,3772,7646,7878],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19119:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(65239),s=r(48088),o=r(88170),i=r.n(o),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["test-copy-paste-export",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,39094)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-export\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-export\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/test-copy-paste-export/page",pathname:"/test-copy-paste-export",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27878:(e,t,r)=>{"use strict";r.d(t,{Mk:()=>p,checkCopyPastePermission:()=>l,grantCopyPastePermission:()=>c,i7:()=>u,removeCopyPastePermission:()=>d});var a=r(33784),s=r(75535);let o={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},i={users:"users"};class n{static async checkCopyPastePermission(e){try{let t=await (0,s.x7)((0,s.H9)(a.db,i.users,e));if(!t.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let r=t.data()[o.quickTranslationAdvantageExpiry];if(!r)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let n=r.toDate(),l=new Date,c=n>l,d=c?Math.ceil((n.getTime()-l.getTime())/864e5):0;return{hasPermission:c,daysRemaining:d,expiryDate:n}}catch(t){return console.error(`Error checking copy-paste permission for user ${e}:`,t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,t){try{let r=new Date;r.setDate(r.getDate()+t);let n=(0,s.H9)(a.db,i.users,e);await (0,s.mZ)(n,{[o.quickTranslationAdvantageExpiry]:s.Dc.fromDate(r),[o.lastCopyPasteReduction]:s.Dc.now()}),console.log(`✅ Granted copy-paste permission to user ${e} for ${t} days (expires: ${r.toDateString()})`)}catch(t){throw console.error(`Error granting copy-paste permission to user ${e}:`,t),t}}static async removeCopyPastePermission(e){try{let t=(0,s.H9)(a.db,i.users,e);await (0,s.mZ)(t,{[o.quickTranslationAdvantageExpiry]:null}),console.log(`✅ Removed copy-paste permission from user ${e}`)}catch(t){throw console.error(`Error removing copy-paste permission from user ${e}:`,t),t}}static async reduceCopyPasteDays(e){try{let t=await (0,s.x7)((0,s.H9)(a.db,i.users,e));if(!t.exists())return{reduced:!1,daysRemaining:0,expired:!1};let r=t.data(),n=r[o.quickTranslationAdvantageExpiry],l=r[o.lastCopyPasteReduction];if(!n)return{reduced:!1,daysRemaining:0,expired:!1};let c=new Date().toDateString();if((l?l.toDate().toDateString():null)===c){let e=n.toDate(),t=new Date,r=Math.max(0,Math.ceil((e.getTime()-t.getTime())/864e5));return{reduced:!1,daysRemaining:r,expired:0===r}}let d=n.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let p=(0,s.H9)(a.db,i.users,e);if(u<=new Date)return await (0,s.mZ)(p,{[o.quickTranslationAdvantageExpiry]:null,[o.lastCopyPasteReduction]:s.Dc.now()}),console.log(`📅 Copy-paste permission expired for user ${e}`),{reduced:!0,daysRemaining:0,expired:!0};{await (0,s.mZ)(p,{[o.quickTranslationAdvantageExpiry]:s.Dc.fromDate(u),[o.lastCopyPasteReduction]:s.Dc.now()});let t=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log(`📅 Reduced copy-paste days for user ${e}: ${t} days remaining`),{reduced:!0,daysRemaining:t,expired:!1}}}catch(t){return console.error(`Error reducing copy-paste days for user ${e}:`,t),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,s.getDocs)((0,s.collection)(a.db,i.users)),t=0,r=0,o=0,l=0;for(let a of e.docs)try{t++;let e=await n.reduceCopyPasteDays(a.id);e.reduced&&(r++,e.expired&&o++)}catch(e){l++,console.error(`Error processing copy-paste reduction for user ${a.id}:`,e)}return console.log(`✅ Daily copy-paste reduction complete:`),console.log(`   - Processed: ${t} users`),console.log(`   - Reduced: ${r} users`),console.log(`   - Expired: ${o} users`),console.log(`   - Errors: ${l} users`),{processed:t,reduced:r,expired:o,errors:l}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let t=await n.checkCopyPastePermission(e);return{hasPermission:t.hasPermission,daysRemaining:t.daysRemaining,expiryDate:t.expiryDate?t.expiryDate.toDateString():null}}catch(t){return console.error(`Error getting copy-paste status for user ${e}:`,t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let l=n.checkCopyPastePermission,c=n.grantCopyPastePermission,d=n.removeCopyPastePermission,u=n.reduceCopyPasteDays,p=n.processAllUsersCopyPasteReduction;n.getCopyPasteStatus},27910:e=>{"use strict";e.exports=require("stream")},28181:(e,t,r)=>{Promise.resolve().then(r.bind(r,39094))},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},39094:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-copy-paste-export\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-export\\page.tsx","default")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65232:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(60687),s=r(43210),o=r(87979),i=r(91391),n=r(83475),l=r(27878);function c(){let{user:e,loading:t}=(0,o.Nu)(),[r,c]=(0,s.useState)(null),[d,u]=(0,s.useState)(!1),p=async()=>{u(!0),c(null);try{console.log("\uD83E\uDDEA Testing copy-paste export functionality...");let e=await (0,i.CF)();console.log(`📊 Retrieved ${e.length} users for testing`);let t=e.slice(0,5),r=[];for(let e of t)try{let t=await (0,l.checkCopyPastePermission)(e.id),a={email:e.email,name:e.name,hasQuickTranslationAdvantageExpiry:!!e.quickTranslationAdvantageExpiry,hasQuickVideoAdvantageExpiry:!!e.quickVideoAdvantageExpiry,quickTranslationAdvantageExpiry:e.quickTranslationAdvantageExpiry,quickVideoAdvantageExpiry:e.quickVideoAdvantageExpiry,servicePermission:t.hasPermission,serviceDaysRemaining:t.daysRemaining,serviceExpiryDate:t.expiryDate};r.push(a),console.log(`📋 User ${e.email}:`,a)}catch(t){console.error(`❌ Error testing user ${e.email}:`,t)}let a=(0,n.Fz)(t);console.log("\uD83D\uDCCA Export data sample:",a.slice(0,2));let s=e.filter(e=>e.quickTranslationAdvantageExpiry||e.quickVideoAdvantageExpiry);c({success:!0,totalUsers:e.length,usersWithCopyPaste:s.length,testUsers:r,exportSample:a.slice(0,2)})}catch(e){console.error("❌ Test failed:",e),c({success:!1,error:e.message})}finally{u(!1)}},y=async()=>{try{let e=await (0,i.CF)(),t=(0,n.Fz)(e);(0,n.Bf)(t,"test_copy_paste_export"),alert(`Downloaded test export with ${e.length} users. Check the Copy-Paste columns!`)}catch(e){console.error("Export failed:",e),alert("Export failed. Check console for details.")}};return t?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,a.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Copy-Paste Export"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("button",{onClick:p,disabled:d,className:"bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold disabled:opacity-50",children:d?"Testing...":"Test Copy-Paste Data"}),(0,a.jsx)("button",{onClick:y,className:"bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg font-semibold",children:"Download Test Export"})]}),r&&(0,a.jsx)("div",{className:"p-4 bg-white/10 rounded-lg",children:r.success?(0,a.jsxs)("div",{className:"text-white space-y-4",children:[(0,a.jsx)("h3",{className:"font-bold text-green-400",children:"Test Results:"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Total Users:"})," ",r.totalUsers]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Users with Copy-Paste:"})," ",r.usersWithCopyPaste]})]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-bold mb-2",children:"Sample Users Test:"}),(0,a.jsx)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:r.testUsers.map((e,t)=>(0,a.jsxs)("div",{className:"p-2 bg-white/5 rounded text-sm",children:[(0,a.jsx)("p",{children:(0,a.jsx)("strong",{children:e.email})}),(0,a.jsxs)("p",{children:["Service Permission: ",e.servicePermission?"Yes":"No"]}),(0,a.jsxs)("p",{children:["Service Days Remaining: ",e.serviceDaysRemaining]}),(0,a.jsxs)("p",{children:["Has Expiry Field: ",e.hasQuickTranslationAdvantageExpiry?"Yes":"No"]})]},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-bold mb-2",children:"Export Sample:"}),(0,a.jsx)("div",{className:"text-xs bg-black/20 p-2 rounded overflow-x-auto",children:(0,a.jsx)("pre",{children:JSON.stringify(r.exportSample,null,2)})})]})]}):(0,a.jsxs)("div",{className:"text-red-400",children:[(0,a.jsx)("h3",{className:"font-bold",children:"Test Failed:"}),(0,a.jsx)("p",{children:r.error})]})})]}),(0,a.jsxs)("div",{className:"mt-8 p-4 bg-white/5 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,a.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,a.jsx)("li",{children:'• Click "Test Copy-Paste Data" to analyze first 5 users'}),(0,a.jsx)("li",{children:'• Click "Download Test Export" to get full CSV with copy-paste data'}),(0,a.jsx)("li",{children:"• Check console for detailed debugging information"}),(0,a.jsx)("li",{children:"• Verify Copy-Paste columns in the downloaded CSV"})]})]})]})})})}},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83475:(e,t,r)=>{"use strict";function a(e,t,r){if(!e||0===e.length)return void alert("No data to export");let a=r||Object.keys(e[0]),s=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],o=new Blob(["\uFEFF"+[a.join(","),...e.map(e=>a.map(t=>{let r=e[t];if(null==r)return"";let a=s.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof r){let e=r.replace(/"/g,'""');return`"${e}"`}return r instanceof Date?`"${r.toLocaleDateString()}"`:"object"==typeof r&&null!==r&&r.toDate?`"${r.toDate().toLocaleDateString()}"`:a&&("number"==typeof r||!isNaN(Number(r)))?`"${r}"`:"number"==typeof r?r.toString():`"${String(r)}"`}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(o);i.setAttribute("href",e),i.setAttribute("download",`${t}_${new Date().toISOString().split("T")[0]}.csv`),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function s(e){return e.map(t=>{let r=0,a=null,s="No",o=t.quickTranslationAdvantageExpiry||t.quickVideoAdvantageExpiry;if(o)try{o instanceof Date?a=o:o.toDate&&"function"==typeof o.toDate?a=o.toDate():a=new Date(o);let i=new Date,n=a.getTime()-i.getTime();s=(r=Math.max(0,Math.ceil(n/864e5)))>0?"Yes":"No",5>e.indexOf(t)&&console.log(`📊 Export debug for user ${t.email}:`,{expiryField:o,expiryFieldType:typeof o,copyPasteExpiryDate:a,copyPasteRemainingDays:r,copyPastePermission:s,hasQuickTranslationAdvantageExpiry:!!t.quickTranslationAdvantageExpiry,hasQuickVideoAdvantageExpiry:!!t.quickVideoAdvantageExpiry})}catch(e){console.error(`❌ Error calculating copy-paste days for user ${t.email}:`,e)}else 5>e.indexOf(t)&&console.log(`📊 Export debug for user ${t.email}: No copy-paste expiry field found`);return{"User ID":t.id||"",Name:t.name||"",Email:t.email||"",Mobile:String(t.mobile||""),"Referral Code":t.referralCode||"","Referred By":t.referredBy||"Direct","Referrals Count":t.referralCount||0,Plan:t.plan||"","Plan Expiry":t.planExpiry instanceof Date?t.planExpiry.toLocaleDateString():t.planExpiry?new Date(t.planExpiry).toLocaleDateString():"","Active Days":t.activeDays||0,"Total Translations":t.totalTranslations||t.totalVideos||0,"Today Translations":t.todayTranslations||t.todayVideos||0,"Last Translation Date":t.lastTranslationDate instanceof Date?t.lastTranslationDate.toLocaleDateString():t.lastTranslationDate?new Date(t.lastTranslationDate).toLocaleDateString():t.lastVideoDate instanceof Date?t.lastVideoDate.toLocaleDateString():t.lastVideoDate?new Date(t.lastVideoDate).toLocaleDateString():"","Copy-Paste Permission":s,"Copy-Paste Remaining Days":r,"Copy-Paste Expiry":a?a.toLocaleDateString():"","Copy-Paste Granted By":t.quickTranslationAdvantageGrantedBy||t.quickVideoAdvantageGrantedBy||"","Copy-Paste Granted At":t.quickTranslationAdvantageGrantedAt?t.quickTranslationAdvantageGrantedAt instanceof Date?t.quickTranslationAdvantageGrantedAt.toLocaleDateString():new Date(t.quickTranslationAdvantageGrantedAt).toLocaleDateString():"","Wallet Balance":t.wallet||0,"Referral Bonus Credited":t.referralBonusCredited?"Yes":"No",Status:t.status||"","Joined Date":t.joinedDate instanceof Date?t.joinedDate.toLocaleDateString():t.joinedDate?new Date(t.joinedDate).toLocaleDateString():"","Joined Time":t.joinedDate instanceof Date?t.joinedDate.toLocaleTimeString():t.joinedDate?new Date(t.joinedDate).toLocaleTimeString():""}})}function o(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function i(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":e.bankDetails?.accountHolderName||"","Bank Name":e.bankDetails?.bankName||"","Account Number":String(e.bankDetails?.accountNumber||""),"IFSC Code":e.bankDetails?.ifscCode||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}))}function n(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}r.d(t,{Bf:()=>a,Fz:()=>s,Pe:()=>n,dB:()=>i,sL:()=>o})},86389:(e,t,r)=>{Promise.resolve().then(r.bind(r,65232))},91391:(e,t,r)=>{"use strict";r.d(t,{CF:()=>d,P_:()=>x,Pn:()=>n,TK:()=>y,getWithdrawals:()=>p,hG:()=>g,lo:()=>l,nQ:()=>u,searchUsers:()=>c,updateWithdrawalStatus:()=>m});var a=r(75535),s=r(33784),o=r(3582);let i=new Map;async function n(){let e="dashboard-stats",t=function(e){let t=i.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let r=a.Dc.fromDate(t),n=await (0,a.getDocs)((0,a.collection)(s.db,o.COLLECTIONS.users)),l=n.size,c=(0,a.P)((0,a.collection)(s.db,o.COLLECTIONS.users),(0,a._M)(o.Yr.joinedDate,">=",r)),d=(await (0,a.getDocs)(c)).size,u=0,p=0,y=0,g=0;n.forEach(e=>{let r=e.data(),a=Number(r[o.Yr.totalTranslations])||0,s=Number(r[o.Yr.wallet])||0;u+=a,p+=s;let i=r[o.Yr.lastTranslationDate]?.toDate();if(i&&i.toDateString()===t.toDateString()){let e=Number(r[o.Yr.todayTranslations])||0;y+=e}});try{let e=(0,a.P)((0,a.collection)(s.db,o.COLLECTIONS.transactions),(0,a._M)(o.Yr.type,"==","translation_earning"),(0,a.AB)(1e3));(await (0,a.getDocs)(e)).forEach(e=>{let r=e.data(),a=r[o.Yr.date]?.toDate();if(a&&a>=t){let e=Number(r[o.Yr.amount])||0;g+=e}})}catch(e){console.warn("Could not fetch today's transactions:",e)}let m=(0,a.P)((0,a.collection)(s.db,o.COLLECTIONS.withdrawals),(0,a._M)("status","==","pending")),x=(await (0,a.getDocs)(m)).size,D=(0,a.P)((0,a.collection)(s.db,o.COLLECTIONS.withdrawals),(0,a._M)("date",">=",r)),h=(await (0,a.getDocs)(D)).size,f={totalUsers:Number(l)||0,totalTranslations:Number(u)||0,totalEarnings:Number(p)||0,pendingWithdrawals:Number(x)||0,todayUsers:Number(d)||0,todayTranslations:Number(y)||0,todayEarnings:Number(g)||0,todayWithdrawals:Number(h)||0};return i.set(e,{data:f,timestamp:Date.now()}),f}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(e=50,t=null){try{let r=(0,a.P)((0,a.collection)(s.db,o.COLLECTIONS.users),(0,a.My)(o.Yr.joinedDate,"desc"),(0,a.AB)(e));t&&(r=(0,a.P)((0,a.collection)(s.db,o.COLLECTIONS.users),(0,a.My)(o.Yr.joinedDate,"desc"),(0,a.HM)(t),(0,a.AB)(e)));let i=await (0,a.getDocs)(r);return{users:i.docs.map(e=>{let t=e.data();return{id:e.id,...t,joinedDate:t[o.Yr.joinedDate]?.toDate(),planExpiry:t[o.Yr.planExpiry]?.toDate(),quickTranslationAdvantageExpiry:t.quickTranslationAdvantageExpiry?.toDate(),quickVideoAdvantageExpiry:t.quickVideoAdvantageExpiry?.toDate(),lastTranslationDate:t.lastTranslationDate?.toDate(),lastVideoDate:t.lastVideoDate?.toDate(),lastCopyPasteReduction:t.lastCopyPasteReduction?.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function c(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),r=(0,a.P)((0,a.collection)(s.db,o.COLLECTIONS.users),(0,a.My)(o.Yr.joinedDate,"desc"));return(await (0,a.getDocs)(r)).docs.map(e=>{let t=e.data();return{id:e.id,...t,joinedDate:t[o.Yr.joinedDate]?.toDate(),planExpiry:t[o.Yr.planExpiry]?.toDate(),quickTranslationAdvantageExpiry:t.quickTranslationAdvantageExpiry?.toDate(),quickVideoAdvantageExpiry:t.quickVideoAdvantageExpiry?.toDate(),lastTranslationDate:t.lastTranslationDate?.toDate(),lastVideoDate:t.lastVideoDate?.toDate(),lastCopyPasteReduction:t.lastCopyPasteReduction?.toDate()}}).filter(e=>{let r=String(e[o.Yr.name]||"").toLowerCase(),a=String(e[o.Yr.email]||"").toLowerCase(),s=String(e[o.Yr.mobile]||"").toLowerCase(),i=String(e[o.Yr.referralCode]||"").toLowerCase();return r.includes(t)||a.includes(t)||s.includes(t)||i.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function d(){try{let e=(0,a.P)((0,a.collection)(s.db,o.COLLECTIONS.users),(0,a.My)(o.Yr.joinedDate,"desc"));return(await (0,a.getDocs)(e)).docs.map(e=>{let t=e.data();return{id:e.id,...t,joinedDate:t[o.Yr.joinedDate]?.toDate(),planExpiry:t[o.Yr.planExpiry]?.toDate(),quickTranslationAdvantageExpiry:t.quickTranslationAdvantageExpiry?.toDate(),quickVideoAdvantageExpiry:t.quickVideoAdvantageExpiry?.toDate(),lastTranslationDate:t.lastTranslationDate?.toDate(),lastVideoDate:t.lastVideoDate?.toDate(),lastCopyPasteReduction:t.lastCopyPasteReduction?.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,a.P)((0,a.collection)(s.db,o.COLLECTIONS.users));return(await (0,a.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function p(e=50,t=null){try{let r=(0,a.P)((0,a.collection)(s.db,o.COLLECTIONS.withdrawals),(0,a.My)("date","desc"),(0,a.AB)(e));t&&(r=(0,a.P)((0,a.collection)(s.db,o.COLLECTIONS.withdrawals),(0,a.My)("date","desc"),(0,a.HM)(t),(0,a.AB)(e)));let i=await (0,a.getDocs)(r);return{withdrawals:i.docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()})),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function y(e,t){try{await (0,a.mZ)((0,a.H9)(s.db,o.COLLECTIONS.users,e),t),i.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function g(e){try{await (0,a.kd)((0,a.H9)(s.db,o.COLLECTIONS.users,e)),i.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function m(e,t,n){try{let l=await (0,a.x7)((0,a.H9)(s.db,o.COLLECTIONS.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:c,amount:d,status:u}=l.data(),p={status:t,updatedAt:a.Dc.now()};if(n&&(p.adminNotes=n),await (0,a.mZ)((0,a.H9)(s.db,o.COLLECTIONS.withdrawals,e),p),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(r.bind(r,3582));await e(c,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${d} processed for transfer`})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(r.bind(r,3582));await e(c,d),await t(c,{type:"withdrawal_rejected",amount:d,description:`Withdrawal rejected - ₹${d} credited back to wallet`})}i.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}async function x(){try{let e=await d();return await Promise.all(e.map(async e=>{let t=await (0,o.$T)(e.referralCode);return{...e,referralCount:t}}))}catch(e){throw console.error("Error getting all users with referral counts:",e),e}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4573,6803,3582],()=>r(19119));module.exports=a})();