"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2439],{2439:(e,a,t)=>{t.d(a,{ActiveDaysService:()=>i,S3:()=>y,i7:()=>n,isUserPlanExpired:()=>D,mH:()=>d,nd:()=>u,updateUserActiveDays:()=>l});var r=t(6104),c=t(5317);let s={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},o={users:"users"};class i{static async calculateActiveDays(e){try{var a,t;let s,n=await (0,c.x7)((0,c.H9)(r.db,o.users,e));if(!n.exists())return console.error("User ".concat(e," not found")),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let l=n.data(),d=(null==(a=l.joinedDate)?void 0:a.toDate())||new Date,u=null==(t=l.lastActiveDayUpdate)?void 0:t.toDate(),y=l.activeDays||0,D=l.plan||"Trial",p=new Date,v=p.toDateString(),g=u?u.toDateString():null;if(console.log("\uD83D\uDCC5 Calculating active days for user ".concat(e,":")),console.log("   - Joined: ".concat(d.toDateString())),console.log("   - Current active days: ".concat(y)),console.log("   - Last update: ".concat(g||"Never")),console.log("   - Today: ".concat(v)),console.log("   - Plan: ".concat(D)),console.log("   - Is new day: ".concat(g!==v)),g===v)return console.log("✅ Already updated today for user ".concat(e)),{activeDays:y,shouldUpdate:!1,isNewDay:!1};if("Admin"===D)return console.log("⏭️ Skipping active days increment for admin user ".concat(e)),await i.updateLastActiveDayUpdate(e),{activeDays:y,shouldUpdate:!1,isNewDay:!0};if(await i.isUserOnLeaveToday(e))return console.log("\uD83C\uDFD6️ User ".concat(e," is on leave today, not incrementing active days")),await i.updateLastActiveDayUpdate(e),{activeDays:y,shouldUpdate:!1,isNewDay:!0};return s="Trial"===D?Math.floor((p.getTime()-d.getTime())/864e5)+1:y+1,console.log("\uD83D\uDCC8 New active days calculated: ".concat(y," → ").concat(s)),{activeDays:s,shouldUpdate:s!==y,isNewDay:!0}}catch(a){return console.error("Error calculating active days for user ".concat(e,":"),a),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(e){try{let a=await i.calculateActiveDays(e);if(a.shouldUpdate){let t=(0,c.H9)(r.db,o.users,e);await (0,c.mZ)(t,{[s.activeDays]:a.activeDays,[s.lastActiveDayUpdate]:c.Dc.now()}),console.log("✅ Updated active days for user ".concat(e,": ").concat(a.activeDays))}else a.isNewDay&&await i.updateLastActiveDayUpdate(e);return a.activeDays}catch(a){throw console.error("Error updating active days for user ".concat(e,":"),a),a}}static async updateLastActiveDayUpdate(e){try{let a=(0,c.H9)(r.db,o.users,e);await (0,c.mZ)(a,{[s.lastActiveDayUpdate]:c.Dc.now()})}catch(a){console.error("Error updating last active day timestamp for user ".concat(e,":"),a)}}static async isUserOnLeaveToday(e){try{let{isUserOnLeave:a}=await t.e(9567).then(t.bind(t,9567));return await a(e,new Date)}catch(a){return console.error("Error checking leave status for user ".concat(e,":"),a),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let e=await (0,c.getDocs)((0,c.collection)(r.db,o.users)),a=0,t=0,s=0;for(let r of e.docs)try{a++;let e=await i.calculateActiveDays(r.id);(e.shouldUpdate||e.isNewDay)&&(await i.updateUserActiveDays(r.id),e.shouldUpdate&&t++)}catch(e){s++,console.error("Error processing active days for user ".concat(r.id,":"),e)}return console.log("✅ Daily active days processing complete:"),console.log("   - Processed: ".concat(a," users")),console.log("   - Updated: ".concat(t," users")),console.log("   - Errors: ".concat(s," users")),{processed:a,updated:t,errors:s}}catch(e){throw console.error("Error in daily active days processing:",e),e}}static async getUserActiveDays(e){try{let a=await (0,c.x7)((0,c.H9)(r.db,o.users,e));if(!a.exists())return 0;return a.data().activeDays||0}catch(a){return console.error("Error getting active days for user ".concat(e,":"),a),0}}static async initializeActiveDaysForNewUser(e){try{let a=(0,c.H9)(r.db,o.users,e);await (0,c.mZ)(a,{[s.activeDays]:1,[s.lastActiveDayUpdate]:c.Dc.now()}),console.log("✅ Initialized active days for new user ".concat(e,": Day 1"))}catch(a){throw console.error("Error initializing active days for user ".concat(e,":"),a),a}}static async getActiveDaysDisplay(e){try{let a,t=await (0,c.x7)((0,c.H9)(r.db,o.users,e));if(!t.exists())return{current:0,total:2,displayText:"0/2"};let s=t.data(),i=s.plan||"Trial",n=s.activeDays||0;return a="Trial"===i?2:30,{current:n,total:a,displayText:"".concat(n,"/").concat(a)}}catch(a){return console.error("Error getting active days display for user ".concat(e,":"),a),{current:0,total:2,displayText:"0/2"}}}static async isUserPlanExpired(e){try{let a=await (0,c.x7)((0,c.H9)(r.db,o.users,e));if(!a.exists())return{expired:!0,reason:"User not found"};let t=a.data(),s=t.plan||"Trial",i=t.activeDays||0,n=t.planExpiry;if(console.log("\uD83D\uDCC5 Checking plan expiry for user ".concat(e,":"),{plan:s,activeDays:i,hasPlanExpiry:!!n,planExpiryDate:n?n.toDate():null}),"Admin"===s){let a={expired:!1,activeDays:i};return console.log("\uD83D\uDCC5 Plan expiry result for admin user ".concat(e,":"),a),a}if("Trial"===s){let a=Math.max(0,2-i),t={expired:a<=0,reason:a<=0?"Trial period expired":void 0,daysLeft:a,activeDays:i};return console.log("\uD83D\uDCC5 Plan expiry result for trial user ".concat(e,":"),t),t}if(n){let a=new Date,t=n.toDate(),r=a>t,c=r?0:Math.ceil((t.getTime()-a.getTime())/864e5),s={expired:r,reason:r?"Plan subscription expired":void 0,daysLeft:c,activeDays:i};return console.log("\uD83D\uDCC5 Plan expiry result for user ".concat(e," (using planExpiry field):"),s),s}let l=Math.max(0,30-i),d=i>30,u={expired:d,reason:d?"Plan expired - You have used ".concat(i," days out of 30 allowed days"):void 0,daysLeft:l,activeDays:i};return console.log("\uD83D\uDCC5 Plan expiry result for user ".concat(e,":"),u),u}catch(a){return console.error("Error checking plan expiry for user ".concat(e,":"),a),{expired:!0,reason:"Error checking plan status"}}}static async setPlanExpiryForUser(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30;try{var t;let s=(0,c.H9)(r.db,o.users,e),i=await (0,c.x7)(s);if(!i.exists())return console.error("User ".concat(e," not found")),!1;let n=i.data(),l=n.plan||"Trial";if("Trial"===l||"Admin"===l)return console.log("Skipping plan expiry setup for ".concat(l," user: ").concat(e)),!1;if(n.planExpiry)return console.log("User ".concat(e," already has plan expiry set: ").concat(n.planExpiry.toDate())),!1;let d=(null==(t=n.joinedDate)?void 0:t.toDate())||new Date,u=new Date(d);return u.setDate(u.getDate()+a),await (0,c.mZ)(s,{planExpiry:c.Dc.fromDate(u),planExpirySetDate:c.Dc.now()}),console.log("✅ Set plan expiry for user ".concat(e,": ").concat(u)),!0}catch(a){return console.error("Error setting plan expiry for user ".concat(e,":"),a),!1}}static async forceUpdateActiveDays(e,a,t){try{let i=(0,c.H9)(r.db,o.users,e);await (0,c.mZ)(i,{[s.activeDays]:a,[s.lastActiveDayUpdate]:c.Dc.now()}),console.log("\uD83D\uDD27 Admin ".concat(t," force updated active days for user ").concat(e,": ").concat(a))}catch(a){throw console.error("Error force updating active days for user ".concat(e,":"),a),a}}static async getActiveDaysStatistics(){try{let a=await (0,c.getDocs)((0,c.collection)(r.db,o.users)),t=0,s=0,i=0,n=0,l=0,d=0,u=new Date().toDateString();for(let r of a.docs){var e;let a=r.data(),c=a.plan||"Trial",o=a.activeDays||0,y=null==(e=a.lastActiveDayUpdate)?void 0:e.toDate();t++,l+=o,"Trial"===c?s++:"Admin"===c?n++:i++,y&&y.toDateString()===u&&d++}return{totalUsers:t,trialUsers:s,paidUsers:i,adminUsers:n,averageActiveDays:t>0?Math.round(l/t*100)/100:0,usersUpdatedToday:d}}catch(e){throw console.error("Error getting active days statistics:",e),e}}}let n=i.calculateActiveDays,l=i.updateUserActiveDays,d=i.processAllUsersActiveDays,u=i.getUserActiveDays,y=i.initializeActiveDaysForNewUser;i.getActiveDaysDisplay;let D=i.isUserPlanExpired;i.forceUpdateActiveDays,i.getActiveDaysStatistics}}]);